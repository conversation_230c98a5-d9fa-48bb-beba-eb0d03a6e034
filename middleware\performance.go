package middleware

import (
	"strconv"
	"strings"
	"time"
	"wechatdll/comm"

	"github.com/astaxie/beego/context"
	log "github.com/sirupsen/logrus"
)

// PerformanceMiddleware 性能监控中间件
func PerformanceMiddleware(ctx *context.Context) {
	startTime := time.Now()

	// 在请求完成后记录性能指标
	defer func() {
		duration := time.Since(startTime)

		// 记录到性能监控器
		perfMonitor := comm.GetPerformanceMonitor()
		perfMonitor.RecordRequestMetric(ctx.Request.URL.Path, duration, ctx.Output.Status < 400)

		// 记录慢请求
		if duration > 1*time.Second {
			log.WithFields(log.Fields{
				"path":     ctx.Request.URL.Path,
				"method":   ctx.Request.Method,
				"duration": duration,
				"status":   ctx.Output.Status,
				"ip":       ctx.Input.IP(),
			}).Warn("慢请求检测")
		}

		// 添加性能头信息
		ctx.Output.Header("X-Response-Time", strconv.FormatInt(duration.Nanoseconds()/1000000, 10)+"ms")
	}()
}

// ResponseCacheMiddleware 响应缓存中间件（重命名避免冲突）
func ResponseCacheMiddleware(ctx *context.Context) {
	// 只对GET请求启用缓存
	if ctx.Request.Method != "GET" {
		return
	}

	// 跳过某些不应该缓存的路径
	if shouldSkipCache(ctx.Request.URL.Path) {
		return
	}

	cache := comm.GetResponseCache()

	// 尝试从缓存获取响应
	if cachedData, found := cache.Get(ctx.Request.Method, ctx.Request.URL.Path, ctx.Request.URL.Query()); found {
		ctx.Output.Header("X-Cache", "HIT")
		ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
		ctx.Output.JSON(cachedData, false, false)
		return
	}

	// 标记缓存未命中
	ctx.Output.Header("X-Cache", "MISS")
}

// shouldSkipCache 判断是否应该跳过缓存
func shouldSkipCache(path string) bool {
	skipPaths := []string{
		"/api/Login/",
		"/api/Monitor/",
		"/health",
		"/metrics",
	}

	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// OptimizedJSONMiddleware 优化的JSON响应中间件
func OptimizedJSONMiddleware(ctx *context.Context) {
	// 设置优化的响应头
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")

	// 根据请求类型设置不同的缓存策略
	if ctx.Request.Method == "GET" {
		// GET请求可以短时间缓存
		ctx.Output.Header("Cache-Control", "public, max-age=60")
	} else {
		// 其他请求不缓存
		ctx.Output.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		ctx.Output.Header("Pragma", "no-cache")
		ctx.Output.Header("Expires", "0")
	}

	// 添加CORS头（如果需要）
	ctx.Output.Header("Access-Control-Allow-Origin", "*")
	ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// ConnectionOptimizationMiddleware 连接优化中间件
func ConnectionOptimizationMiddleware(ctx *context.Context) {
	// 启用HTTP Keep-Alive
	ctx.Output.Header("Connection", "keep-alive")

	// 设置Keep-Alive参数
	ctx.Output.Header("Keep-Alive", "timeout=30, max=100")

	// 启用HTTP/2服务器推送（如果支持）
	// 注意：beego的ResponseWriter可能不支持HTTP/2推送
	// 这里只是示例代码
	_ = ctx.ResponseWriter // 避免未使用变量警告
}

// RequestSizeMiddleware 请求大小限制中间件
func RequestSizeMiddleware(maxSize int64) func(*context.Context) {
	return func(ctx *context.Context) {
		if ctx.Request.ContentLength > maxSize {
			ctx.Output.SetStatus(413) // Request Entity Too Large
			ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
			ctx.Output.JSON(map[string]interface{}{
				"code":    -1009,
				"success": false,
				"message": "请求体过大",
			}, false, false)
			return
		}
	}
}

// ETagMiddleware ETag缓存中间件
func ETagMiddleware(ctx *context.Context) {
	// 只对GET请求启用ETag
	if ctx.Request.Method != "GET" {
		return
	}

	// 检查If-None-Match头
	ifNoneMatch := ctx.Request.Header.Get("If-None-Match")
	if ifNoneMatch != "" {
		// 这里应该根据实际内容生成ETag
		// 简化实现，实际项目中需要根据响应内容生成
		currentETag := generateETag(ctx.Request.URL.Path)

		if ifNoneMatch == currentETag {
			ctx.Output.SetStatus(304) // Not Modified
			return
		}

		ctx.Output.Header("ETag", currentETag)
	}
}

// generateETag 生成ETag（简化实现）
func generateETag(path string) string {
	// 实际实现中应该根据内容生成ETag
	// 这里使用路径和时间戳的简化版本
	return `"` + path + "-" + strconv.FormatInt(time.Now().Unix()/300, 10) + `"`
}

// PreflightMiddleware 预检请求处理中间件
func PreflightMiddleware(ctx *context.Context) {
	if ctx.Request.Method == "OPTIONS" {
		ctx.Output.Header("Access-Control-Allow-Origin", "*")
		ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		ctx.Output.Header("Access-Control-Max-Age", "86400") // 24小时
		ctx.Output.SetStatus(200)
		return
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware(ctx *context.Context) {
	// 添加安全相关的HTTP头
	ctx.Output.Header("X-Content-Type-Options", "nosniff")
	ctx.Output.Header("X-Frame-Options", "DENY")
	ctx.Output.Header("X-XSS-Protection", "1; mode=block")
	ctx.Output.Header("Referrer-Policy", "strict-origin-when-cross-origin")

	// 如果是HTTPS，添加HSTS头
	if ctx.Request.TLS != nil {
		ctx.Output.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware(ctx *context.Context) {
	requestID := ctx.Request.Header.Get("X-Request-ID")
	if requestID == "" {
		requestID = generatePerformanceRequestID()
	}

	// 设置响应头
	ctx.Output.Header("X-Request-ID", requestID)

	// 将请求ID存储到上下文中，供后续使用
	ctx.Input.SetData("request_id", requestID)
}

// generatePerformanceRequestID 生成性能监控请求ID
func generatePerformanceRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

// HealthCheckMiddleware 健康检查中间件
func HealthCheckMiddleware(ctx *context.Context) {
	if ctx.Request.URL.Path == "/health" {
		ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
		ctx.Output.JSON(map[string]interface{}{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"uptime":    time.Since(startTime).Seconds(),
		}, false, false)
		return
	}
}

// 服务启动时间
var startTime = time.Now()

// MetricsMiddleware 指标收集中间件
func MetricsMiddleware(ctx *context.Context) {
	if ctx.Request.URL.Path == "/metrics" {
		// 收集并返回性能指标
		perfMonitor := comm.GetPerformanceMonitor()
		httpPool := comm.GetHTTPPool()
		cache := comm.GetResponseCache()

		metrics := map[string]interface{}{
			"performance": perfMonitor.GetSystemMetrics(),
			"http_pool":   httpPool.GetStats(),
			"cache":       cache.GetStats(),
			"uptime":      time.Since(startTime).Seconds(),
			"timestamp":   time.Now().Unix(),
		}

		ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
		ctx.Output.JSON(metrics, false, false)
		return
	}
}
