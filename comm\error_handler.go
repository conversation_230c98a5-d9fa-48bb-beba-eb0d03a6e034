package comm

import (
	"fmt"
	"runtime"
	"strings"
	"time"

	log "github.com/sirupsen/logrus"
)

// BusinessErrorLevel 业务错误级别
type BusinessErrorLevel int

const (
	BusinessErrorLevelInfo BusinessErrorLevel = iota
	BusinessErrorLevelWarn
	BusinessErrorLevelError
	BusinessErrorLevelFatal
)

// BusinessError 业务错误
type BusinessError struct {
	Code      int64              `json:"code"`
	Message   string             `json:"message"`
	Details   string             `json:"details,omitempty"`
	Level     BusinessErrorLevel `json:"level"`
	Timestamp time.Time          `json:"timestamp"`
	Context   interface{}        `json:"context,omitempty"`
	Stack     string             `json:"stack,omitempty"`
}

// Error 实现error接口
func (e *BusinessError) Error() string {
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int64, message string) *BusinessError {
	return &BusinessError{
		Code:      code,
		Message:   message,
		Level:     BusinessErrorLevelError,
		Timestamp: time.Now(),
		Stack:     getStackTrace(2),
	}
}

// NewBusinessErrorWithDetails 创建带详情的业务错误
func NewBusinessErrorWithDetails(code int64, message, details string) *BusinessError {
	return &BusinessError{
		Code:      code,
		Message:   message,
		Details:   details,
		Level:     BusinessErrorLevelError,
		Timestamp: time.Now(),
		Stack:     getStackTrace(2),
	}
}

// NewBusinessErrorWithLevel 创建指定级别的业务错误
func NewBusinessErrorWithLevel(code int64, message string, level BusinessErrorLevel) *BusinessError {
	return &BusinessError{
		Code:      code,
		Message:   message,
		Level:     level,
		Timestamp: time.Now(),
		Stack:     getStackTrace(2),
	}
}

// WithContext 添加上下文信息
func (e *BusinessError) WithContext(ctx interface{}) *BusinessError {
	e.Context = ctx
	return e
}

// WithDetails 添加详细信息
func (e *BusinessError) WithDetails(details string) *BusinessError {
	e.Details = details
	return e
}

// getStackTrace 获取调用栈信息
func getStackTrace(skip int) string {
	var stack []string
	for i := skip; i < skip+10; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}

		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}

		// 只保留项目相关的调用栈
		if strings.Contains(file, "wechatdll") {
			stack = append(stack, fmt.Sprintf("%s:%d %s", file, line, fn.Name()))
		}

		if len(stack) >= 5 {
			break
		}
	}

	return strings.Join(stack, "\n")
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	perfMonitor *PerformanceMonitor
}

var globalErrorHandler *ErrorHandler

// GetErrorHandler 获取全局错误处理器
func GetErrorHandler() *ErrorHandler {
	if globalErrorHandler == nil {
		globalErrorHandler = &ErrorHandler{
			perfMonitor: GetPerformanceMonitor(),
		}
	}
	return globalErrorHandler
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(err error, context ...interface{}) {
	if err == nil {
		return
	}

	// 记录错误指标
	if eh.perfMonitor != nil {
		eh.perfMonitor.RecordMetric("error_count", 1, "count", map[string]string{
			"type": fmt.Sprintf("%T", err),
		})
	}

	// 根据错误类型进行不同处理
	switch e := err.(type) {
	case *BusinessError:
		eh.handleBusinessError(e, context...)
	default:
		eh.handleSystemError(err, context...)
	}
}

// handleBusinessError 处理业务错误
func (eh *ErrorHandler) handleBusinessError(err *BusinessError, context ...interface{}) {
	logFields := log.Fields{
		"error_code":  err.Code,
		"error_level": err.Level,
		"timestamp":   err.Timestamp,
	}

	if err.Details != "" {
		logFields["details"] = err.Details
	}

	if err.Context != nil {
		logFields["context"] = err.Context
	}

	if len(context) > 0 {
		logFields["additional_context"] = context
	}

	// 根据错误级别选择日志级别
	switch err.Level {
	case BusinessErrorLevelInfo:
		log.WithFields(logFields).Info(err.Message)
	case BusinessErrorLevelWarn:
		log.WithFields(logFields).Warn(err.Message)
	case BusinessErrorLevelError:
		log.WithFields(logFields).Error(err.Message)
	case BusinessErrorLevelFatal:
		log.WithFields(logFields).Fatal(err.Message)
	}
}

// handleSystemError 处理系统错误
func (eh *ErrorHandler) handleSystemError(err error, context ...interface{}) {
	logFields := log.Fields{
		"error_type": fmt.Sprintf("%T", err),
		"stack":      getStackTrace(3),
	}

	if len(context) > 0 {
		logFields["context"] = context
	}

	log.WithFields(logFields).Error(err.Error())
}

// RecoverFromPanic 从panic中恢复
func (eh *ErrorHandler) RecoverFromPanic(context ...interface{}) {
	if r := recover(); r != nil {
		// 记录panic指标
		if eh.perfMonitor != nil {
			eh.perfMonitor.RecordMetric("panic_count", 1, "count", map[string]string{
				"recovered": "true",
			})
		}

		logFields := log.Fields{
			"panic_value": r,
			"stack":       getStackTrace(3),
		}

		if len(context) > 0 {
			logFields["context"] = context
		}

		log.WithFields(logFields).Error("Panic recovered")

		// 可以在这里添加告警逻辑
	}
}

// WrapWithRecover 包装函数以自动恢复panic
func (eh *ErrorHandler) WrapWithRecover(fn func(), context ...interface{}) {
	defer eh.RecoverFromPanic(context...)
	fn()
}

// 预定义的业务错误
var (
	ErrInvalidParameter = NewBusinessError(-1001, "参数无效")
	ErrUnauthorized     = NewBusinessError(-1002, "未授权访问")
	ErrNotFound         = NewBusinessError(-1003, "资源不存在")
	ErrRateLimit        = NewBusinessError(-1004, "请求频率限制")
	ErrSystemError      = NewBusinessError(-1005, "系统错误")
	ErrLoginExpired     = NewBusinessError(-1006, "登录已过期")
	ErrOperationFailed  = NewBusinessError(-1007, "操作失败")
	ErrNetworkError     = NewBusinessError(-1008, "网络连接错误")
	ErrRedisError       = NewBusinessError(-1009, "Redis操作失败")
	ErrDatabaseError    = NewBusinessError(-1010, "数据库操作失败")
	ErrConfigError      = NewBusinessError(-1011, "配置错误")
	ErrValidationError  = NewBusinessError(-1012, "数据验证失败")
)

// 便捷函数
func HandleError(err error, context ...interface{}) {
	GetErrorHandler().HandleError(err, context...)
}

func RecoverFromPanic(context ...interface{}) {
	GetErrorHandler().RecoverFromPanic(context...)
}

func WrapWithRecover(fn func(), context ...interface{}) {
	GetErrorHandler().WrapWithRecover(fn, context...)
}

// IsBusinessError 检查是否为业务错误
func IsBusinessError(err error) bool {
	_, ok := err.(*BusinessError)
	return ok
}

// GetErrorCode 获取错误码
func GetErrorCode(err error) int64 {
	if bizErr, ok := err.(*BusinessError); ok {
		return bizErr.Code
	}
	return -1
}

// CreateValidationError 创建验证错误
func CreateValidationError(field, message string) *BusinessError {
	return NewBusinessErrorWithDetails(-1012, "数据验证失败",
		fmt.Sprintf("字段 %s: %s", field, message))
}

// CreateNetworkError 创建网络错误
func CreateNetworkError(operation string, err error) *BusinessError {
	return NewBusinessErrorWithDetails(-1008, "网络连接错误",
		fmt.Sprintf("操作 %s 失败: %v", operation, err))
}

// CreateRedisError 创建Redis错误
func CreateRedisError(operation string, err error) *BusinessError {
	return NewBusinessErrorWithDetails(-1009, "Redis操作失败",
		fmt.Sprintf("操作 %s 失败: %v", operation, err))
}
