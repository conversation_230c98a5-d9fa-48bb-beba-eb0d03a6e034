package comm

import (
	"net/http"
	"sync"
	"time"
	"wechatdll/models"

	log "github.com/sirupsen/logrus"
)

// HTTPClientPool HTTP客户端连接池
type HTTPClientPool struct {
	pool    sync.Pool
	timeout time.Duration
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	httpPool *HTTPClientPool
	mutex    sync.RWMutex
}

var (
	connManager *ConnectionManager
	connOnce    sync.Once
)

// GetConnectionManager 获取连接管理器实例
func GetConnectionManager() *ConnectionManager {
	connOnce.Do(func() {
		connManager = &ConnectionManager{
			httpPool: NewHTTPClientPool(30 * time.Second),
		}
	})
	return connManager
}

// NewHTTPClientPool 创建HTTP客户端池
func NewHTTPClientPool(timeout time.Duration) *HTTPClientPool {
	return &HTTPClientPool{
		timeout: timeout,
		pool: sync.Pool{
			New: func() interface{} {
				return &http.Client{
					Timeout: timeout,
					Transport: &http.Transport{
						MaxIdleConns:        100,
						MaxIdleConnsPerHost: 10,
						IdleConnTimeout:     90 * time.Second,
						DisableKeepAlives:   false, // 启用连接复用
					},
				}
			},
		},
	}
}

// GetClient 从池中获取HTTP客户端
func (p *HTTPClientPool) GetClient() *http.Client {
	return p.pool.Get().(*http.Client)
}

// PutClient 将HTTP客户端放回池中
func (p *HTTPClientPool) PutClient(client *http.Client) {
	p.pool.Put(client)
}

// GetHTTPClient 获取配置好的HTTP客户端
func (cm *ConnectionManager) GetHTTPClient(proxy models.ProxyInfo) (*http.Client, error) {
	client := cm.httpPool.GetClient()

	// 如果需要代理，重新配置Transport
	if proxy.ProxyIp != "" && proxy.ProxyIp != "string" {
		transport, err := Socks5Transport(proxy.ProxyIp, proxy.ProxyUser, proxy.ProxyPassword)
		if err != nil {
			log.Errorf("Failed to create proxy transport: %v", err)
			return nil, err
		}
		client.Transport = transport
	}

	return client, nil
}

// ReleaseHTTPClient 释放HTTP客户端
func (cm *ConnectionManager) ReleaseHTTPClient(client *http.Client) {
	cm.httpPool.PutClient(client)
}

// OptimizedHTTPRequest 优化的HTTP请求方法
func OptimizedHTTPRequest(url, method string, headers *map[string]string,
	body interface{}, ua string, proxy models.ProxyInfo) (*http.Response, error) {

	connMgr := GetConnectionManager()
	client, err := connMgr.GetHTTPClient(proxy)
	if err != nil {
		return nil, err
	}
	defer connMgr.ReleaseHTTPClient(client)

	// 构建请求
	req, err := buildRequest(url, method, body, ua, headers)
	if err != nil {
		return nil, err
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		log.Errorf("HTTP request failed: %v", err)
		return nil, err
	}

	return resp, nil
}

// buildRequest 构建HTTP请求
func buildRequest(url, method string, body interface{}, ua string, headers *map[string]string) (*http.Request, error) {
	// 这里可以根据body类型构建不同的请求体
	// 简化实现，实际使用时需要完善
	_ = body // 暂时忽略body参数，后续可以扩展
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}

	// 设置默认headers
	if ua == "" {
		ua = GenDefaultIpadUA()
	}
	req.Header.Set("User-Agent", ua)
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")

	// 设置自定义headers
	if headers != nil {
		for k, v := range *headers {
			req.Header.Set(k, v)
		}
	}

	return req, nil
}
