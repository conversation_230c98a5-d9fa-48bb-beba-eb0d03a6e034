/* CSS工具类 */

/* 间距工具类 */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 文本工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 显示工具类 */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Flexbox工具类 */
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* 位置工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-small); }
.rounded-md { border-radius: var(--radius-medium); }
.rounded-lg { border-radius: var(--radius-large); }
.rounded-full { border-radius: var(--radius-round); }

/* 阴影工具类 */
.shadow-none { box-shadow: none; }
.shadow-light { box-shadow: var(--shadow-light); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-heavy { box-shadow: var(--shadow-heavy); }

/* 边框工具类 */
.border-0 { border: 0; }
.border { border: 1px solid var(--border-color); }
.border-light { border: 1px solid var(--border-light); }
.border-primary { border: 1px solid var(--primary-color); }

/* 背景工具类 */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-hover { background-color: var(--bg-hover); }

/* 宽度工具类 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

/* 高度工具类 */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }

/* 溢出工具类 - 隐藏滚动条版本 */
.overflow-hidden { overflow: hidden; }
.overflow-auto {
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}
.overflow-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}
.overflow-scroll {
  overflow: scroll;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}
.overflow-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* 光标工具类 */
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

/* 选择工具类 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 过渡工具类 */
.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* 变换工具类 */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }

/* 透明度工具类 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 响应式显示工具类 */
@media (max-width: 768px) {
  .mobile-hidden { display: none; }
  .mobile-block { display: block; }
}

@media (min-width: 769px) {
  .desktop-hidden { display: none; }
  .desktop-block { display: block; }
}

/* 状态工具类 */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* 交互增强 */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.focus-ring:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

/* 截断文本 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 间隙工具类 */
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* 网格工具类 */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); }
