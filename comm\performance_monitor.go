package comm

import (
	"context"
	"runtime"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	mu       sync.RWMutex
	metrics  map[string]*Metric
	enabled  bool
	interval time.Duration
	ctx      context.Context
	cancel   context.CancelFunc
}

// Metric 性能指标
type Metric struct {
	Name      string    `json:"name"`
	Value     float64   `json:"value"`
	Unit      string    `json:"unit"`
	Timestamp time.Time `json:"timestamp"`
	Tags      map[string]string `json:"tags,omitempty"`
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPUUsage      float64 `json:"cpu_usage"`
	MemoryUsage   float64 `json:"memory_usage"`
	GoroutineNum  int     `json:"goroutine_num"`
	HeapSize      uint64  `json:"heap_size"`
	HeapUsed      uint64  `json:"heap_used"`
	GCPauseTime   float64 `json:"gc_pause_time"`
	RequestCount  int64   `json:"request_count"`
	ErrorCount    int64   `json:"error_count"`
	ResponseTime  float64 `json:"response_time"`
}

var (
	perfMonitor     *PerformanceMonitor
	perfMonitorOnce sync.Once
)

// GetPerformanceMonitor 获取性能监控器实例
func GetPerformanceMonitor() *PerformanceMonitor {
	perfMonitorOnce.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())
		perfMonitor = &PerformanceMonitor{
			metrics:  make(map[string]*Metric),
			enabled:  true,
			interval: 30 * time.Second, // 默认30秒收集一次
			ctx:      ctx,
			cancel:   cancel,
		}
		
		// 启动监控
		go perfMonitor.start()
	})
	return perfMonitor
}

// start 启动性能监控
func (pm *PerformanceMonitor) start() {
	if !pm.enabled {
		return
	}
	
	ticker := time.NewTicker(pm.interval)
	defer ticker.Stop()
	
	log.Info("性能监控器已启动")
	
	for {
		select {
		case <-pm.ctx.Done():
			log.Info("性能监控器已停止")
			return
		case <-ticker.C:
			pm.collectMetrics()
		}
	}
}

// collectMetrics 收集性能指标
func (pm *PerformanceMonitor) collectMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	now := time.Now()
	
	// 收集系统指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 内存使用率
	pm.metrics["memory_usage"] = &Metric{
		Name:      "memory_usage",
		Value:     float64(m.Alloc) / 1024 / 1024, // MB
		Unit:      "MB",
		Timestamp: now,
	}
	
	// 堆大小
	pm.metrics["heap_size"] = &Metric{
		Name:      "heap_size",
		Value:     float64(m.HeapSys) / 1024 / 1024, // MB
		Unit:      "MB",
		Timestamp: now,
	}
	
	// 堆使用量
	pm.metrics["heap_used"] = &Metric{
		Name:      "heap_used",
		Value:     float64(m.HeapInuse) / 1024 / 1024, // MB
		Unit:      "MB",
		Timestamp: now,
	}
	
	// Goroutine数量
	pm.metrics["goroutine_count"] = &Metric{
		Name:      "goroutine_count",
		Value:     float64(runtime.NumGoroutine()),
		Unit:      "count",
		Timestamp: now,
	}
	
	// GC暂停时间
	if m.NumGC > 0 {
		pm.metrics["gc_pause_time"] = &Metric{
			Name:      "gc_pause_time",
			Value:     float64(m.PauseNs[(m.NumGC+255)%256]) / 1000000, // ms
			Unit:      "ms",
			Timestamp: now,
		}
	}
	
	// GC次数
	pm.metrics["gc_count"] = &Metric{
		Name:      "gc_count",
		Value:     float64(m.NumGC),
		Unit:      "count",
		Timestamp: now,
	}
	
	// 检查Redis连接状态
	if RedisClient != nil {
		_, err := RedisClient.Ping().Result()
		redisStatus := 1.0
		if err != nil {
			redisStatus = 0.0
		}
		pm.metrics["redis_status"] = &Metric{
			Name:      "redis_status",
			Value:     redisStatus,
			Unit:      "status",
			Timestamp: now,
		}
	}
}

// RecordMetric 记录自定义指标
func (pm *PerformanceMonitor) RecordMetric(name string, value float64, unit string, tags map[string]string) {
	if !pm.enabled {
		return
	}
	
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.metrics[name] = &Metric{
		Name:      name,
		Value:     value,
		Unit:      unit,
		Timestamp: time.Now(),
		Tags:      tags,
	}
}

// GetMetrics 获取所有指标
func (pm *PerformanceMonitor) GetMetrics() map[string]*Metric {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	// 创建副本避免并发问题
	result := make(map[string]*Metric)
	for k, v := range pm.metrics {
		result[k] = &Metric{
			Name:      v.Name,
			Value:     v.Value,
			Unit:      v.Unit,
			Timestamp: v.Timestamp,
			Tags:      v.Tags,
		}
	}
	
	return result
}

// GetSystemMetrics 获取系统指标摘要
func (pm *PerformanceMonitor) GetSystemMetrics() *SystemMetrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	metrics := &SystemMetrics{}
	
	if m, ok := pm.metrics["memory_usage"]; ok {
		metrics.MemoryUsage = m.Value
	}
	
	if m, ok := pm.metrics["heap_size"]; ok {
		metrics.HeapSize = uint64(m.Value * 1024 * 1024) // 转换回字节
	}
	
	if m, ok := pm.metrics["heap_used"]; ok {
		metrics.HeapUsed = uint64(m.Value * 1024 * 1024) // 转换回字节
	}
	
	if m, ok := pm.metrics["goroutine_count"]; ok {
		metrics.GoroutineNum = int(m.Value)
	}
	
	if m, ok := pm.metrics["gc_pause_time"]; ok {
		metrics.GCPauseTime = m.Value
	}
	
	return metrics
}

// RecordRequestMetric 记录请求指标
func (pm *PerformanceMonitor) RecordRequestMetric(endpoint string, duration time.Duration, success bool) {
	if !pm.enabled {
		return
	}
	
	tags := map[string]string{
		"endpoint": endpoint,
		"status":   "success",
	}
	
	if !success {
		tags["status"] = "error"
	}
	
	pm.RecordMetric("request_duration", float64(duration.Milliseconds()), "ms", tags)
}

// Enable 启用性能监控
func (pm *PerformanceMonitor) Enable() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.enabled = true
	log.Info("性能监控已启用")
}

// Disable 禁用性能监控
func (pm *PerformanceMonitor) Disable() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.enabled = false
	log.Info("性能监控已禁用")
}

// Stop 停止性能监控
func (pm *PerformanceMonitor) Stop() {
	if pm.cancel != nil {
		pm.cancel()
	}
}

// SetInterval 设置收集间隔
func (pm *PerformanceMonitor) SetInterval(interval time.Duration) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.interval = interval
	log.Infof("性能监控收集间隔已设置为: %v", interval)
}

// GetHealthStatus 获取健康状态
func (pm *PerformanceMonitor) GetHealthStatus() map[string]interface{} {
	metrics := pm.GetSystemMetrics()
	
	status := map[string]interface{}{
		"healthy": true,
		"checks":  make(map[string]interface{}),
	}
	
	checks := status["checks"].(map[string]interface{})
	
	// 检查内存使用率
	if metrics.MemoryUsage > 1000 { // 超过1GB
		checks["memory"] = map[string]interface{}{
			"status":  "warning",
			"message": "内存使用率较高",
			"value":   metrics.MemoryUsage,
		}
	} else {
		checks["memory"] = map[string]interface{}{
			"status": "ok",
			"value":  metrics.MemoryUsage,
		}
	}
	
	// 检查Goroutine数量
	if metrics.GoroutineNum > 1000 {
		checks["goroutines"] = map[string]interface{}{
			"status":  "warning",
			"message": "Goroutine数量较多",
			"value":   metrics.GoroutineNum,
		}
		status["healthy"] = false
	} else {
		checks["goroutines"] = map[string]interface{}{
			"status": "ok",
			"value":  metrics.GoroutineNum,
		}
	}
	
	return status
}
