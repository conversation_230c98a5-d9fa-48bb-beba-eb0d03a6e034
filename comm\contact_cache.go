package comm

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"sync"
	"time"
	"wechatdll/models"

	log "github.com/sirupsen/logrus"
)

// ContactCacheItem 通讯录缓存项
type ContactCacheItem struct {
	Data      interface{} `json:"data"`
	ExpiresAt time.Time   `json:"expires_at"`
	CreatedAt time.Time   `json:"created_at"`
	HitCount  int64       `json:"hit_count"`
	Wxid      string      `json:"wxid"`
}

// IsExpired 检查缓存项是否过期（永不过期模式）
func (item *ContactCacheItem) IsExpired() bool {
	// 永不过期，只能手动删除
	return false
}

// ContactCache 通讯录缓存管理器
type ContactCache struct {
	cache     map[string]*ContactCacheItem
	mutex     sync.RWMutex
	maxSize   int
	hitCount  int64
	missCount int64

	// 清理相关
	cleanupInterval time.Duration
	stopCleanup     chan bool
}

// NewContactCache 创建通讯录缓存管理器
func NewContactCache(maxSize int, cleanupInterval time.Duration) *ContactCache {
	cc := &ContactCache{
		cache:           make(map[string]*ContactCacheItem),
		maxSize:         maxSize,
		cleanupInterval: cleanupInterval,
		stopCleanup:     make(chan bool),
	}

	// 启动清理协程
	go cc.startCleanup()

	return cc
}

// generateContactListKey 生成通讯录列表缓存键
func (cc *ContactCache) generateContactListKey(wxid string, currentWxcontactSeq, currentChatRoomContactSeq int32) string {
	keyData := fmt.Sprintf("contact_list:%s:%d:%d", wxid, currentWxcontactSeq, currentChatRoomContactSeq)
	hash := md5.Sum([]byte(keyData))
	return hex.EncodeToString(hash[:])
}

// generateContactDetailKey 生成通讯录详情缓存键
func (cc *ContactCache) generateContactDetailKey(wxid, towxids, chatRoom string) string {
	keyData := fmt.Sprintf("contact_detail:%s:%s:%s", wxid, towxids, chatRoom)
	hash := md5.Sum([]byte(keyData))
	return hex.EncodeToString(hash[:])
}

// GetContactList 获取通讯录列表缓存
func (cc *ContactCache) GetContactList(wxid string, currentWxcontactSeq, currentChatRoomContactSeq int32) (models.ResponseResult, bool) {
	key := cc.generateContactListKey(wxid, currentWxcontactSeq, currentChatRoomContactSeq)
	
	cc.mutex.RLock()
	item, exists := cc.cache[key]
	cc.mutex.RUnlock()

	if !exists {
		cc.mutex.Lock()
		cc.missCount++
		cc.mutex.Unlock()
		log.WithFields(log.Fields{
			"wxid": wxid,
			"type": "contact_list",
		}).Debug("缓存未命中")
		return models.ResponseResult{}, false
	}

	// 检查是否过期
	if item.IsExpired() {
		cc.mutex.Lock()
		delete(cc.cache, key)
		cc.missCount++
		cc.mutex.Unlock()
		log.WithFields(log.Fields{
			"wxid": wxid,
			"type": "contact_list",
		}).Debug("缓存已过期")
		return models.ResponseResult{}, false
	}

	// 更新命中次数
	cc.mutex.Lock()
	item.HitCount++
	cc.hitCount++
	cc.mutex.Unlock()

	log.WithFields(log.Fields{
		"wxid": wxid,
		"type": "contact_list",
		"hit_count": item.HitCount,
	}).Debug("缓存命中")

	if result, ok := item.Data.(models.ResponseResult); ok {
		return result, true
	}
	return models.ResponseResult{}, false
}

// SetContactList 设置通讯录列表缓存
func (cc *ContactCache) SetContactList(wxid string, currentWxcontactSeq, currentChatRoomContactSeq int32, data models.ResponseResult, ttl time.Duration) {
	key := cc.generateContactListKey(wxid, currentWxcontactSeq, currentChatRoomContactSeq)

	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	// 检查缓存大小限制（如果maxSize为0则无限制）
	if cc.maxSize > 0 && len(cc.cache) >= cc.maxSize {
		cc.evictLRU()
	}

	// 创建缓存项（永不过期）
	item := &ContactCacheItem{
		Data:      data,
		ExpiresAt: time.Time{}, // 零时间，表示永不过期
		CreatedAt: time.Now(),
		HitCount:  0,
		Wxid:      wxid,
	}

	cc.cache[key] = item

	log.WithFields(log.Fields{
		"wxid": wxid,
		"type": "contact_list",
		"ttl": ttl,
	}).Debug("缓存已设置")
}

// GetContactDetail 获取通讯录详情缓存
func (cc *ContactCache) GetContactDetail(wxid, towxids, chatRoom string) (models.ResponseResult, bool) {
	key := cc.generateContactDetailKey(wxid, towxids, chatRoom)
	
	cc.mutex.RLock()
	item, exists := cc.cache[key]
	cc.mutex.RUnlock()

	if !exists {
		cc.mutex.Lock()
		cc.missCount++
		cc.mutex.Unlock()
		log.WithFields(log.Fields{
			"wxid": wxid,
			"type": "contact_detail",
		}).Debug("缓存未命中")
		return models.ResponseResult{}, false
	}

	// 检查是否过期
	if item.IsExpired() {
		cc.mutex.Lock()
		delete(cc.cache, key)
		cc.missCount++
		cc.mutex.Unlock()
		log.WithFields(log.Fields{
			"wxid": wxid,
			"type": "contact_detail",
		}).Debug("缓存已过期")
		return models.ResponseResult{}, false
	}

	// 更新命中次数
	cc.mutex.Lock()
	item.HitCount++
	cc.hitCount++
	cc.mutex.Unlock()

	log.WithFields(log.Fields{
		"wxid": wxid,
		"type": "contact_detail",
		"hit_count": item.HitCount,
	}).Debug("缓存命中")

	if result, ok := item.Data.(models.ResponseResult); ok {
		return result, true
	}
	return models.ResponseResult{}, false
}

// SetContactDetail 设置通讯录详情缓存
func (cc *ContactCache) SetContactDetail(wxid, towxids, chatRoom string, data models.ResponseResult, ttl time.Duration) {
	key := cc.generateContactDetailKey(wxid, towxids, chatRoom)

	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	// 检查缓存大小限制（如果maxSize为0则无限制）
	if cc.maxSize > 0 && len(cc.cache) >= cc.maxSize {
		cc.evictLRU()
	}

	// 创建缓存项（永不过期）
	item := &ContactCacheItem{
		Data:      data,
		ExpiresAt: time.Time{}, // 零时间，表示永不过期
		CreatedAt: time.Now(),
		HitCount:  0,
		Wxid:      wxid,
	}

	cc.cache[key] = item

	log.WithFields(log.Fields{
		"wxid": wxid,
		"type": "contact_detail",
		"ttl": ttl,
	}).Debug("缓存已设置")
}

// ClearContactCache 清除指定微信号的所有通讯录缓存
func (cc *ContactCache) ClearContactCache(wxid string) int {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	cleared := 0
	for key, item := range cc.cache {
		if item.Wxid == wxid {
			delete(cc.cache, key)
			cleared++
		}
	}

	log.WithFields(log.Fields{
		"wxid": wxid,
		"cleared_count": cleared,
	}).Info("已清除通讯录缓存")

	return cleared
}

// evictLRU 使用LRU策略驱逐缓存项
func (cc *ContactCache) evictLRU() {
	if len(cc.cache) == 0 {
		return
	}

	// 找到最久未使用的缓存项
	var oldestKey string
	var oldestTime time.Time
	first := true

	for key, item := range cc.cache {
		if first || item.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.CreatedAt
			first = false
		}
	}

	if oldestKey != "" {
		delete(cc.cache, oldestKey)
		log.WithField("evicted_key", oldestKey).Debug("缓存项已驱逐")
	}
}

// startCleanup 启动清理协程
func (cc *ContactCache) startCleanup() {
	ticker := time.NewTicker(cc.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cc.cleanup()
		case <-cc.stopCleanup:
			return
		}
	}
}

// cleanup 清理过期缓存项
func (cc *ContactCache) cleanup() {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	expired := 0
	for key, item := range cc.cache {
		if item.IsExpired() {
			delete(cc.cache, key)
			expired++
		}
	}

	if expired > 0 {
		log.WithField("expired_count", expired).Debug("已清理过期缓存项")
	}
}

// GetStats 获取缓存统计信息
func (cc *ContactCache) GetStats() map[string]interface{} {
	cc.mutex.RLock()
	defer cc.mutex.RUnlock()

	total := cc.hitCount + cc.missCount
	hitRate := float64(0)
	if total > 0 {
		hitRate = float64(cc.hitCount) / float64(total) * 100
	}

	maxSizeDisplay := "无限制"
	if cc.maxSize > 0 {
		maxSizeDisplay = fmt.Sprintf("%d", cc.maxSize)
	}

	return map[string]interface{}{
		"cache_size":     len(cc.cache),
		"max_size":       maxSizeDisplay,
		"expiration":     "永不过期",
		"hit_count":      cc.hitCount,
		"miss_count":     cc.missCount,
		"hit_rate":       fmt.Sprintf("%.2f%%", hitRate),
		"total_requests": total,
	}
}

// Stop 停止缓存管理器
func (cc *ContactCache) Stop() {
	close(cc.stopCleanup)
}

// 默认通讯录缓存配置
var DefaultContactCacheConfig = struct {
	MaxSize         int
	CleanupInterval time.Duration
}{
	MaxSize:         0,  // 无限制缓存（设置为0表示无上限）
	CleanupInterval: 10 * time.Minute, // 每10分钟清理一次（实际上不会清理任何东西，因为永不过期）
}

// 全局通讯录缓存实例
var (
	globalContactCache *ContactCache
	contactCacheOnce   sync.Once
)

// GetContactCache 获取全局通讯录缓存实例
func GetContactCache() *ContactCache {
	contactCacheOnce.Do(func() {
		globalContactCache = NewContactCache(
			DefaultContactCacheConfig.MaxSize,
			DefaultContactCacheConfig.CleanupInterval,
		)
	})
	return globalContactCache
}
