package dynlib

import (
	"fmt"
)

// DynamicLibrary 是动态库的通用接口
type DynamicLibrary interface {
	Call(funcName string, args ...interface{}) (uintptr, uintptr, error)
	Close() error
}

// baseLibrary 是通用的动态库实现
type baseLibrary struct {
	handle uintptr
}

// defaultNewLibrary 是默认的fallback实现
func defaultNewLibrary(path string) (DynamicLibrary, error) {
	return nil, fmt.Errorf("动态库加载功能不可用 (平台不支持或CGO被禁用)")
}

// NewLibrary 根据平台加载动态库
func NewLibrary(path string) (DynamicLibrary, error) {
	fmt.Println(path)
	if newLibrary == nil {
		return defaultNewLibrary(path)
	}
	return newLibrary(path)
}

// newLibrary 是平台特定的实现，默认为nil，由各平台的init函数设置
var newLibrary func(path string) (DynamicLibrary, error)
