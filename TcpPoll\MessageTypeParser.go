// Package TcpPoll 消息类型解析器
//
// 本文件提供了完整的微信消息类型解析功能，包括：
// - 扩展的消息类型定义
// - 智能消息内容解析
// - 特殊消息类型处理
// - 未知消息类型的智能识别
package TcpPoll

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// 扩展消息类型常量
const (
	// 基础消息类型
	MsgTypeTextMessage     = 1  // 文本消息
	MsgTypeImageMessage    = 3  // 图片消息
	MsgTypeVoiceMessage    = 34 // 语音消息
	MsgTypeVideoMessage    = 43 // 视频消息
	MsgTypeCardMessage     = 42 // 名片消息
	MsgTypeEmojiMessage    = 47 // 表情消息
	MsgTypeLocationMessage = 48 // 位置消息
	MsgTypeLinkMessage     = 49 // 链接/小程序/文件
	MsgTypeReferMessage    = 49 // 引用消息
	MsgTypeStatusMessage   = 51 // 状态通知

	// 系统消息类型
	MsgTypeSystemMessage = 10000 // 系统消息
	MsgTypeSystemMsg     = 10002 // 系统消息(扩展)
	MsgTypeConfigUpdate  = 9999  // 配置更新

	// 特殊消息类型
	MsgTypePayment   = 2001  // 支付消息
	MsgTypeRedPacket = 1002  // 红包消息
	MsgTypeTransfer  = 2000  // 转账消息
	MsgTypeRecall    = 10002 // 撤回消息
)

// MessageTypeInfo 消息类型信息
type MessageTypeInfo struct {
	Type        uint32 `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
	IsSystem    bool   `json:"isSystem"`
}

// 完整的消息类型映射表
var CompleteMessageTypes = map[uint32]MessageTypeInfo{
	// 基础消息类型
	MsgTypeTextMessage: {
		Type: MsgTypeTextMessage, Name: "text", Description: "文本消息", Category: "basic", IsSystem: false,
	},
	MsgTypeImageMessage: {
		Type: MsgTypeImageMessage, Name: "image", Description: "图片消息", Category: "media", IsSystem: false,
	},
	MsgTypeVoiceMessage: {
		Type: MsgTypeVoiceMessage, Name: "voice", Description: "语音消息", Category: "media", IsSystem: false,
	},
	MsgTypeVideoMessage: {
		Type: MsgTypeVideoMessage, Name: "video", Description: "视频消息", Category: "media", IsSystem: false,
	},
	MsgTypeCardMessage: {
		Type: MsgTypeCardMessage, Name: "card", Description: "名片消息", Category: "contact", IsSystem: false,
	},
	MsgTypeEmojiMessage: {
		Type: MsgTypeEmojiMessage, Name: "emoji", Description: "表情消息", Category: "media", IsSystem: false,
	},
	MsgTypeLocationMessage: {
		Type: MsgTypeLocationMessage, Name: "location", Description: "位置消息", Category: "location", IsSystem: false,
	},
	MsgTypeLinkMessage: {
		Type: MsgTypeLinkMessage, Name: "link", Description: "链接/小程序/文件", Category: "rich", IsSystem: false,
	},
	MsgTypeStatusMessage: {
		Type: MsgTypeStatusMessage, Name: "status", Description: "状态通知", Category: "system", IsSystem: true,
	},

	// 系统消息类型
	MsgTypeSystemMessage: {
		Type: MsgTypeSystemMessage, Name: "system", Description: "系统消息", Category: "system", IsSystem: true,
	},
	MsgTypeConfigUpdate: {
		Type: MsgTypeConfigUpdate, Name: "config_update", Description: "配置更新", Category: "system", IsSystem: true,
	},

	// 特殊消息类型
	MsgTypePayment: {
		Type: MsgTypePayment, Name: "payment", Description: "支付消息", Category: "payment", IsSystem: false,
	},
	MsgTypeRedPacket: {
		Type: MsgTypeRedPacket, Name: "red_packet", Description: "红包消息", Category: "payment", IsSystem: false,
	},
	MsgTypeTransfer: {
		Type: MsgTypeTransfer, Name: "transfer", Description: "转账消息", Category: "payment", IsSystem: false,
	},
	MsgTypeRecall: {
		Type: MsgTypeRecall, Name: "recall", Description: "撤回消息", Category: "system", IsSystem: true,
	},
}

// ParsedMessage 解析后的消息结构
type ParsedMessage struct {
	Content     string                 `json:"content"`
	ContentType string                 `json:"contentType"`
	MsgType     uint32                 `json:"msgType"`
	MsgTypeDesc string                 `json:"msgTypeDesc"`
	Category    string                 `json:"category"`
	IsSystem    bool                   `json:"isSystem"`
	ExtraData   map[string]interface{} `json:"extraData,omitempty"`
	ParsedAt    int64                  `json:"parsedAt"`
}

// EnhancedMessageParser 增强的消息解析器
type EnhancedMessageParser struct {
	enableSmartParsing bool
	debugMode          bool
}

// NewEnhancedMessageParser 创建增强消息解析器
func NewEnhancedMessageParser(enableSmartParsing, debugMode bool) *EnhancedMessageParser {
	return &EnhancedMessageParser{
		enableSmartParsing: enableSmartParsing,
		debugMode:          debugMode,
	}
}

// ParseMessage 解析消息内容
func (emp *EnhancedMessageParser) ParseMessage(content string, msgType uint32) *ParsedMessage {
	parsed := &ParsedMessage{
		Content:     content,
		ContentType: "unknown",
		MsgType:     msgType,
		MsgTypeDesc: emp.GetMessageTypeDescription(msgType),
		Category:    "unknown",
		IsSystem:    false,
		ExtraData:   make(map[string]interface{}),
		ParsedAt:    time.Now().Unix(),
	}

	// 获取消息类型信息
	if typeInfo, exists := CompleteMessageTypes[msgType]; exists {
		parsed.ContentType = typeInfo.Name
		parsed.Category = typeInfo.Category
		parsed.IsSystem = typeInfo.IsSystem
	}

	// 根据消息类型进行具体解析
	switch msgType {
	case MsgTypeTextMessage:
		emp.parseTextMessage(parsed, content)
	case MsgTypeImageMessage:
		emp.parseImageMessage(parsed, content)
	case MsgTypeVoiceMessage:
		emp.parseVoiceMessage(parsed, content)
	case MsgTypeVideoMessage:
		emp.parseVideoMessage(parsed, content)
	case MsgTypeCardMessage:
		emp.parseCardMessage(parsed, content)
	case MsgTypeEmojiMessage:
		emp.parseEmojiMessage(parsed, content)
	case MsgTypeLocationMessage:
		emp.parseLocationMessage(parsed, content)
	case MsgTypeLinkMessage:
		emp.parseLinkMessage(parsed, content)
	case MsgTypeStatusMessage:
		emp.parseStatusMessage(parsed, content)
	case MsgTypeSystemMessage, MsgTypeSystemMsg:
		emp.parseSystemMessage(parsed, content)
	case MsgTypeConfigUpdate:
		emp.parseConfigUpdateMessage(parsed, content)
	default:
		if emp.enableSmartParsing {
			emp.smartParseUnknownMessage(parsed, content)
		}
	}

	return parsed
}

// GetMessageTypeDescription 获取消息类型描述
func (emp *EnhancedMessageParser) GetMessageTypeDescription(msgType uint32) string {
	if typeInfo, exists := CompleteMessageTypes[msgType]; exists {
		return typeInfo.Description
	}
	return fmt.Sprintf("未知类型(%d)", msgType)
}

// parseTextMessage 解析文本消息
func (emp *EnhancedMessageParser) parseTextMessage(parsed *ParsedMessage, content string) {
	// 检查是否为群聊消息格式 (wxid:\n消息内容)
	if groupInfo := emp.parseGroupMessage(content); groupInfo != nil {
		parsed.Content = groupInfo["actualContent"].(string)
		parsed.ExtraData["isGroupMessage"] = true
		parsed.ExtraData["senderWxid"] = groupInfo["senderWxid"]
		parsed.ExtraData["senderNickname"] = groupInfo["senderNickname"]
		parsed.ExtraData["originalContent"] = content
	} else {
		parsed.Content = content
	}

	parsed.ExtraData["length"] = len(parsed.Content)
	parsed.ExtraData["hasEmoji"] = containsEmoji(parsed.Content)
	parsed.ExtraData["hasURL"] = containsURL(parsed.Content)
}

// parseGroupMessage 解析群聊消息格式
func (emp *EnhancedMessageParser) parseGroupMessage(content string) map[string]interface{} {
	// 群聊消息格式: wxid:\n消息内容
	// 例如: "wxid_ntss24zwm7lc22:\n对面瑞纳骂队友"

	if !strings.Contains(content, ":\n") {
		return nil // 不是群聊消息格式
	}

	parts := strings.SplitN(content, ":\n", 2)
	if len(parts) != 2 {
		return nil // 格式不正确
	}

	senderWxid := strings.TrimSpace(parts[0])
	actualContent := parts[1]

	// 验证发送者wxid格式（基本验证）
	if senderWxid == "" || len(senderWxid) < 5 {
		return nil // wxid格式不正确
	}

	// 提取发送者昵称（如果可能）
	senderNickname := extractNicknameFromWxid(senderWxid)
	if senderNickname == "" {
		senderNickname = senderWxid // 如果无法提取昵称，使用wxid
	}

	return map[string]interface{}{
		"senderWxid":     senderWxid,
		"senderNickname": senderNickname,
		"actualContent":  actualContent,
	}
}

// parseImageMessage 解析图片消息
func (emp *EnhancedMessageParser) parseImageMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[图片]"

	if imageInfo := emp.extractImageInfo(content); imageInfo != nil {
		parsed.ExtraData = imageInfo
		if url, ok := imageInfo["url"].(string); ok && url != "" {
			parsed.ExtraData["hasImage"] = true
		}
	}
}

// parseVoiceMessage 解析语音消息
func (emp *EnhancedMessageParser) parseVoiceMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[语音]"

	if voiceInfo := emp.extractVoiceInfo(content); voiceInfo != nil {
		parsed.ExtraData = voiceInfo
		if duration, ok := voiceInfo["duration"].(string); ok {
			parsed.Content = fmt.Sprintf("[语音 %s秒]", duration)
		}
	}
}

// parseVideoMessage 解析视频消息
func (emp *EnhancedMessageParser) parseVideoMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[视频]"

	if videoInfo := emp.extractVideoInfo(content); videoInfo != nil {
		parsed.ExtraData = videoInfo
	}
}

// parseCardMessage 解析名片消息
func (emp *EnhancedMessageParser) parseCardMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[名片]"

	if cardInfo := emp.extractCardInfo(content); cardInfo != nil {
		parsed.ExtraData = cardInfo
		if nickname, ok := cardInfo["nickname"].(string); ok && nickname != "" {
			parsed.Content = fmt.Sprintf("[名片: %s]", nickname)
		}
	}
}

// parseEmojiMessage 解析表情消息
func (emp *EnhancedMessageParser) parseEmojiMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[表情]"

	if emojiInfo := emp.extractEmojiInfo(content); emojiInfo != nil {
		parsed.ExtraData = emojiInfo
	}
}

// parseLocationMessage 解析位置消息
func (emp *EnhancedMessageParser) parseLocationMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[位置]"

	if locationInfo := emp.extractLocationInfo(content); locationInfo != nil {
		parsed.ExtraData = locationInfo
		if label, ok := locationInfo["label"].(string); ok && label != "" {
			parsed.Content = fmt.Sprintf("[位置: %s]", label)
		}
	}
}

// parseLinkMessage 解析链接消息
func (emp *EnhancedMessageParser) parseLinkMessage(parsed *ParsedMessage, content string) {
	if linkInfo := emp.extractLinkInfo(content); linkInfo != nil {
		parsed.ExtraData = linkInfo

		// 根据链接类型设置内容
		if linkType, ok := linkInfo["type"].(string); ok {
			switch linkType {
			case "miniprogram":
				parsed.ContentType = "miniprogram"
				if title, ok := linkInfo["title"].(string); ok {
					parsed.Content = fmt.Sprintf("[小程序: %s]", title)
				} else {
					parsed.Content = "[小程序]"
				}
			case "file":
				parsed.ContentType = "file"
				if title, ok := linkInfo["title"].(string); ok {
					parsed.Content = fmt.Sprintf("[文件: %s]", title)
				} else {
					parsed.Content = "[文件]"
				}
			default:
				if title, ok := linkInfo["title"].(string); ok {
					parsed.Content = title
				} else {
					parsed.Content = "[链接]"
				}
			}
		}
	} else {
		parsed.Content = "[链接/文件]"
	}
}

// parseStatusMessage 解析状态消息
func (emp *EnhancedMessageParser) parseStatusMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[状态通知]"

	statusInfo := map[string]interface{}{
		"originalContent": content,
	}

	// 分析状态类型
	if strings.Contains(content, "进入了聊天") {
		statusInfo["action"] = "enter_chat"
		parsed.Content = "[用户进入聊天]"
	} else if strings.Contains(content, "退出了聊天") {
		statusInfo["action"] = "exit_chat"
		parsed.Content = "[用户退出聊天]"
	} else {
		statusInfo["action"] = "unknown"
	}

	parsed.ExtraData = statusInfo
}

// parseSystemMessage 解析系统消息
func (emp *EnhancedMessageParser) parseSystemMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[系统消息]"

	systemInfo := map[string]interface{}{
		"originalContent": content,
	}

	// 检查是否是业务推荐卡片
	if strings.Contains(content, "BizRecommendCard") {
		parsed.ContentType = "biz_recommend"
		parsed.Content = "[业务推荐]"
		systemInfo["subType"] = "biz_recommend"

		if cardInfo := emp.extractBizRecommendCard(content); cardInfo != nil {
			systemInfo["cardInfo"] = cardInfo
		}
	} else if strings.Contains(content, "dynacfg") {
		parsed.ContentType = "dynamic_config"
		parsed.Content = "[动态配置]"
		systemInfo["subType"] = "dynamic_config"

		if configInfo := emp.extractDynamicConfig(content); configInfo != nil {
			systemInfo["configInfo"] = configInfo
		}
	} else {
		systemInfo["subType"] = "unknown"
	}

	parsed.ExtraData = systemInfo
}

// parseConfigUpdateMessage 解析配置更新消息
func (emp *EnhancedMessageParser) parseConfigUpdateMessage(parsed *ParsedMessage, content string) {
	parsed.Content = "[配置更新]"

	configInfo := map[string]interface{}{
		"originalContent": content,
	}

	// 解析新消息数量
	if newcount := extractXMLValue(content, "newcount"); newcount != "" {
		if count, err := strconv.Atoi(newcount); err == nil {
			configInfo["newCount"] = count
			parsed.Content = fmt.Sprintf("[新消息通知: %d条]", count)
		}
	}

	// 解析版本信息
	if version := extractXMLValue(content, "version"); version != "" {
		configInfo["version"] = version
	}

	parsed.ExtraData = configInfo
}

// smartParseUnknownMessage 智能解析未知消息类型
func (emp *EnhancedMessageParser) smartParseUnknownMessage(parsed *ParsedMessage, content string) {
	smartInfo := map[string]interface{}{
		"originalContent": content,
		"contentLength":   len(content),
		"hasXML":          strings.Contains(content, "<"),
		"hasJSON":         strings.Contains(content, "{"),
	}

	// 根据内容特征智能判断类型
	if strings.Contains(content, "<sysmsg") {
		parsed.ContentType = "system_xml"
		parsed.Content = "[系统XML消息]"
		smartInfo["detectedType"] = "system_xml"

		if xmlInfo := emp.parseSystemXMLMessage(content); xmlInfo != nil {
			smartInfo["xmlInfo"] = xmlInfo
			if desc, ok := xmlInfo["description"].(string); ok {
				parsed.Content = desc
			}
		}
	} else if strings.Contains(content, "<?xml") || strings.Contains(content, "<msg>") {
		parsed.ContentType = "xml_message"
		parsed.Content = "[XML消息]"
		smartInfo["detectedType"] = "xml_message"

		if xmlData := parseXMLToMap(content); xmlData != nil {
			smartInfo["xmlData"] = xmlData
		}
	} else if len(content) > 0 && content[0] == '{' {
		parsed.ContentType = "json_message"
		parsed.Content = "[JSON消息]"
		smartInfo["detectedType"] = "json_message"

		if jsonData := emp.parseJSONMessage(content); jsonData != nil {
			smartInfo["jsonData"] = jsonData
		}
	} else {
		smartInfo["detectedType"] = "unknown"
	}

	parsed.ExtraData = smartInfo
}

// 辅助解析函数

// extractImageInfo 提取图片信息
func (emp *EnhancedMessageParser) extractImageInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取图片URL
	if cdnUrl := extractXMLValue(content, "cdnthumburl"); cdnUrl != "" {
		result["thumbUrl"] = cdnUrl
	}
	if cdnUrl := extractXMLValue(content, "cdnmidimgurl"); cdnUrl != "" {
		result["midUrl"] = cdnUrl
	}
	if cdnUrl := extractXMLValue(content, "cdnbigimgurl"); cdnUrl != "" {
		result["bigUrl"] = cdnUrl
	}

	// 提取图片尺寸
	if width := extractXMLValue(content, "width"); width != "" {
		result["width"] = width
	}
	if height := extractXMLValue(content, "height"); height != "" {
		result["height"] = height
	}

	return result
}

// extractVoiceInfo 提取语音信息
func (emp *EnhancedMessageParser) extractVoiceInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取语音时长
	if duration := extractXMLValue(content, "voicelength"); duration != "" {
		result["duration"] = duration
	}

	// 提取客户端消息ID
	if clientMsgId := extractXMLValue(content, "clientmsgid"); clientMsgId != "" {
		result["clientMsgId"] = clientMsgId
	}

	return result
}

// extractVideoInfo 提取视频信息
func (emp *EnhancedMessageParser) extractVideoInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取视频时长
	if duration := extractXMLValue(content, "playlength"); duration != "" {
		result["duration"] = duration
	}

	// 提取缩略图
	if thumbUrl := extractXMLValue(content, "cdnthumburl"); thumbUrl != "" {
		result["thumbUrl"] = thumbUrl
	}

	return result
}

// extractCardInfo 提取名片信息
func (emp *EnhancedMessageParser) extractCardInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取用户名
	if username := extractXMLValue(content, "username"); username != "" {
		result["username"] = username
	}

	// 提取昵称
	if nickname := extractXMLValue(content, "nickname"); nickname != "" {
		result["nickname"] = nickname
	}

	// 提取别名
	if alias := extractXMLValue(content, "alias"); alias != "" {
		result["alias"] = alias
	}

	return result
}

// extractEmojiInfo 提取表情信息
func (emp *EnhancedMessageParser) extractEmojiInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取表情URL
	if cdnUrl := extractXMLValue(content, "cdnurl"); cdnUrl != "" {
		result["url"] = cdnUrl
	}

	// 提取表情MD5
	if md5 := extractXMLValue(content, "md5"); md5 != "" {
		result["md5"] = md5
	}

	return result
}

// extractLocationInfo 提取位置信息
func (emp *EnhancedMessageParser) extractLocationInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取位置标签
	if label := extractXMLValue(content, "label"); label != "" {
		result["label"] = label
	}

	// 提取经纬度
	if x := extractXMLValue(content, "location_x"); x != "" {
		result["longitude"] = x
	}
	if y := extractXMLValue(content, "location_y"); y != "" {
		result["latitude"] = y
	}

	// 提取地图缩放级别
	if scale := extractXMLValue(content, "scale"); scale != "" {
		result["scale"] = scale
	}

	return result
}

// extractLinkInfo 提取链接信息
func (emp *EnhancedMessageParser) extractLinkInfo(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{}

	// 提取标题
	if title := extractXMLValue(content, "title"); title != "" {
		result["title"] = title
	}

	// 提取描述
	if des := extractXMLValue(content, "des"); des != "" {
		result["description"] = des
	}

	// 提取URL
	if url := extractXMLValue(content, "url"); url != "" {
		result["url"] = url
	}

	// 提取缩略图
	if thumbUrl := extractXMLValue(content, "thumburl"); thumbUrl != "" {
		result["thumbUrl"] = thumbUrl
	}

	// 检查是否是小程序
	if appid := extractXMLValue(content, "appid"); appid != "" {
		result["appId"] = appid
		result["type"] = "miniprogram"

		// 提取小程序路径
		if pagepath := extractXMLValue(content, "pagepath"); pagepath != "" {
			result["pagePath"] = pagepath
		}
	} else if fileext := extractXMLValue(content, "fileext"); fileext != "" {
		// 文件类型
		result["fileExt"] = fileext
		result["type"] = "file"
	} else {
		// 普通链接
		result["type"] = "link"
	}

	return result
}

// extractBizRecommendCard 提取业务推荐卡片信息
func (emp *EnhancedMessageParser) extractBizRecommendCard(content string) map[string]interface{} {
	result := map[string]interface{}{}

	// 提取卡片名称
	if cardName := extractXMLValue(content, "CardName"); cardName != "" {
		result["cardName"] = cardName
	}

	// 提取卡片缓冲区数据
	if cardBuffer := extractXMLValue(content, "CardBuffer"); cardBuffer != "" {
		result["cardBuffer"] = cardBuffer

		// 尝试解析base64编码的数据
		if decoded, err := base64.StdEncoding.DecodeString(cardBuffer); err == nil {
			result["decodedSize"] = len(decoded)
			// 这里可以进一步解析解码后的数据
		}
	}

	return result
}

// extractDynamicConfig 提取动态配置信息
func (emp *EnhancedMessageParser) extractDynamicConfig(content string) map[string]interface{} {
	result := map[string]interface{}{}

	// 计算配置项数量
	itemCount := strings.Count(content, "<Item")
	if itemCount > 0 {
		result["itemCount"] = itemCount
	}

	// 提取一些关键配置项
	configItems := []string{}
	if strings.Contains(content, "AndroidDynamicConfigVer") {
		configItems = append(configItems, "AndroidDynamicConfigVer")
	}
	if strings.Contains(content, "ClientBenchmarkLevel") {
		configItems = append(configItems, "ClientBenchmarkLevel")
	}

	if len(configItems) > 0 {
		result["keyItems"] = configItems
	}

	return result
}

// parseSystemXMLMessage 解析系统XML消息
func (emp *EnhancedMessageParser) parseSystemXMLMessage(content string) map[string]interface{} {
	result := map[string]interface{}{}

	// 提取sysmsg类型
	if msgType := extractXMLAttribute(content, "sysmsg", "type"); msgType != "" {
		result["systemType"] = msgType

		switch msgType {
		case "BizRecommendCard":
			result["description"] = "[业务推荐卡片]"
		case "dynacfg":
			result["description"] = "[动态配置]"
		default:
			result["description"] = fmt.Sprintf("[系统消息: %s]", msgType)
		}
	}

	return result
}

// parseJSONMessage 解析JSON消息
func (emp *EnhancedMessageParser) parseJSONMessage(content string) map[string]interface{} {
	var jsonData interface{}
	if err := json.Unmarshal([]byte(content), &jsonData); err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	return map[string]interface{}{
		"data": jsonData,
	}
}

// 工具函数

// containsEmoji 检查文本是否包含emoji
func containsEmoji(text string) bool {
	// 简单的emoji检测
	emojiPattern := regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]`)
	return emojiPattern.MatchString(text)
}

// containsURL 检查文本是否包含URL
func containsURL(text string) bool {
	urlPattern := regexp.MustCompile(`https?://[^\s]+`)
	return urlPattern.MatchString(text)
}

// extractXMLAttribute 提取XML属性值
func extractXMLAttribute(content, element, attribute string) string {
	pattern := fmt.Sprintf(`<%s[^>]*%s="([^"]*)"`, element, attribute)
	re := regexp.MustCompile(pattern)
	if matches := re.FindStringSubmatch(content); len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// GetAllMessageTypes 获取所有支持的消息类型
func GetAllMessageTypes() map[uint32]MessageTypeInfo {
	return CompleteMessageTypes
}

// GetMessageTypesByCategory 按类别获取消息类型
func GetMessageTypesByCategory(category string) []MessageTypeInfo {
	var types []MessageTypeInfo
	for _, typeInfo := range CompleteMessageTypes {
		if typeInfo.Category == category {
			types = append(types, typeInfo)
		}
	}
	return types
}

// IsSystemMessage 判断是否为系统消息
func IsSystemMessage(msgType uint32) bool {
	if typeInfo, exists := CompleteMessageTypes[msgType]; exists {
		return typeInfo.IsSystem
	}
	return false
}
