package Friend

import (
	"fmt"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
	log "github.com/sirupsen/logrus"
)

func GetContractList(Data GetContractListparameter) models.ResponseResult {
	// 如果不是强制刷新，先尝试从缓存获取数据
	if !Data.ForceRefresh {
		cache := comm.GetContactCache()
		if cachedResult, found := cache.GetContactList(Data.Wxid, Data.CurrentWxcontactSeq, Data.CurrentChatRoomContactSeq); found {
			log.WithFields(log.Fields{
				"wxid": Data.Wxid,
				"type": "contact_list",
			}).Info("使用缓存数据返回通讯录列表")
			return cachedResult
		}
		log.WithFields(log.Fields{
			"wxid": Data.Wxid,
			"type": "contact_list",
		}).Info("缓存未命中，将请求微信服务器并缓存结果")
	} else {
		log.WithFields(log.Fields{
			"wxid": Data.Wxid,
			"type": "contact_list",
		}).Info("🔄 强制刷新缓存，跳过缓存检查")
	}

	D, err := comm.GetLoginata(Data.Wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		errorMsg := fmt.Sprintf("异常：%v [%v]", "未找到登录信息", Data.Wxid)
		if err != nil {
			errorMsg = fmt.Sprintf("异常：%v", err.Error())
		}
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: errorMsg,
			Data:    nil,
		}
	}

	req := &mm.InitContactRequest{
		Username:                  proto.String(Data.Wxid),
		CurrentWxcontactSeq:       proto.Int32(Data.CurrentWxcontactSeq),
		CurrentChatRoomContactSeq: proto.Int32(Data.CurrentChatRoomContactSeq),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Ip:     D.Mmtlsip,
		Host:   D.ShortHost,
		Cgiurl: "/cgi-bin/micromsg-bin/initcontact",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              851,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.RsaPublicKey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.InitContactResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}

	// 默认将结果保存到缓存
	cache := comm.GetContactCache()
	cache.SetContactList(Data.Wxid, Data.CurrentWxcontactSeq, Data.CurrentChatRoomContactSeq, result, 0) // TTL参数被忽略
	log.WithFields(log.Fields{
		"wxid": Data.Wxid,
		"type": "contact_list",
		"expires": "永不过期",
	}).Info("通讯录列表数据已缓存")

	return result
}
