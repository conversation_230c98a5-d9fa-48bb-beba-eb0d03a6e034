/* API详情模态框样式 */

/* API详情模态框 */
.api-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
  overflow: hidden; /* 防止模态框内容溢出 */
  box-sizing: border-box;
}

.api-modal.show {
  display: flex;
}

.api-modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  display: flex;
  flex-direction: column;
}

.api-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
}

.api-modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  margin: 0;
}

.api-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
  background: var(--bg-primary);
}

/* API详情内容 */
.api-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.api-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-medium);
  padding: 24px;
  border: 1px solid var(--border-light);
}

.api-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-light);
}

.api-info-grid {
  display: grid;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-item label {
  min-width: 80px;
  font-weight: 500;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.info-item code {
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-small);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  color: var(--text-primary);
}

/* URL容器样式 */
.url-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--bg-tertiary);
  padding: 8px 12px;
  border-radius: var(--radius-small);
  border: 1px solid var(--border-light);
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

.url-container:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-color);
}

.url-container .full-url {
  flex: 1;
  background: none;
  padding: 0;
  font-size: 13px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  color: var(--text-primary);
  word-break: break-all;
  line-height: 1.4;
}

.url-container .copy-icon {
  color: var(--text-secondary);
  font-size: 12px;
  transition: color 0.2s ease;
  flex-shrink: 0;
}

.url-container:hover .copy-icon {
  color: var(--primary-color);
}

/* 参数表格 */
.params-table {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 12px;
  background: var(--bg-primary);
  border-radius: var(--radius-small);
  border: 1px solid var(--border-light);
}

.param-info {
  flex: 1;
}

.param-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.param-type {
  font-size: 12px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-small);
  display: inline-block;
  margin-bottom: 4px;
}

.param-description {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.param-required {
  color: var(--error-color);
  font-size: 12px;
  font-weight: 600;
}

.param-input {
  width: 200px;
  flex-shrink: 0;
}

.param-input input {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
}

.param-input input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.param-input input::placeholder {
  color: var(--text-tertiary);
}

/* 请求体参数 */
.body-param-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.body-param label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.body-param textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  line-height: 1.4;
  resize: vertical;
}

.body-param textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.body-param textarea::placeholder {
  color: var(--text-tertiary);
}

.body-param small {
  display: block;
  margin-top: 4px;
  color: var(--text-tertiary);
  font-size: 12px;
}

/* 兼容性样式 - 保持向后兼容 */
.body-param-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  resize: vertical;
}

.body-param-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.body-param-textarea::placeholder {
  color: var(--text-tertiary);
}

.param-hint {
  margin-top: 4px;
  color: var(--text-tertiary);
  font-size: 12px;
}

/* 测试部分 */
.test-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-actions {
  display: flex;
  gap: 12px;
}

/* 响应部分 */
.response-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.response-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-light);
}

.response-tab {
  padding: 8px 16px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.response-tab:hover,
.response-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.response-content {
  min-height: 200px;
}

.response-panel {
  display: none;
}

.response-panel.active {
  display: block;
}

/* 响应容器 - 为悬浮按钮提供相对定位上下文 */
.response-container {
  position: relative;
}

.response-panel pre {
  background: var(--bg-tertiary);
  padding: 16px;
  border-radius: var(--radius-small);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  line-height: 1.4;
  color: var(--text-primary);
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  min-height: 150px;
}

/* 悬浮复制按钮 */
.copy-response-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  transform: translateY(-4px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 悬浮时显示按钮 */
.response-container:hover .copy-response-btn {
  opacity: 1;
  transform: translateY(0);
}

/* 按钮悬浮效果 */
.copy-response-btn:hover {
  background: rgba(24, 144, 255, 0.15);
  color: var(--primary-color);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 4px 16px rgba(24, 144, 255, 0.2),
    0 0 0 1px rgba(24, 144, 255, 0.3),
    0 0 20px rgba(24, 144, 255, 0.1);
}

/* 按钮点击效果 */
.copy-response-btn:active {
  transform: translateY(0) scale(0.95);
  transition: transform 0.1s ease;
}

/* 微光动画效果 */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(24, 144, 255, 0.2);
  }
  50% {
    box-shadow:
      0 4px 16px rgba(24, 144, 255, 0.3),
      0 0 0 1px rgba(24, 144, 255, 0.5),
      0 0 30px rgba(24, 144, 255, 0.2);
  }
}

/* 成功复制后的反馈动画 */
@keyframes copy-success {
  0% {
    transform: translateY(-2px) scale(1.05);
  }
  50% {
    transform: translateY(-4px) scale(1.1);
    background: rgba(82, 196, 26, 0.2);
    color: #52c41a;
  }
  100% {
    transform: translateY(-2px) scale(1.05);
  }
}

.copy-response-btn:hover {
  animation: glow-pulse 2s ease-in-out infinite;
}

.copy-response-btn.success {
  animation: copy-success 0.6s ease-out;
}

/* 暗色主题适配 */
[data-theme="dark"] .copy-response-btn {
  background: rgba(0, 0, 0, 0.3);
  color: var(--text-secondary);
}

[data-theme="dark"] .copy-response-btn:hover {
  background: rgba(24, 144, 255, 0.2);
  color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .copy-response-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
    top: 8px;
    right: 8px;
  }

  /* 移动设备上始终显示按钮，不需要悬浮触发 */
  .copy-response-btn {
    opacity: 0.7;
    transform: translateY(0);
  }

  .copy-response-btn:hover {
    opacity: 1;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .copy-response-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--border-color);
  }

  .copy-response-btn:hover {
    background: var(--primary-color);
    color: white;
  }
}

/* cURL命令头部样式 */
.curl-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-light);
}

.copy-curl-btn {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: var(--radius-small);
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.copy-curl-btn:hover {
  transform: translateY(-1px);
}

.copy-curl-btn i {
  font-size: 11px;
}

/* cURL面板特殊样式 */
#curl-panel {
  padding-top: 0;
}

#curl-panel pre {
  margin-top: 0;
  min-height: 120px;
}

/* 代码生成模态框样式 */
.code-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.code-modal {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
}

.code-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-secondary);
  border-radius: var(--radius-large) var(--radius-large) 0 0;
}

.code-modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.code-modal-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.code-modal-body {
  padding: 0;
  overflow: auto;
  flex: 1;
}

.code-modal-body pre {
  margin: 0;
  padding: 24px;
  background: var(--bg-code);
  color: var(--text-code);
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.6;
  overflow: auto;
  border-radius: 0 0 var(--radius-large) var(--radius-large);
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-modal-body code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* 代码高亮样式 */
.language-python .token.keyword {
  color: #ff7b72;
}

.language-python .token.string {
  color: #a5d6ff;
}

.language-python .token.comment {
  color: #8b949e;
  font-style: italic;
}

.language-python .token.number {
  color: #79c0ff;
}

.language-python .token.function {
  color: #d2a8ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .code-modal {
    width: 95%;
    max-height: 95vh;
  }

  .code-modal-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .code-modal-actions {
    justify-content: center;
  }

  .code-modal-body pre {
    padding: 16px;
    font-size: 12px;
  }
}

/* 语言选择模态框样式 */
.language-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.language-modal {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  width: 90%;
  max-width: 400px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.language-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-secondary);
}

.language-modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-small);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.language-modal-body {
  padding: 24px;
}

.language-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.language-option:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.language-option:active {
  transform: translateY(0);
}

.language-option i {
  font-size: 20px;
  color: var(--primary-color);
  width: 24px;
  text-align: center;
}

.language-option span {
  flex: 1;
}

/* Python图标颜色 */
.language-option .fa-python {
  color: #3776ab;
}

/* 易语言图标颜色 */
.language-option .fa-code {
  color: #ff6b35;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .language-modal {
    width: 95%;
    max-width: none;
  }

  .language-modal-header {
    padding: 16px 20px;
  }

  .language-modal-body {
    padding: 20px;
  }

  .language-option {
    padding: 14px 16px;
  }
}
