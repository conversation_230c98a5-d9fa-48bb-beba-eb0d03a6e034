package TcpPoll

import (
	"fmt"
	"time"
	"wechatdll/comm"
)

// ==================== 自动红包领取状态管理 ====================

var (
	redpacketRestoredStatus = make(map[string]bool)
	collectRestoredStatus   = make(map[string]bool)
	friendRestoredStatus    = make(map[string]bool)
)

// SetAutoRedPacketStatus 设置自动红包领取状态（支持Redis持久化）
func SetAutoRedPacketStatus(wxid string, enable bool) error {
	autoRedPacketMutex.Lock()
	defer autoRedPacketMutex.Unlock()

	// 更新内存状态
	autoRedPacketStatus[wxid] = enable

	// 持久化到Redis
	redisKey := fmt.Sprintf("auto_redpacket:%s", wxid)
	statusData := map[string]interface{}{
		"wxid":       wxid,
		"enable":     enable,
		"updated_at": time.Now().Unix(),
		"updated_by": "system",
	}

	// 使用Redis持久化存储（永不过期）
	if err := comm.SETExpirationObj(redisKey, statusData, 0); err != nil {
		fmt.Printf("[AUTO_REDPACKET_ERROR] Redis持久化失败: %v\n", err)
		// 即使Redis失败，也不影响内存状态的设置
	} else {
		fmt.Printf("[AUTO_REDPACKET] Redis持久化成功: %s\n", redisKey)
	}

	status := "关闭"
	if enable {
		status = "开启"
	}
	fmt.Printf("[AUTO_REDPACKET] %s 自动红包领取已%s\n", wxid, status)

	return nil
}

// GetAutoRedPacketStatus 获取自动红包领取状态（优先从内存，备用从Redis）
func GetAutoRedPacketStatus(wxid string) bool {
	autoRedPacketMutex.RLock()
	defer autoRedPacketMutex.RUnlock()

	// 优先从内存获取
	if status, exists := autoRedPacketStatus[wxid]; exists {
		return status
	}

	// 内存中不存在，尝试从Redis获取
	redisKey := fmt.Sprintf("auto_redpacket:%s", wxid)
	var statusData map[string]interface{}

	if err := comm.GETObj(redisKey, &statusData); err == nil {
		if enable, ok := statusData["enable"].(bool); ok {
			// 同步到内存中
			autoRedPacketStatus[wxid] = enable

			if !redpacketRestoredStatus[wxid] {
				fmt.Printf("[AUTO_REDPACKET] 从Redis恢复状态: %s = %v\n", wxid, enable)
				redpacketRestoredStatus[wxid] = true
			}

			return enable
		}
	}

	// 都获取不到，返回默认值
	return false
}

// GetAutoRedPacketStatusWithDetails 获取详细的自动红包领取状态信息
func GetAutoRedPacketStatusWithDetails(wxid string) map[string]interface{} {
	result := map[string]interface{}{
		"wxid":   wxid,
		"enable": GetAutoRedPacketStatus(wxid),
		"status": map[string]interface{}{
			"code":        0,
			"description": "",
		},
		"source": "memory",
	}

	// 尝试从Redis获取详细信息
	redisKey := fmt.Sprintf("auto_redpacket:%s", wxid)
	var statusData map[string]interface{}

	if err := comm.GETObj(redisKey, &statusData); err == nil {
		result["source"] = "redis"
		if updatedAt, ok := statusData["updated_at"]; ok {
			result["updated_at"] = updatedAt
		}
		if updatedBy, ok := statusData["updated_by"]; ok {
			result["updated_by"] = updatedBy
		}
	}

	// 设置状态描述
	if result["enable"].(bool) {
		result["status"] = map[string]interface{}{
			"code":        1,
			"description": "自动红包领取已开启",
		}
	} else {
		result["status"] = map[string]interface{}{
			"code":        0,
			"description": "自动红包领取已关闭",
		}
	}

	return result
}

// ==================== 自动收款确认状态管理 ====================

// SetAutoCollectMoneyStatus 设置自动收款确认状态（支持Redis持久化）
func SetAutoCollectMoneyStatus(wxid string, enable bool) error {
	autoCollectMoneyMutex.Lock()
	defer autoCollectMoneyMutex.Unlock()

	// 更新内存状态
	autoCollectMoneyStatus[wxid] = enable

	// 持久化到Redis
	redisKey := fmt.Sprintf("auto_collect_money:%s", wxid)
	statusData := map[string]interface{}{
		"wxid":       wxid,
		"enable":     enable,
		"updated_at": time.Now().Unix(),
		"updated_by": "system",
	}

	// 使用Redis持久化存储（永不过期）
	if err := comm.SETExpirationObj(redisKey, statusData, 0); err != nil {
		fmt.Printf("[AUTO_COLLECT_ERROR] Redis持久化失败: %v\n", err)
		// 即使Redis失败，也不影响内存状态的设置
	} else {
		fmt.Printf("[AUTO_COLLECT] Redis持久化成功: %s\n", redisKey)
	}

	status := "关闭"
	if enable {
		status = "开启"
	}
	fmt.Printf("[AUTO_COLLECT] %s 自动收款确认已%s\n", wxid, status)

	return nil
}

// GetAutoCollectMoneyStatus 获取自动收款确认状态（优先从内存，备用从Redis）
func GetAutoCollectMoneyStatus(wxid string) bool {
	autoCollectMoneyMutex.RLock()
	defer autoCollectMoneyMutex.RUnlock()

	// 优先从内存获取
	if status, exists := autoCollectMoneyStatus[wxid]; exists {
		return status
	}

	// 内存中不存在，尝试从Redis获取
	redisKey := fmt.Sprintf("auto_collect_money:%s", wxid)
	var statusData map[string]interface{}

	if err := comm.GETObj(redisKey, &statusData); err == nil {
		if enable, ok := statusData["enable"].(bool); ok {
			// 同步到内存中
			autoCollectMoneyStatus[wxid] = enable

			if !collectRestoredStatus[wxid] {
				fmt.Printf("[AUTO_COLLECT] 从Redis恢复状态: %s = %v\n", wxid, enable)
				collectRestoredStatus[wxid] = true
			}

			return enable
		}
	}

	// 都获取不到，返回默认值
	return false
}

// GetAutoCollectMoneyStatusWithDetails 获取详细的自动收款确认状态信息
func GetAutoCollectMoneyStatusWithDetails(wxid string) map[string]interface{} {
	result := map[string]interface{}{
		"wxid":   wxid,
		"enable": GetAutoCollectMoneyStatus(wxid),
		"status": map[string]interface{}{
			"code":        0,
			"description": "",
		},
		"source": "memory",
	}

	// 尝试从Redis获取详细信息
	redisKey := fmt.Sprintf("auto_collect_money:%s", wxid)
	var statusData map[string]interface{}

	if err := comm.GETObj(redisKey, &statusData); err == nil {
		result["source"] = "redis"
		if updatedAt, ok := statusData["updated_at"]; ok {
			result["updated_at"] = updatedAt
		}
		if updatedBy, ok := statusData["updated_by"]; ok {
			result["updated_by"] = updatedBy
		}
	}

	// 设置状态描述
	if result["enable"].(bool) {
		result["status"] = map[string]interface{}{
			"code":        1,
			"description": "自动收款确认已开启",
		}
	} else {
		result["status"] = map[string]interface{}{
			"code":        0,
			"description": "自动收款确认已关闭",
		}
	}

	return result
}

// ==================== 自动通过好友请求状态管理 ====================

// SetAutoAcceptFriendStatus 设置自动通过好友请求状态（支持Redis持久化）
func SetAutoAcceptFriendStatus(wxid string, enable bool) error {
	autoAcceptFriendMutex.Lock()
	defer autoAcceptFriendMutex.Unlock()

	// 更新内存状态
	autoAcceptFriendStatus[wxid] = enable

	// 持久化到Redis
	redisKey := fmt.Sprintf("auto_accept_friend:%s", wxid)
	statusData := map[string]interface{}{
		"wxid":       wxid,
		"enable":     enable,
		"updated_at": time.Now().Unix(),
		"updated_by": "system",
	}

	// 使用Redis持久化存储（永不过期）
	if err := comm.SETExpirationObj(redisKey, statusData, 0); err != nil {
		fmt.Printf("[AUTO_FRIEND_ERROR] Redis持久化失败: %v\n", err)
		// 即使Redis失败，也不影响内存状态的设置
	} else {
		fmt.Printf("[AUTO_FRIEND] Redis持久化成功: %s\n", redisKey)
	}

	status := "关闭"
	if enable {
		status = "开启"
	}
	fmt.Printf("[AUTO_FRIEND] %s 自动通过好友请求已%s\n", wxid, status)

	return nil
}

// GetAutoAcceptFriendStatus 获取自动通过好友请求状态（优先从内存，备用从Redis）
func GetAutoAcceptFriendStatus(wxid string) bool {
	autoAcceptFriendMutex.RLock()
	defer autoAcceptFriendMutex.RUnlock()

	// 优先从内存获取
	if status, exists := autoAcceptFriendStatus[wxid]; exists {
		return status
	}

	// 内存中不存在，尝试从Redis获取
	redisKey := fmt.Sprintf("auto_accept_friend:%s", wxid)
	var statusData map[string]interface{}

	if err := comm.GETObj(redisKey, &statusData); err == nil {
		if enable, ok := statusData["enable"].(bool); ok {
			// 同步到内存中
			autoAcceptFriendStatus[wxid] = enable

			if !friendRestoredStatus[wxid] {
				fmt.Printf("[AUTO_FRIEND] 从Redis恢复状态: %s = %v\n", wxid, enable)
				friendRestoredStatus[wxid] = true
			}

			return enable
		}
	}

	// 都获取不到，返回默认值
	return false
}

// GetAutoAcceptFriendStatusWithDetails 获取详细的自动通过好友请求状态信息
func GetAutoAcceptFriendStatusWithDetails(wxid string) map[string]interface{} {
	result := map[string]interface{}{
		"wxid":   wxid,
		"enable": GetAutoAcceptFriendStatus(wxid),
		"status": map[string]interface{}{
			"code":        0,
			"description": "",
		},
		"source": "memory",
	}

	// 尝试从Redis获取详细信息
	redisKey := fmt.Sprintf("auto_accept_friend:%s", wxid)
	var statusData map[string]interface{}

	if err := comm.GETObj(redisKey, &statusData); err == nil {
		result["source"] = "redis"
		if updatedAt, ok := statusData["updated_at"]; ok {
			result["updated_at"] = updatedAt
		}
		if updatedBy, ok := statusData["updated_by"]; ok {
			result["updated_by"] = updatedBy
		}
	}

	// 设置状态描述
	if result["enable"].(bool) {
		result["status"] = map[string]interface{}{
			"code":        1,
			"description": "自动通过好友请求已开启",
		}
	} else {
		result["status"] = map[string]interface{}{
			"code":        0,
			"description": "自动通过好友请求已关闭",
		}
	}

	return result
}

// ==================== 初始化函数 ====================

// InitAutoFeaturesFromRedis 从Redis初始化自动功能状态
func InitAutoFeaturesFromRedis() {
	fmt.Printf("[AUTO_FEATURES] 开始从Redis恢复自动功能状态...\n")

	// 预热状态缓存 - 扫描Redis中的自动功能键
	go preloadAutoFeatureStates()

	fmt.Printf("[AUTO_FEATURES] Redis状态恢复完成，将在首次访问时按需加载\n")
}

// preloadAutoFeatureStates 预加载自动功能状态到内存
func preloadAutoFeatureStates() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("[AUTO_FEATURES] 预加载状态时发生错误: %v\n", r)
		}
	}()

	// 扫描自动红包键
	if keys, err := comm.RedisClient.Keys("auto_redpacket:*").Result(); err == nil {
		for _, key := range keys {
			if wxid := extractWxidFromKey(key, "auto_redpacket:"); wxid != "" {
				// 触发状态加载到内存
				GetAutoRedPacketStatus(wxid)
			}
		}
		fmt.Printf("[AUTO_FEATURES] 预加载了 %d 个自动红包状态\n", len(keys))
	}

	// 扫描自动收款键
	if keys, err := comm.RedisClient.Keys("auto_collect_money:*").Result(); err == nil {
		for _, key := range keys {
			if wxid := extractWxidFromKey(key, "auto_collect_money:"); wxid != "" {
				GetAutoCollectMoneyStatus(wxid)
			}
		}
		fmt.Printf("[AUTO_FEATURES] 预加载了 %d 个自动收款状态\n", len(keys))
	}

	// 扫描自动通过好友键
	if keys, err := comm.RedisClient.Keys("auto_accept_friend:*").Result(); err == nil {
		for _, key := range keys {
			if wxid := extractWxidFromKey(key, "auto_accept_friend:"); wxid != "" {
				GetAutoAcceptFriendStatus(wxid)
			}
		}
		fmt.Printf("[AUTO_FEATURES] 预加载了 %d 个自动通过好友状态\n", len(keys))
	}
}

// extractWxidFromKey 从Redis键中提取wxid
func extractWxidFromKey(key, prefix string) string {
	if len(key) > len(prefix) {
		return key[len(prefix):]
	}
	return ""
}

// GetAutoFeaturesStats 获取自动功能统计信息
func GetAutoFeaturesStats() map[string]interface{} {
	autoRedPacketMutex.RLock()
	autoCollectMoneyMutex.RLock()
	autoAcceptFriendMutex.RLock()

	stats := map[string]interface{}{
		"memory_status_count": map[string]int{
			"redpacket": len(autoRedPacketStatus),
			"collect":   len(autoCollectMoneyStatus),
			"friend":    len(autoAcceptFriendStatus),
		},
		"restored_status_count": map[string]int{
			"redpacket": len(redpacketRestoredStatus),
			"collect":   len(collectRestoredStatus),
			"friend":    len(friendRestoredStatus),
		},
	}

	autoAcceptFriendMutex.RUnlock()
	autoCollectMoneyMutex.RUnlock()
	autoRedPacketMutex.RUnlock()

	return stats
}
