// Package controllers 消息类型管理控制器
//
// 本控制器提供消息类型解析和管理的API接口，包括：
// - 消息类型查询
// - 消息内容解析
// - 消息类型统计
// - 解析器配置管理
package controllers

import (
	"encoding/json"
	"fmt"
	"strconv"
	"wechatdll/TcpPoll"
	"wechatdll/models"

	"github.com/astaxie/beego"
)

// MessageTypeController 消息类型控制器
type MessageTypeController struct {
	beego.Controller
}

// GetAllMessageTypes 获取所有支持的消息类型
// @Title 获取所有消息类型
// @Description 获取系统支持的所有消息类型信息
// @Success 200 {object} models.ResponseResult "成功"
// @Failure 500 {object} models.ResponseResult "服务器错误"
// @router /types [get]
func (c *MessageTypeController) GetAllMessageTypes() {
	defer func() {
		if r := recover(); r != nil {
			c.Data["json"] = models.ResponseResult{
				Code:    -1,
				Success: false,
				Message: fmt.Sprintf("系统异常: %v", r),
				Data:    nil,
			}
			c.ServeJSON()
		}
	}()

	// 获取所有消息类型
	messageTypes := TcpPoll.GetAllMessageTypes()
	
	// 按类别分组
	typesByCategory := make(map[string][]TcpPoll.MessageTypeInfo)
	for _, typeInfo := range messageTypes {
		typesByCategory[typeInfo.Category] = append(typesByCategory[typeInfo.Category], typeInfo)
	}

	result := map[string]interface{}{
		"total":          len(messageTypes),
		"types":          messageTypes,
		"byCategory":     typesByCategory,
		"categories":     []string{"basic", "media", "contact", "location", "rich", "system", "payment"},
	}

	c.Data["json"] = models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "获取成功",
		Data:    result,
	}
	c.ServeJSON()
}

// GetMessageTypesByCategory 按类别获取消息类型
// @Title 按类别获取消息类型
// @Description 根据指定类别获取消息类型列表
// @Param category query string true "消息类别"
// @Success 200 {object} models.ResponseResult "成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @router /types/category [get]
func (c *MessageTypeController) GetMessageTypesByCategory() {
	defer func() {
		if r := recover(); r != nil {
			c.Data["json"] = models.ResponseResult{
				Code:    -1,
				Success: false,
				Message: fmt.Sprintf("系统异常: %v", r),
				Data:    nil,
			}
			c.ServeJSON()
		}
	}()

	category := c.GetString("category")
	if category == "" {
		c.Data["json"] = models.ResponseResult{
			Code:    -2,
			Success: false,
			Message: "请提供消息类别参数",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	// 获取指定类别的消息类型
	types := TcpPoll.GetMessageTypesByCategory(category)

	result := map[string]interface{}{
		"category": category,
		"count":    len(types),
		"types":    types,
	}

	c.Data["json"] = models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "获取成功",
		Data:    result,
	}
	c.ServeJSON()
}

// ParseMessage 解析消息内容
// @Title 解析消息内容
// @Description 解析指定类型的消息内容，返回结构化数据
// @Param body body ParseMessageRequest true "解析请求"
// @Success 200 {object} models.ResponseResult "成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @router /parse [post]
func (c *MessageTypeController) ParseMessage() {
	defer func() {
		if r := recover(); r != nil {
			c.Data["json"] = models.ResponseResult{
				Code:    -1,
				Success: false,
				Message: fmt.Sprintf("系统异常: %v", r),
				Data:    nil,
			}
			c.ServeJSON()
		}
	}()

	var request ParseMessageRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		c.Data["json"] = models.ResponseResult{
			Code:    -2,
			Success: false,
			Message: "请求参数解析失败",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	// 验证参数
	if request.Content == "" {
		c.Data["json"] = models.ResponseResult{
			Code:    -3,
			Success: false,
			Message: "消息内容不能为空",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	// 创建消息解析器
	parser := TcpPoll.NewEnhancedMessageParser(true, request.DebugMode)
	
	// 解析消息
	parsed := parser.ParseMessage(request.Content, request.MsgType)

	result := map[string]interface{}{
		"original": map[string]interface{}{
			"content": request.Content,
			"msgType": request.MsgType,
		},
		"parsed": parsed,
		"analysis": map[string]interface{}{
			"contentLength":  len(request.Content),
			"hasXML":        containsXML(request.Content),
			"hasJSON":       containsJSON(request.Content),
			"isSystemType":  TcpPoll.IsSystemMessage(request.MsgType),
		},
	}

	c.Data["json"] = models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "解析成功",
		Data:    result,
	}
	c.ServeJSON()
}

// GetMessageTypeStats 获取消息类型统计
// @Title 获取消息类型统计
// @Description 获取消息类型的统计信息
// @Success 200 {object} models.ResponseResult "成功"
// @router /stats [get]
func (c *MessageTypeController) GetMessageTypeStats() {
	defer func() {
		if r := recover(); r != nil {
			c.Data["json"] = models.ResponseResult{
				Code:    -1,
				Success: false,
				Message: fmt.Sprintf("系统异常: %v", r),
				Data:    nil,
			}
			c.ServeJSON()
		}
	}()

	messageTypes := TcpPoll.GetAllMessageTypes()
	
	// 统计各类别的消息类型数量
	categoryStats := make(map[string]int)
	systemTypeCount := 0
	
	for _, typeInfo := range messageTypes {
		categoryStats[typeInfo.Category]++
		if typeInfo.IsSystem {
			systemTypeCount++
		}
	}

	result := map[string]interface{}{
		"totalTypes":      len(messageTypes),
		"systemTypes":     systemTypeCount,
		"userTypes":       len(messageTypes) - systemTypeCount,
		"categoryStats":   categoryStats,
		"supportedCategories": []string{"basic", "media", "contact", "location", "rich", "system", "payment"},
	}

	c.Data["json"] = models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "获取统计成功",
		Data:    result,
	}
	c.ServeJSON()
}

// CheckMessageType 检查消息类型
// @Title 检查消息类型
// @Description 检查指定的消息类型是否被支持
// @Param msgType query int true "消息类型"
// @Success 200 {object} models.ResponseResult "成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @router /check [get]
func (c *MessageTypeController) CheckMessageType() {
	defer func() {
		if r := recover(); r != nil {
			c.Data["json"] = models.ResponseResult{
				Code:    -1,
				Success: false,
				Message: fmt.Sprintf("系统异常: %v", r),
				Data:    nil,
			}
			c.ServeJSON()
		}
	}()

	msgTypeStr := c.GetString("msgType")
	if msgTypeStr == "" {
		c.Data["json"] = models.ResponseResult{
			Code:    -2,
			Success: false,
			Message: "请提供消息类型参数",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	msgType, err := strconv.ParseUint(msgTypeStr, 10, 32)
	if err != nil {
		c.Data["json"] = models.ResponseResult{
			Code:    -3,
			Success: false,
			Message: "消息类型参数格式错误",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	messageTypes := TcpPoll.GetAllMessageTypes()
	typeInfo, supported := messageTypes[uint32(msgType)]
	
	result := map[string]interface{}{
		"msgType":   uint32(msgType),
		"supported": supported,
	}
	
	if supported {
		result["typeInfo"] = typeInfo
		result["description"] = typeInfo.Description
		result["category"] = typeInfo.Category
		result["isSystem"] = typeInfo.IsSystem
	} else {
		result["description"] = fmt.Sprintf("未知类型(%d)", msgType)
	}

	c.Data["json"] = models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "检查完成",
		Data:    result,
	}
	c.ServeJSON()
}

// ParseMessageRequest 解析消息请求结构
type ParseMessageRequest struct {
	Content   string `json:"content"`   // 消息内容
	MsgType   uint32 `json:"msgType"`   // 消息类型
	DebugMode bool   `json:"debugMode"` // 调试模式
}

// 辅助函数

// containsXML 检查内容是否包含XML
func containsXML(content string) bool {
	return len(content) > 0 && (content[0] == '<' || 
		   (len(content) > 5 && content[:5] == "<?xml"))
}

// containsJSON 检查内容是否包含JSON
func containsJSON(content string) bool {
	return len(content) > 0 && content[0] == '{'
}
