package Login

import (
	"encoding/json"
	"wechatdll/models"
)

type ExtDeviceLoginConfirmParam struct {
	Wxid string
	Url  string
}

type Data62LoginReq struct {
	UserName   string
	Password   string
	Data62     string
	DeviceName string
	Proxy      models.ProxyInfo
}

type Data62SMSAgainReq struct {
	Url    string
	Cookie string
	Proxy  models.ProxyInfo
}

type Data62SMSVerifyReq struct {
	Url    string
	Cookie string
	Sms    string
	Proxy  models.ProxyInfo
}

type Data62QRCodeVerifyReq struct {
	Url   string
	Proxy models.ProxyInfo
}

// UnmarshalJSON 自定义JSON反序列化，兼容前端发送的空Proxy对象
func (req *Data62LoginReq) UnmarshalJSON(data []byte) error {
	// 定义一个临时结构体，避免递归调用
	type Alias Data62LoginReq
	aux := &struct {
		*Alias
		Proxy interface{} `json:"Proxy"`
	}{
		Alias: (*Alias)(req),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 处理Proxy字段
	if aux.Proxy != nil {
		// 如果Proxy是空对象{}，创建一个空的ProxyInfo
		if proxyBytes, err := json.Marshal(aux.Proxy); err == nil {
			var proxy models.ProxyInfo
			if err := json.Unmarshal(proxyBytes, &proxy); err == nil {
				req.Proxy = proxy
			}
		}
	}

	return nil
}
