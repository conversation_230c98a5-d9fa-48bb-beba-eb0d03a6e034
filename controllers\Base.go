package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"
	"wechatdll/models"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

type BaseController struct {
	beego.Controller
}

// ServeJSON 优化的JSON响应方法
func (c *BaseController) ServeJSON() {
	c.Ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	c.Ctx.Output.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Ctx.Output.Header("Pragma", "no-cache")
	c.Ctx.Output.Header("Expires", "0")

	data := c.Data["json"]
	if data == nil {
		c.ServeErrorJSON(http.StatusInternalServerError, "响应数据为空")
		return
	}

	encoder := json.NewEncoder(c.Ctx.Output.Context.ResponseWriter)
	encoder.SetEscapeHTML(false)

	// 生产环境不使用缩进，减少传输大小
	if beego.BConfig.RunMode != "prod" {
		encoder.SetIndent("", "  ")
	}

	if err := encoder.Encode(data); err != nil {
		// 检查是否是客户端连接关闭导致的错误
		if isClientDisconnectError(err) {
			log.Debugf("客户端连接已断开，无法发送响应: %v", err)
			return
		}
		log.Errorf("JSON编码失败: %v", err)
		c.ServeErrorJSON(http.StatusInternalServerError, "JSON编码失败")
		return
	}
}

// SendSuccessResponse 返回成功响应 - 新的统一方法
func (c *BaseController) SendSuccessResponse(data any, message string) {
	if message == "" {
		message = "操作成功"
	}

	response := models.NewSuccessResponse(data, message)
	response.SetRequestID(c.getRequestID())

	c.Data["json"] = response
	c.ServeJSON()
}

// SendErrorResponse 返回错误响应 - 新的统一方法
func (c *BaseController) SendErrorResponse(code int64, message string) {
	if message == "" {
		message = "操作失败"
	}

	response := models.NewErrorResponse(code, message)
	response.SetRequestID(c.getRequestID())

	c.Ctx.Output.SetStatus(int(code))
	c.Data["json"] = response
	c.ServeJSON()
}

// ServeSuccessJSON 返回成功响应 - 保持向后兼容
func (c *BaseController) ServeSuccessJSON(data any, message string) {
	c.SendSuccessResponse(data, message)
}

// ServeErrorJSON 返回错误响应 - 保持向后兼容
func (c *BaseController) ServeErrorJSON(code int, message string) {
	c.SendErrorResponse(int64(code), message)
}

// ParseRequestBody 解析请求体
func (c *BaseController) ParseRequestBody(v any) error {
	if len(c.Ctx.Input.RequestBody) == 0 {
		return fmt.Errorf("请求体为空")
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, v); err != nil {
		return fmt.Errorf("JSON解析失败: %w", err)
	}

	return nil
}

// SendLegacyResponse 发送兼容旧版本的响应（用于逐步迁移）
func (c *BaseController) SendLegacyResponse(code int64, success bool, message string, data interface{}) {
	response := models.NewLegacyResponse(code, success, message, data)
	response.SetRequestID(c.getRequestID())

	c.Data["json"] = response
	c.ServeJSON()
}

// SendLegacyResponseWithData62 发送包含Data62的响应（用于登录相关接口）
func (c *BaseController) SendLegacyResponseWithData62(code int64, success bool, message string, data interface{}, data62, deviceID string) {
	response := models.NewLegacyResponseWithData62(code, success, message, data, data62, deviceID)
	response.SetRequestID(c.getRequestID())

	c.Data["json"] = response
	c.ServeJSON()
}

// HandleError 统一错误处理 - 增强版
func (c *BaseController) HandleError(err error, defaultMessage string) {
	if err != nil {
		// 根据错误类型返回不同的错误码和处理方式
		var code int64 = models.ErrCodeSystemError
		message := defaultMessage

		if apiErr, ok := err.(*models.APIError); ok {
			code = apiErr.Code
			message = apiErr.Message
		} else if err.Error() != "" {
			message = err.Error()
		}

		// 记录错误日志
		log.WithFields(log.Fields{
			"error":      err.Error(),
			"method":     c.Ctx.Request.Method,
			"path":       c.Ctx.Request.URL.Path,
			"ip":         c.Ctx.Input.IP(),
			"user_agent": c.Ctx.Request.UserAgent(),
			"request_id": c.getRequestID(),
		}).Error("API错误")

		c.SendErrorResponse(code, message)
	}
}

// HandleWXError 处理微信API错误
func (c *BaseController) HandleWXError(wxErr error, wxCode int32, defaultMessage string) {
	var code int64 = models.ErrCodeWXAPIError
	message := defaultMessage

	// 根据微信错误码映射
	switch wxCode {
	case -1:
		code = models.ErrCodeWXProtocolError
		message = "微信协议错误"
	case -2:
		code = models.ErrCodeWXAccountError
		message = "微信账号异常"
	case -100:
		code = models.ErrCodeLoginExpired
		message = "登录已过期，请重新登录"
	default:
		if wxErr != nil {
			message = fmt.Sprintf("微信API错误: %v", wxErr)
		}
	}

	// 记录微信API错误
	log.WithFields(log.Fields{
		"wx_code":    wxCode,
		"wx_error":   wxErr,
		"method":     c.Ctx.Request.Method,
		"path":       c.Ctx.Request.URL.Path,
		"request_id": c.getRequestID(),
	}).Warn("微信API错误")

	c.SendErrorResponse(code, message)
}

// HandleNetworkError 处理网络错误
func (c *BaseController) HandleNetworkError(err error) {
	var code int64 = models.ErrCodeNetworkError
	message := "网络连接错误"

	// 根据错误类型细分
	errStr := err.Error()
	if strings.Contains(errStr, "timeout") {
		code = models.ErrCodeTimeout
		message = "请求超时"
	} else if strings.Contains(errStr, "connection refused") {
		code = models.ErrCodeWXConnectFailed
		message = "连接被拒绝"
	}

	// 记录网络错误
	log.WithFields(log.Fields{
		"error":      err.Error(),
		"method":     c.Ctx.Request.Method,
		"path":       c.Ctx.Request.URL.Path,
		"request_id": c.getRequestID(),
	}).Warn("网络错误")

	c.SendErrorResponse(code, message)
}

// ValidateAndParseRequest 验证并解析请求
func (c *BaseController) ValidateAndParseRequest(v interface{}) error {
	if err := c.ParseRequestBody(v); err != nil {
		c.SendErrorResponse(models.ErrCodeInvalidParam, "请求参数解析失败: "+err.Error())
		return err
	}
	return nil
}

// getRequestID 生成请求ID
func (c *BaseController) getRequestID() string {
	// 简单的请求ID生成，实际项目中可以使用更复杂的算法
	return fmt.Sprintf("%d-%s", time.Now().UnixNano(), c.Ctx.Input.IP())
}

// isClientDisconnectError 检查是否是客户端断开连接的错误
func isClientDisconnectError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// 检查常见的客户端断开连接错误
	clientDisconnectPatterns := []string{
		"connection was forcibly closed by the remote host",
		"wsasend: An existing connection was forcibly closed",
		"broken pipe",
		"connection reset by peer",
		"client disconnected",
		"write: connection reset by peer",
		"write: broken pipe",
	}

	for _, pattern := range clientDisconnectPatterns {
		if strings.Contains(strings.ToLower(errStr), strings.ToLower(pattern)) {
			return true
		}
	}

	return false
}
