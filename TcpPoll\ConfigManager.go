package TcpPoll

import (
	"sync"
	"time"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// ==================== 配置管理优化 ====================

// Config 配置结构
type Config struct {
	// 连接相关配置
	LongLinkEnabled        bool          `json:"long_link_enabled"`
	ConnectionTimeout      time.Duration `json:"connection_timeout"`
	LongLinkConnectTimeout time.Duration `json:"long_link_connect_timeout"`
	HeartbeatInterval      time.Duration `json:"heartbeat_interval"`
	
	// 性能相关配置
	MaxConnections         int           `json:"max_connections"`
	EpollWaitTimeout       time.Duration `json:"epoll_wait_timeout"`
	MessageBufferSize      int           `json:"message_buffer_size"`
	WorkerPoolSize         int           `json:"worker_pool_size"`
	
	// 重试相关配置
	MaxRetries             int           `json:"max_retries"`
	RetryInterval          time.Duration `json:"retry_interval"`
	
	// 日志相关配置
	LogLevel               string        `json:"log_level"`
	EnablePerformanceLog   bool          `json:"enable_performance_log"`
	
	// 自动功能配置
	AutoRedPacketEnabled   bool          `json:"auto_red_packet_enabled"`
	AutoCollectEnabled     bool          `json:"auto_collect_enabled"`
	AutoFriendEnabled      bool          `json:"auto_friend_enabled"`
	
	// 安全相关配置
	EnableRateLimit        bool          `json:"enable_rate_limit"`
	RateLimitPerSecond     int           `json:"rate_limit_per_second"`
}

// ConfigManager 配置管理器
type ConfigManager struct {
	config *Config
	mutex  sync.RWMutex
}

var (
	configManager *ConfigManager
	configOnce    sync.Once
)

// GetConfigManager 获取配置管理器单例
func GetConfigManager() *ConfigManager {
	configOnce.Do(func() {
		configManager = &ConfigManager{
			config: loadDefaultConfig(),
		}
		configManager.loadFromBeego()
	})
	return configManager
}

// loadDefaultConfig 加载默认配置
func loadDefaultConfig() *Config {
	return &Config{
		// 连接相关默认配置
		LongLinkEnabled:        true,
		ConnectionTimeout:      30 * time.Second,
		LongLinkConnectTimeout: 30 * time.Second,
		HeartbeatInterval:      20 * time.Second,
		
		// 性能相关默认配置
		MaxConnections:         1000,
		EpollWaitTimeout:       100 * time.Millisecond,
		MessageBufferSize:      4096,
		WorkerPoolSize:         10,
		
		// 重试相关默认配置
		MaxRetries:             3,
		RetryInterval:          1 * time.Second,
		
		// 日志相关默认配置
		LogLevel:               "info",
		EnablePerformanceLog:   true,
		
		// 自动功能默认配置
		AutoRedPacketEnabled:   false,
		AutoCollectEnabled:     false,
		AutoFriendEnabled:      false,
		
		// 安全相关默认配置
		EnableRateLimit:        true,
		RateLimitPerSecond:     100,
	}
}

// loadFromBeego 从Beego配置加载
func (cm *ConfigManager) loadFromBeego() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	// 连接相关配置
	if enabled, err := beego.AppConfig.Bool("longlinkenabled"); err == nil {
		cm.config.LongLinkEnabled = enabled
	}
	
	if timeout := beego.AppConfig.String("connectiontimeout"); timeout != "" {
		if d, err := time.ParseDuration(timeout); err == nil {
			cm.config.ConnectionTimeout = d
		}
	}
	
	if timeout := beego.AppConfig.String("longlinkconnecttimeout"); timeout != "" {
		if d, err := time.ParseDuration(timeout); err == nil {
			cm.config.LongLinkConnectTimeout = d
		}
	}
	
	if interval := beego.AppConfig.String("heartbeatinterval"); interval != "" {
		if d, err := time.ParseDuration(interval); err == nil {
			cm.config.HeartbeatInterval = d
		}
	}
	
	// 性能相关配置
	if maxConn, err := beego.AppConfig.Int("maxconnections"); err == nil {
		cm.config.MaxConnections = maxConn
	}
	
	if bufSize, err := beego.AppConfig.Int("messagebuffersize"); err == nil {
		cm.config.MessageBufferSize = bufSize
	}
	
	if poolSize, err := beego.AppConfig.Int("workerpoolsize"); err == nil {
		cm.config.WorkerPoolSize = poolSize
	}
	
	// 重试相关配置
	if maxRetries, err := beego.AppConfig.Int("maxretries"); err == nil {
		cm.config.MaxRetries = maxRetries
	}
	
	if interval := beego.AppConfig.String("retryinterval"); interval != "" {
		if d, err := time.ParseDuration(interval); err == nil {
			cm.config.RetryInterval = d
		}
	}
	
	// 日志相关配置
	if level := beego.AppConfig.String("loglevel"); level != "" {
		cm.config.LogLevel = level
	}
	
	if enabled, err := beego.AppConfig.Bool("enableperformancelog"); err == nil {
		cm.config.EnablePerformanceLog = enabled
	}
	
	// 安全相关配置
	if enabled, err := beego.AppConfig.Bool("enableratelimit"); err == nil {
		cm.config.EnableRateLimit = enabled
	}
	
	if rate, err := beego.AppConfig.Int("ratelimitpersecond"); err == nil {
		cm.config.RateLimitPerSecond = rate
	}
}

// GetConfig 获取配置副本
func (cm *ConfigManager) GetConfig() Config {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return *cm.config
}

// UpdateConfig 更新配置
func (cm *ConfigManager) UpdateConfig(newConfig Config) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.config = &newConfig
	log.Info("配置已更新")
}

// 便捷的配置获取方法

// IsLongLinkEnabled 是否启用长连接
func (cm *ConfigManager) IsLongLinkEnabled() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.LongLinkEnabled
}

// GetConnectionTimeout 获取连接超时时间
func (cm *ConfigManager) GetConnectionTimeout() time.Duration {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.ConnectionTimeout
}

// GetLongLinkConnectTimeout 获取长连接超时时间
func (cm *ConfigManager) GetLongLinkConnectTimeout() time.Duration {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.LongLinkConnectTimeout
}

// GetHeartbeatInterval 获取心跳间隔
func (cm *ConfigManager) GetHeartbeatInterval() time.Duration {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.HeartbeatInterval
}

// GetMaxConnections 获取最大连接数
func (cm *ConfigManager) GetMaxConnections() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.MaxConnections
}

// GetMessageBufferSize 获取消息缓冲区大小
func (cm *ConfigManager) GetMessageBufferSize() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.MessageBufferSize
}

// GetWorkerPoolSize 获取工作池大小
func (cm *ConfigManager) GetWorkerPoolSize() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.WorkerPoolSize
}

// GetMaxRetries 获取最大重试次数
func (cm *ConfigManager) GetMaxRetries() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.MaxRetries
}

// GetRetryInterval 获取重试间隔
func (cm *ConfigManager) GetRetryInterval() time.Duration {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.RetryInterval
}

// IsPerformanceLogEnabled 是否启用性能日志
func (cm *ConfigManager) IsPerformanceLogEnabled() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.EnablePerformanceLog
}

// IsRateLimitEnabled 是否启用速率限制
func (cm *ConfigManager) IsRateLimitEnabled() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.EnableRateLimit
}

// GetRateLimitPerSecond 获取每秒速率限制
func (cm *ConfigManager) GetRateLimitPerSecond() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config.RateLimitPerSecond
}

// ==================== 全局配置访问函数 ====================

// 提供全局访问函数，简化使用
var globalConfig = GetConfigManager()

// IsLongLinkEnabled 全局函数：是否启用长连接
func IsLongLinkEnabled() bool {
	return globalConfig.IsLongLinkEnabled()
}

// GetConnectionTimeout 全局函数：获取连接超时时间
func GetConnectionTimeout() time.Duration {
	return globalConfig.GetConnectionTimeout()
}

// GetHeartbeatInterval 全局函数：获取心跳间隔
func GetHeartbeatInterval() time.Duration {
	return globalConfig.GetHeartbeatInterval()
}

// GetMaxConnections 全局函数：获取最大连接数
func GetMaxConnections() int {
	return globalConfig.GetMaxConnections()
}

// GetMessageBufferSize 全局函数：获取消息缓冲区大小
func GetMessageBufferSize() int {
	return globalConfig.GetMessageBufferSize()
}
