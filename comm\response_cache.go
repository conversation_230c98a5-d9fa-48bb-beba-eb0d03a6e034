package comm

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// CacheItem 缓存项
type CacheItem struct {
	Data      interface{} `json:"data"`
	ExpiresAt time.Time   `json:"expires_at"`
	CreatedAt time.Time   `json:"created_at"`
	HitCount  int64       `json:"hit_count"`
}

// IsExpired 检查缓存项是否过期
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// ResponseCache 响应缓存管理器
type ResponseCache struct {
	cache     map[string]*CacheItem
	mutex     sync.RWMutex
	maxSize   int
	hitCount  int64
	missCount int64

	// 清理相关
	cleanupInterval time.Duration
	stopCleanup     chan bool
}

// NewResponseCache 创建响应缓存管理器
func NewResponseCache(maxSize int, cleanupInterval time.Duration) *ResponseCache {
	rc := &ResponseCache{
		cache:           make(map[string]*CacheItem),
		maxSize:         maxSize,
		cleanupInterval: cleanupInterval,
		stopCleanup:     make(chan bool),
	}

	// 启动清理协程
	go rc.startCleanup()

	return rc
}

// generateKey 生成缓存键
func (rc *ResponseCache) generateKey(method, path string, params interface{}) string {
	// 将参数序列化为JSON
	paramBytes, _ := json.Marshal(params)

	// 创建唯一键
	keyData := fmt.Sprintf("%s:%s:%s", method, path, string(paramBytes))

	// 使用MD5生成短键
	hash := md5.Sum([]byte(keyData))
	return hex.EncodeToString(hash[:])
}

// Get 获取缓存数据
func (rc *ResponseCache) Get(method, path string, params interface{}) (interface{}, bool) {
	key := rc.generateKey(method, path, params)

	rc.mutex.RLock()
	item, exists := rc.cache[key]
	rc.mutex.RUnlock()

	if !exists {
		rc.mutex.Lock()
		rc.missCount++
		rc.mutex.Unlock()
		return nil, false
	}

	// 检查是否过期
	if item.IsExpired() {
		rc.mutex.Lock()
		delete(rc.cache, key)
		rc.missCount++
		rc.mutex.Unlock()
		return nil, false
	}

	// 更新命中次数
	rc.mutex.Lock()
	item.HitCount++
	rc.hitCount++
	rc.mutex.Unlock()

	return item.Data, true
}

// Set 设置缓存数据
func (rc *ResponseCache) Set(method, path string, params interface{}, data interface{}, ttl time.Duration) {
	key := rc.generateKey(method, path, params)

	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	// 检查缓存大小限制
	if len(rc.cache) >= rc.maxSize {
		rc.evictLRU()
	}

	// 创建缓存项
	item := &CacheItem{
		Data:      data,
		ExpiresAt: time.Now().Add(ttl),
		CreatedAt: time.Now(),
		HitCount:  0,
	}

	rc.cache[key] = item
}

// evictLRU 使用LRU策略清理缓存
func (rc *ResponseCache) evictLRU() {
	if len(rc.cache) == 0 {
		return
	}

	// 找到最少使用的缓存项
	var oldestKey string
	var oldestTime time.Time = time.Now()
	var lowestHitCount int64 = -1

	for key, item := range rc.cache {
		// 优先清理过期的项
		if item.IsExpired() {
			delete(rc.cache, key)
			return
		}

		// 找到命中次数最少且最旧的项
		if lowestHitCount == -1 || item.HitCount < lowestHitCount ||
			(item.HitCount == lowestHitCount && item.CreatedAt.Before(oldestTime)) {
			oldestKey = key
			oldestTime = item.CreatedAt
			lowestHitCount = item.HitCount
		}
	}

	if oldestKey != "" {
		delete(rc.cache, oldestKey)
	}
}

// Clear 清空缓存
func (rc *ResponseCache) Clear() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	rc.cache = make(map[string]*CacheItem)
	rc.hitCount = 0
	rc.missCount = 0
}

// GetStats 获取缓存统计信息
func (rc *ResponseCache) GetStats() map[string]interface{} {
	rc.mutex.RLock()
	defer rc.mutex.RUnlock()

	totalRequests := rc.hitCount + rc.missCount
	hitRate := float64(0)
	if totalRequests > 0 {
		hitRate = float64(rc.hitCount) / float64(totalRequests) * 100
	}

	return map[string]interface{}{
		"cache_size":     len(rc.cache),
		"max_size":       rc.maxSize,
		"hit_count":      rc.hitCount,
		"miss_count":     rc.missCount,
		"hit_rate":       fmt.Sprintf("%.2f%%", hitRate),
		"total_requests": totalRequests,
	}
}

// startCleanup 启动定期清理
func (rc *ResponseCache) startCleanup() {
	ticker := time.NewTicker(rc.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rc.cleanup()
		case <-rc.stopCleanup:
			return
		}
	}
}

// cleanup 清理过期缓存
func (rc *ResponseCache) cleanup() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	expiredKeys := make([]string, 0)

	for key, item := range rc.cache {
		if item.IsExpired() {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(rc.cache, key)
	}

	if len(expiredKeys) > 0 {
		log.Debugf("清理了 %d 个过期缓存项", len(expiredKeys))
	}
}

// Stop 停止缓存管理器
func (rc *ResponseCache) Stop() {
	close(rc.stopCleanup)
}

// ResponseCacheConfig 响应缓存配置
type ResponseCacheConfig struct {
	MaxSize         int           `json:"max_size"`         // 最大缓存项数量
	CleanupInterval time.Duration `json:"cleanup_interval"` // 清理间隔
	DefaultTTL      time.Duration `json:"default_ttl"`      // 默认过期时间
}

// DefaultResponseCacheConfig 默认响应缓存配置
var DefaultResponseCacheConfig = ResponseCacheConfig{
	MaxSize:         1000,
	CleanupInterval: 5 * time.Minute,
	DefaultTTL:      10 * time.Minute,
}

// 全局缓存实例
var (
	globalResponseCache *ResponseCache
	cacheOnce           sync.Once
)

// GetResponseCache 获取全局响应缓存实例
func GetResponseCache() *ResponseCache {
	cacheOnce.Do(func() {
		globalResponseCache = NewResponseCache(
			DefaultResponseCacheConfig.MaxSize,
			DefaultResponseCacheConfig.CleanupInterval,
		)
	})
	return globalResponseCache
}

// CacheableResponse 可缓存的响应接口
type CacheableResponse interface {
	IsCacheable() bool
	GetCacheTTL() time.Duration
}

// ShouldCache 判断响应是否应该被缓存
func ShouldCache(method string, statusCode int, response interface{}) bool {
	// 只缓存GET请求的成功响应
	if method != "GET" || statusCode != 200 {
		return false
	}

	// 如果响应实现了CacheableResponse接口，使用其判断
	if cacheable, ok := response.(CacheableResponse); ok {
		return cacheable.IsCacheable()
	}

	// 默认缓存成功响应
	return true
}

// GetCacheTTL 获取缓存TTL
func GetCacheTTL(response interface{}) time.Duration {
	if cacheable, ok := response.(CacheableResponse); ok {
		return cacheable.GetCacheTTL()
	}
	return DefaultResponseCacheConfig.DefaultTTL
}
