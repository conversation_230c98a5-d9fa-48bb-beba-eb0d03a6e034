package Friend

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
	log "github.com/sirupsen/logrus"
)

// 联系人详情缓存结构
type ContactDetailCacheData struct {
	Wxid      string      `json:"wxid"`
	Towxids   string      `json:"towxids"`
	ChatRoom  string      `json:"chat_room"`
	Data      interface{} `json:"data"`
	CacheTime time.Time   `json:"cache_time"`
}

// 生成缓存文件名（基于参数的MD5）
func getDetailCacheFileName(wxid, towxids, chatRoom string) string {
	key := fmt.Sprintf("%s_%s_%s", wxid, towxids, chatRoom)
	hash := md5.Sum([]byte(key))
	return hex.EncodeToString(hash[:]) + ".json"
}

// 获取联系人详情缓存文件路径
func getDetailCacheFilePath(wxid, towxids, chatRoom string) string {
	// 获取当前执行文件的目录
	execPath, err := os.Executable()
	if err != nil {
		log.WithError(err).Error("获取执行文件路径失败")
		return ""
	}

	execDir := filepath.Dir(execPath)
	cacheDir := filepath.Join(execDir, "cache")

	// 确保缓存目录存在
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		log.WithError(err).Error("创建缓存目录失败")
		return ""
	}

	fileName := getDetailCacheFileName(wxid, towxids, chatRoom)
	return filepath.Join(cacheDir, fileName)
}

// 从文件加载联系人详情缓存
func loadDetailCacheFromFile(wxid, towxids, chatRoom string) (*ContactDetailCacheData, error) {
	filePath := getDetailCacheFilePath(wxid, towxids, chatRoom)
	if filePath == "" {
		return nil, fmt.Errorf("无法获取缓存文件路径")
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("缓存文件不存在")
	}

	// 读取文件内容
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取缓存文件失败: %v", err)
	}

	// 解析JSON
	var cacheData ContactDetailCacheData
	if err := json.Unmarshal(data, &cacheData); err != nil {
		return nil, fmt.Errorf("解析缓存文件失败: %v", err)
	}

	return &cacheData, nil
}

// extractContactFromSharedCache 从共享缓存中提取指定联系人的详细信息
func extractContactFromSharedCache(wxid, towxids string) ([]interface{}, error) {
	// 加载共享缓存文件
	cacheData, err := loadCacheFromFile(wxid)
	if err != nil {
		return nil, err
	}

	// 解析要查找的联系人ID列表
	targetWxids := strings.Split(towxids, ",")
	targetMap := make(map[string]bool)
	for _, id := range targetWxids {
		if trimmed := strings.TrimSpace(id); trimmed != "" {
			targetMap[trimmed] = true
		}
	}

	var result []interface{}

	// 从缓存的详细信息中查找匹配的联系人
	for _, detail := range cacheData.Details {
		// 处理从JSON反序列化的数据（map[string]interface{}格式）
		if detailMap, ok := detail.(map[string]interface{}); ok {
			if contactListData, exists := detailMap["ContactList"]; exists {
				if contacts, ok := contactListData.([]interface{}); ok {
					for _, contactData := range contacts {
						if contactMap, ok := contactData.(map[string]interface{}); ok {
							// 提取联系人的wxid
							if userNameData, exists := contactMap["UserName"]; exists {
								if userNameMap, ok := userNameData.(map[string]interface{}); ok {
									if wxidVal, exists := userNameMap["string"]; exists {
										if wxidStr, ok := wxidVal.(string); ok {
											if targetMap[wxidStr] {
												result = append(result, contactMap)
												delete(targetMap, wxidStr) // 避免重复添加

												log.WithFields(log.Fields{
													"wxid": wxid,
													"found_contact": wxidStr,
												}).Info("📁 在共享缓存中找到指定联系人")
											}
										}
									}
								}
							}
						}
					}
				}
			}
		} else if response, ok := detail.(mm.GetContactResponse); ok {
			// 处理原始的 mm.GetContactResponse 类型数据（向后兼容）
			for _, contact := range response.ContactList {
				if contact.UserName != nil && contact.UserName.String_ != nil {
					contactWxid := *contact.UserName.String_
					if targetMap[contactWxid] {
						result = append(result, contact)
						delete(targetMap, contactWxid) // 避免重复添加

						log.WithFields(log.Fields{
							"wxid": wxid,
							"found_contact": contactWxid,
						}).Info("📁 在共享缓存中找到指定联系人（protobuf格式）")
					}
				}
			}
		}
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("在共享缓存中未找到指定的联系人")
	}

	return result, nil
}

// 保存联系人详情缓存到文件
func saveDetailCacheToFile(wxid, towxids, chatRoom string, data interface{}) error {
	filePath := getDetailCacheFilePath(wxid, towxids, chatRoom)
	if filePath == "" {
		return fmt.Errorf("无法获取缓存文件路径")
	}

	cacheData := ContactDetailCacheData{
		Wxid:      wxid,
		Towxids:   towxids,
		ChatRoom:  chatRoom,
		Data:      data,
		CacheTime: time.Now(),
	}

	// 转换为JSON
	jsonData, err := json.MarshalIndent(cacheData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化缓存数据失败: %v", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(filePath, jsonData, 0644); err != nil {
		return fmt.Errorf("写入缓存文件失败: %v", err)
	}

	log.WithFields(log.Fields{
		"wxid":       wxid,
		"towxids":    towxids,
		"chat_room":  chatRoom,
		"file_path":  filePath,
		"cache_time": cacheData.CacheTime.Format("2006-01-02 15:04:05"),
	}).Info("💾 联系人详情缓存已保存到文件")

	return nil
}

func GetContractDetail(Data GetContractDetailparameter) models.ResponseResult {
	// 默认启用缓存，除非强制刷新
	if !Data.ForceRefresh {
		// 优先尝试从共享缓存中提取联系人信息
		if Data.Towxids != "" && Data.ChatRoom == "" {
			if contacts, err := extractContactFromSharedCache(Data.Wxid, Data.Towxids); err == nil {
				log.WithFields(log.Fields{
					"wxid":       Data.Wxid,
					"towxids":    Data.Towxids,
					"found_count": len(contacts),
					"file_path":  getCacheFilePath(Data.Wxid),
				}).Info("📁 从共享缓存中提取联系人详情")

				return models.ResponseResult{
					Code:    0,
					Success: true,
					Message: "成功（来自共享缓存）",
					Data:    contacts,
				}
			} else {
				log.WithFields(log.Fields{
					"wxid":     Data.Wxid,
					"towxids":  Data.Towxids,
					"error":    err.Error(),
				}).Info("📁 共享缓存中未找到指定联系人，尝试专用缓存")
			}
		}

		// 如果共享缓存中没有找到，尝试专用缓存
		if cacheData, err := loadDetailCacheFromFile(Data.Wxid, Data.Towxids, Data.ChatRoom); err == nil {
			log.WithFields(log.Fields{
				"wxid":       Data.Wxid,
				"towxids":    Data.Towxids,
				"chat_room":  Data.ChatRoom,
				"cache_time": cacheData.CacheTime.Format("2006-01-02 15:04:05"),
				"file_path":  getDetailCacheFilePath(Data.Wxid, Data.Towxids, Data.ChatRoom),
			}).Info("📁 使用专用文件缓存的联系人详情")

			return models.ResponseResult{
				Code:    0,
				Success: true,
				Message: fmt.Sprintf("成功（来自专用缓存，缓存时间：%s）", cacheData.CacheTime.Format("2006-01-02 15:04:05")),
				Data:    cacheData.Data,
			}
		} else {
			log.WithFields(log.Fields{
				"wxid":     Data.Wxid,
				"towxids":  Data.Towxids,
				"chat_room": Data.ChatRoom,
				"error":    err.Error(),
			}).Info("📁 专用缓存文件不存在或无效，将重新获取")
		}
	} else {
		log.WithFields(log.Fields{
			"wxid":     Data.Wxid,
			"towxids":  Data.Towxids,
			"chat_room": Data.ChatRoom,
		}).Info("🔄 强制刷新缓存，跳过缓存检查")
	}

	D, err := comm.GetLoginata(Data.Wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		errorMsg := fmt.Sprintf("异常：%v [%v]", "未找到登录信息", Data.Wxid)
		if err != nil {
			errorMsg = fmt.Sprintf("异常：%v", err.Error())
		}
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: errorMsg,
			Data:    nil,
		}
	}

	TowxdsSplit := strings.Split(Data.Towxids, ",")

	Towxds := make([]*mm.SKBuiltinStringT, len(TowxdsSplit))

	if len(TowxdsSplit) >= 1 {
		for i, v := range TowxdsSplit {
			Towxds[i] = &mm.SKBuiltinStringT{
				String_: proto.String(v),
			}
		}
	}

	ChatRoom := &mm.SKBuiltinStringT{}
	ChatRoomCount := uint32(1)

	if Data.ChatRoom != "" {
		ChatRoomCount = 1
		ChatRoom = &mm.SKBuiltinStringT{
			String_: proto.String(Data.ChatRoom),
		}
	} else {
		ChatRoom = nil
		ChatRoomCount = uint32(0)
	}

	req := &mm.GetContactRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		UserCount:         proto.Int32(int32(len(Towxds))),
		UserNameList:      Towxds,
		FromChatRoomCount: proto.Int32(int32(ChatRoomCount)),
		FromChatRoom:      ChatRoom,
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Ip:     D.Mmtlsip,
		Host:   D.ShortHost,
		Cgiurl: "/cgi-bin/micromsg-bin/getcontact",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              182,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.RsaPublicKey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.GetContactResponse{}
	err = proto.Unmarshal(protobufdata, &Response)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}

	// 默认总是保存缓存到文件
	if err := saveDetailCacheToFile(Data.Wxid, Data.Towxids, Data.ChatRoom, Response); err != nil {
		log.WithFields(log.Fields{
			"wxid":     Data.Wxid,
			"towxids":  Data.Towxids,
			"chat_room": Data.ChatRoom,
			"error":    err.Error(),
		}).Error("💾 保存联系人详情缓存到文件失败")
	} else {
		log.WithFields(log.Fields{
			"wxid":     Data.Wxid,
			"towxids":  Data.Towxids,
			"chat_room": Data.ChatRoom,
		}).Info("💾 联系人详情数据已缓存到文件")
	}

	return result
}
