//go:build linux && !cgo

package dynlib

import (
	"errors"
	"fmt"
)

// noCGOLibrary 是当CGO被禁用时的fallback实现
type noCGOLibrary struct{}

func init() {
	fmt.Println("Initializing linux_nocgo dynlib")
	// 当CGO被禁用时，使用这个fallback实现
	newLibrary = NewNoCGOLibrary
}

func NewNoCGOLibrary(path string) (DynamicLibrary, error) {
	return nil, errors.New("动态库加载需要CGO支持，当前CGO被禁用")
}

func (l *noCGOLibrary) Call(funcName string, args ...interface{}) (uintptr, uintptr, error) {
	return 0, 0, errors.New("动态库加载需要CGO支持")
}

func (l *noCGOLibrary) Close() error {
	return nil
}
