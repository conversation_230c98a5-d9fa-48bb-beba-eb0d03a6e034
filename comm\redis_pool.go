package comm

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis"
	log "github.com/sirupsen/logrus"
)

// OptimizedRedisConfig Redis优化配置
type OptimizedRedisConfig struct {
	Addr               string        `json:"addr"`                 // Redis地址
	Password           string        `json:"password"`             // 密码
	DB                 int           `json:"db"`                   // 数据库编号
	PoolSize           int           `json:"pool_size"`            // 连接池大小
	MinIdleConns       int           `json:"min_idle_conns"`       // 最小空闲连接数
	MaxRetries         int           `json:"max_retries"`          // 最大重试次数
	DialTimeout        time.Duration `json:"dial_timeout"`         // 拨号超时
	ReadTimeout        time.Duration `json:"read_timeout"`         // 读取超时
	WriteTimeout       time.Duration `json:"write_timeout"`        // 写入超时
	PoolTimeout        time.Duration `json:"pool_timeout"`         // 连接池超时
	IdleTimeout        time.Duration `json:"idle_timeout"`         // 空闲超时
	IdleCheckFrequency time.Duration `json:"idle_check_frequency"` // 空闲检查频率
	MaxConnAge         time.Duration `json:"max_conn_age"`         // 最大连接年龄
}

// DefaultOptimizedRedisConfig 默认Redis优化配置
var DefaultOptimizedRedisConfig = OptimizedRedisConfig{
	Addr:               "localhost:6379",
	Password:           "",
	DB:                 0,
	PoolSize:           20,
	MinIdleConns:       5,
	MaxRetries:         3,
	DialTimeout:        5 * time.Second,
	ReadTimeout:        3 * time.Second,
	WriteTimeout:       3 * time.Second,
	PoolTimeout:        4 * time.Second,
	IdleTimeout:        5 * time.Minute,
	IdleCheckFrequency: 1 * time.Minute,
	MaxConnAge:         30 * time.Minute,
}

// OptimizedRedisClient 优化的Redis客户端
type OptimizedRedisClient struct {
	client *redis.Client
	config OptimizedRedisConfig

	// 统计信息
	stats RedisStats
	mutex sync.RWMutex

	// 性能监控
	slowQueryThreshold time.Duration

	// 连接健康检查
	healthCheckInterval time.Duration
	isHealthy           bool
	lastHealthCheck     time.Time
}

// RedisStats Redis统计信息
type RedisStats struct {
	TotalCommands    int64         `json:"total_commands"`
	SuccessCommands  int64         `json:"success_commands"`
	FailedCommands   int64         `json:"failed_commands"`
	SlowQueries      int64         `json:"slow_queries"`
	AvgResponseTime  time.Duration `json:"avg_response_time"`
	ConnectionsInUse int           `json:"connections_in_use"`
	IdleConnections  int           `json:"idle_connections"`
	LastError        string        `json:"last_error"`
	LastErrorTime    time.Time     `json:"last_error_time"`
}

// NewOptimizedRedisClient 创建优化的Redis客户端
func NewOptimizedRedisClient(config OptimizedRedisConfig) *OptimizedRedisClient {
	client := redis.NewClient(&redis.Options{
		Addr:               config.Addr,
		Password:           config.Password,
		DB:                 config.DB,
		PoolSize:           config.PoolSize,
		MinIdleConns:       config.MinIdleConns,
		MaxRetries:         config.MaxRetries,
		DialTimeout:        config.DialTimeout,
		ReadTimeout:        config.ReadTimeout,
		WriteTimeout:       config.WriteTimeout,
		PoolTimeout:        config.PoolTimeout,
		IdleTimeout:        config.IdleTimeout,
		IdleCheckFrequency: config.IdleCheckFrequency,
		MaxConnAge:         config.MaxConnAge,
	})

	optimizedClient := &OptimizedRedisClient{
		client:              client,
		config:              config,
		slowQueryThreshold:  100 * time.Millisecond, // 100ms为慢查询阈值
		healthCheckInterval: 30 * time.Second,
		isHealthy:           true,
	}

	// 启动健康检查
	go optimizedClient.startHealthCheck()

	return optimizedClient
}

// executeWithStats 执行命令并记录统计信息
func (rc *OptimizedRedisClient) executeWithStats(operation string, fn func() error) error {
	startTime := time.Now()

	// 增加命令计数
	rc.mutex.Lock()
	rc.stats.TotalCommands++
	rc.mutex.Unlock()

	// 执行命令
	err := fn()

	// 记录执行时间
	duration := time.Since(startTime)

	// 更新统计信息
	rc.updateStats(err, duration, operation)

	return err
}

// updateStats 更新统计信息
func (rc *OptimizedRedisClient) updateStats(err error, duration time.Duration, operation string) {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	if err != nil {
		rc.stats.FailedCommands++
		rc.stats.LastError = fmt.Sprintf("%s: %v", operation, err)
		rc.stats.LastErrorTime = time.Now()
	} else {
		rc.stats.SuccessCommands++
	}

	// 检查慢查询
	if duration > rc.slowQueryThreshold {
		rc.stats.SlowQueries++
		log.Warnf("Redis慢查询检测: %s 耗时 %v", operation, duration)
	}

	// 更新平均响应时间
	if rc.stats.TotalCommands > 0 {
		rc.stats.AvgResponseTime = (rc.stats.AvgResponseTime + duration) / 2
	}
}

// Get 优化的GET操作
func (rc *OptimizedRedisClient) Get(key string) (string, error) {
	var result string
	var err error

	err = rc.executeWithStats("GET", func() error {
		result, err = rc.client.Get(key).Result()
		return err
	})

	return result, err
}

// Set 优化的SET操作
func (rc *OptimizedRedisClient) Set(key string, value interface{}, expiration time.Duration) error {
	return rc.executeWithStats("SET", func() error {
		return rc.client.Set(key, value, expiration).Err()
	})
}

// GetObject 获取JSON对象
func (rc *OptimizedRedisClient) GetObject(key string, obj interface{}) error {
	return rc.executeWithStats("GET_OBJECT", func() error {
		// 清理首尾空格
		key = strings.TrimSpace(key)
		if key == "" {
			return fmt.Errorf("key不能为空")
		}

		val, err := rc.client.Get(key).Result()
		if err != nil {
			return fmt.Errorf("Redis获取数据失败: %w", err)
		}

		if val == "" {
			return fmt.Errorf("key [%s] 对应的数据为空", key)
		}

		if err := json.Unmarshal([]byte(val), obj); err != nil {
			return fmt.Errorf("JSON反序列化失败: %w", err)
		}

		return nil
	})
}

// SetObject 设置JSON对象
func (rc *OptimizedRedisClient) SetObject(key string, obj interface{}, expiration time.Duration) error {
	return rc.executeWithStats("SET_OBJECT", func() error {
		data, err := json.Marshal(obj)
		if err != nil {
			return fmt.Errorf("JSON序列化失败: %w", err)
		}

		return rc.client.Set(key, data, expiration).Err()
	})
}

// Delete 删除键
func (rc *OptimizedRedisClient) Delete(keys ...string) error {
	return rc.executeWithStats("DELETE", func() error {
		return rc.client.Del(keys...).Err()
	})
}

// Exists 检查键是否存在
func (rc *OptimizedRedisClient) Exists(keys ...string) (int64, error) {
	var result int64
	var err error

	err = rc.executeWithStats("EXISTS", func() error {
		result, err = rc.client.Exists(keys...).Result()
		return err
	})

	return result, err
}

// Expire 设置过期时间
func (rc *OptimizedRedisClient) Expire(key string, expiration time.Duration) error {
	return rc.executeWithStats("EXPIRE", func() error {
		return rc.client.Expire(key, expiration).Err()
	})
}

// Pipeline 管道操作
func (rc *OptimizedRedisClient) Pipeline(fn func(redis.Pipeliner) error) error {
	return rc.executeWithStats("PIPELINE", func() error {
		pipe := rc.client.Pipeline()
		defer pipe.Close()

		if err := fn(pipe); err != nil {
			return err
		}

		_, err := pipe.Exec()
		return err
	})
}

// startHealthCheck 启动健康检查
func (rc *OptimizedRedisClient) startHealthCheck() {
	ticker := time.NewTicker(rc.healthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		rc.checkHealth()
	}
}

// checkHealth 检查连接健康状态
func (rc *OptimizedRedisClient) checkHealth() {
	err := rc.client.Ping().Err()

	rc.mutex.Lock()
	rc.isHealthy = (err == nil)
	rc.lastHealthCheck = time.Now()
	if err != nil {
		rc.stats.LastError = fmt.Sprintf("健康检查失败: %v", err)
		rc.stats.LastErrorTime = time.Now()
	}
	rc.mutex.Unlock()

	if err != nil {
		log.Errorf("Redis健康检查失败: %v", err)
	}
}

// GetStats 获取统计信息
func (rc *OptimizedRedisClient) GetStats() RedisStats {
	rc.mutex.RLock()
	defer rc.mutex.RUnlock()

	stats := rc.stats

	// 获取连接池统计（Redis v6版本的字段可能不同）
	poolStats := rc.client.PoolStats()
	stats.ConnectionsInUse = int(poolStats.TotalConns)
	stats.IdleConnections = int(poolStats.IdleConns)

	return stats
}

// IsHealthy 检查是否健康
func (rc *OptimizedRedisClient) IsHealthy() bool {
	rc.mutex.RLock()
	defer rc.mutex.RUnlock()
	return rc.isHealthy
}

// Close 关闭客户端
func (rc *OptimizedRedisClient) Close() error {
	return rc.client.Close()
}

// GetClient 获取原始客户端（用于高级操作）
func (rc *OptimizedRedisClient) GetClient() *redis.Client {
	return rc.client
}

// 全局优化Redis客户端
var (
	globalOptimizedRedisClient *OptimizedRedisClient
	optimizedRedisOnce         sync.Once
)

// GetOptimizedRedisClient 获取全局优化Redis客户端
func GetOptimizedRedisClient() *OptimizedRedisClient {
	optimizedRedisOnce.Do(func() {
		globalOptimizedRedisClient = NewOptimizedRedisClient(DefaultOptimizedRedisConfig)
	})
	return globalOptimizedRedisClient
}

// InitOptimizedRedis 初始化优化的Redis客户端
func InitOptimizedRedis(config OptimizedRedisConfig) {
	globalOptimizedRedisClient = NewOptimizedRedisClient(config)
	log.Infof("优化的Redis客户端已初始化: %s", config.Addr)
}
