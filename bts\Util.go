package bts

import (
	"wechatdll/Cilent/mm"
	"wechatdll/comm"

	log "github.com/sirupsen/logrus"
)

// convertResponse 通用的响应转换函数，减少重复代码
func convertResponse(data interface{}, target interface{}) error {
	jsonHelper := comm.GetJSONHelper()
	return jsonHelper.ConvertType(data, target)
}

// GetUserOpenIdResponse 获取用户OpenId响应转换
// 使用通用转换函数减少重复代码
func GetUserOpenIdResponse(data interface{}) mm.GetUserOpenIdResp {
	var result mm.GetUserOpenIdResp

	if err := convertResponse(data, &result); err != nil {
		log.Errorf("GetUserOpenIdResponse转换失败: %v", err)
		return mm.GetUserOpenIdResp{} // 返回空结构体而不是nil
	}

	return result
}

// SearchContactResponse 搜索联系人响应转换
// 使用通用转换函数减少重复代码
func SearchContactResponse(data interface{}) mm.SearchContactResponse {
	var result mm.SearchContactResponse

	if err := convertResponse(data, &result); err != nil {
		log.Errorf("SearchContactResponse转换失败: %v", err)
		return mm.SearchContactResponse{} // 返回空结构体而不是nil
	}

	return result
}

// GetContactResponse 获取联系人响应转换
// 使用通用转换函数减少重复代码
func GetContactResponse(data interface{}) mm.GetContactResponse {
	var result mm.GetContactResponse

	if err := convertResponse(data, &result); err != nil {
		log.Errorf("GetContactResponse转换失败: %v", err)
		return mm.GetContactResponse{} // 返回空结构体而不是nil
	}

	return result
}

// GetModContact 获取修改联系人响应转换
// 使用通用转换函数减少重复代码
func GetModContact(data interface{}) mm.ModContact {
	var result mm.ModContact

	if err := convertResponse(data, &result); err != nil {
		log.Errorf("GetModContact转换失败: %v", err)
		return mm.ModContact{} // 返回空结构体而不是nil
	}

	return result
}

// GetA8KeyResponse 获取A8Key响应转换
// 使用通用转换函数减少重复代码
func GetA8KeyResponse(data interface{}) mm.GetA8KeyResp {
	var result mm.GetA8KeyResp

	if err := convertResponse(data, &result); err != nil {
		log.Errorf("GetA8KeyResponse转换失败: %v", err)
		return mm.GetA8KeyResp{} // 返回空结构体而不是nil
	}

	return result
}

// GetProfile 获取用户资料响应转换
// 使用通用转换函数减少重复代码
func GetProfile(data interface{}) mm.GetProfileResponse {
	var result mm.GetProfileResponse

	if err := convertResponse(data, &result); err != nil {
		log.Errorf("GetProfile转换失败: %v", err)
		return mm.GetProfileResponse{} // 返回空结构体而不是nil
	}

	return result
}
