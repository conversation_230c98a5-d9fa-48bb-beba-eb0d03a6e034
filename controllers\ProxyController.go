package controllers

import (
	"encoding/json"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"
	"wechatdll/comm"
	"wechatdll/models/Proxy"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// ProxyController 代理管理控制器
type ProxyController struct {
	beego.Controller
}

// ImportProxies 批量导入代理
// @Title 批量导入代理
// @Description 批量导入SOCKS5代理列表
// @Param body body Proxy.ProxyImportRequest true "代理导入请求"
// @Success 200 {object} comm.BaseResponse
// @Failure 400 {object} comm.BaseResponse
// @router /ImportProxies [post]
func (c *ProxyController) ImportProxies() {
	var req Proxy.ProxyImportRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		comm.ResponseError(c.Ctx, "请求参数解析失败: "+err.Error())
		return
	}

	if req.ProxyList == "" {
		comm.ResponseError(c.Ctx, "代理列表不能为空")
		return
	}

	// 解析代理列表
	lines := strings.Split(req.ProxyList, "\n")
	var proxies []Proxy.ProxyInfo
	var errors []string
	successCount := 0

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		proxy, err := Proxy.ParseProxyLine(line)
		if err != nil {
			errors = append(errors, fmt.Sprintf("第%d行: %s", i+1, err.Error()))
			continue
		}

		// 设置ID
		proxy.ID = len(proxies) + 1
		proxies = append(proxies, *proxy)
		successCount++
	}

	// 保存到Redis
	if err := saveProxiesToRedis(proxies); err != nil {
		comm.ResponseError(c.Ctx, "保存代理失败: "+err.Error())
		return
	}

	// 返回结果
	result := map[string]interface{}{
		"success_count": successCount,
		"error_count":   len(errors),
		"errors":        errors,
		"total_proxies": len(proxies),
	}

	log.Infof("代理导入完成: 成功%d个，失败%d个", successCount, len(errors))
	comm.ResponseSuccess(c.Ctx, result, "代理导入完成")
}

// GetProxyList 获取代理列表
// @Title 获取代理列表
// @Description 获取代理列表，支持分页和筛选
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "状态筛选"
// @Param country query string false "地区筛选"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} Proxy.ProxyListResponse
// @router /GetProxyList [get]
func (c *ProxyController) GetProxyList() {
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)
	status := c.GetString("status")
	country := c.GetString("country")
	keyword := c.GetString("keyword")

	// 从Redis获取代理列表
	proxies, err := getProxiesFromRedis()
	if err != nil {
		comm.ResponseError(c.Ctx, "获取代理列表失败: "+err.Error())
		return
	}

	// 筛选
	var filteredProxies []Proxy.ProxyInfo
	for _, proxy := range proxies {
		// 状态筛选
		if status != "" && proxy.Status != status {
			continue
		}

		// 地区筛选
		if country != "" && proxy.Country != country {
			continue
		}

		// 关键词搜索
		if keyword != "" {
			if !strings.Contains(proxy.IP, keyword) &&
				!strings.Contains(proxy.Username, keyword) &&
				!strings.Contains(proxy.Country, keyword) {
				continue
			}
		}

		filteredProxies = append(filteredProxies, proxy)
	}

	// 分页
	total := len(filteredProxies)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	var pageProxies []Proxy.ProxyInfo
	if start < end {
		pageProxies = filteredProxies[start:end]
	}

	// 构造响应
	response := Proxy.ProxyListResponse{
		Code:    0,
		Message: "获取成功",
		Data: Proxy.ProxyData{
			List:       pageProxies,
			Total:      total,
			Page:       page,
			PageSize:   pageSize,
			TotalPages: (total + pageSize - 1) / pageSize,
		},
	}

	c.Data["json"] = response
	c.ServeJSON()
}

// GetProxyStats 获取代理统计
// @Title 获取代理统计
// @Description 获取代理统计信息
// @Success 200 {object} Proxy.ProxyStatsResponse
// @router /GetProxyStats [get]
func (c *ProxyController) GetProxyStats() {
	proxies, err := getProxiesFromRedis()
	if err != nil {
		comm.ResponseError(c.Ctx, "获取代理统计失败: "+err.Error())
		return
	}

	stats := Proxy.ProxyStats{}
	for _, proxy := range proxies {
		stats.Total++
		switch proxy.Status {
		case "active":
			stats.Active++
		case "inactive":
			stats.Inactive++
		case "testing":
			stats.Testing++
		case "error":
			stats.Error++
		}
	}

	response := Proxy.ProxyStatsResponse{
		Code:    0,
		Message: "获取成功",
		Data:    stats,
	}

	c.Data["json"] = response
	c.ServeJSON()
}

// TestProxies 测试代理连接
// @Title 测试代理连接
// @Description 测试指定代理的连接状态
// @Param body body Proxy.ProxyTestRequest true "代理测试请求"
// @Success 200 {object} comm.BaseResponse
// @router /TestProxies [post]
func (c *ProxyController) TestProxies() {
	var req Proxy.ProxyTestRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		comm.ResponseError(c.Ctx, "请求参数解析失败: "+err.Error())
		return
	}

	if len(req.ProxyIDs) == 0 {
		comm.ResponseError(c.Ctx, "请选择要测试的代理")
		return
	}

	// 获取代理列表
	proxies, err := getProxiesFromRedis()
	if err != nil {
		comm.ResponseError(c.Ctx, "获取代理列表失败: "+err.Error())
		return
	}

	// 异步测试代理
	go func() {
		for _, proxyID := range req.ProxyIDs {
			for i, proxy := range proxies {
				if proxy.ID == proxyID {
					proxies[i].Status = "testing"
					proxies[i].LastTest = time.Now()

					// 测试代理连接
					responseTime, err := testProxyConnection(proxy)
					if err != nil {
						proxies[i].Status = "error"
						proxies[i].ResponseTime = 0
						log.Warnf("代理测试失败 %s:%d - %v", proxy.IP, proxy.Port, err)
					} else {
						proxies[i].Status = "active"
						proxies[i].ResponseTime = responseTime
						log.Infof("代理测试成功 %s:%d - %dms", proxy.IP, proxy.Port, responseTime)
					}

					if country := Proxy.GetCountryByIP(proxy.IP); country != "" {
						proxies[i].Country = country
						log.Infof("代理地区识别成功 %s:%d - %s", proxy.IP, proxy.Port, country)
					}

					proxies[i].UpdateTime = time.Now()
					break
				}
			}
		}

		// 保存更新后的代理列表
		saveProxiesToRedis(proxies)
	}()

	comm.ResponseSuccess(c.Ctx, nil, "代理测试已开始，请稍后查看结果")
}

// DeleteProxy 删除代理
// @Title 删除代理
// @Description 删除指定的代理
// @Param id path int true "代理ID"
// @Success 200 {object} comm.BaseResponse
// @router /DeleteProxy/:id [delete]
func (c *ProxyController) DeleteProxy() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		comm.ResponseError(c.Ctx, "无效的代理ID")
		return
	}

	proxies, err := getProxiesFromRedis()
	if err != nil {
		comm.ResponseError(c.Ctx, "获取代理列表失败: "+err.Error())
		return
	}

	// 删除指定代理
	var newProxies []Proxy.ProxyInfo
	found := false
	for _, proxy := range proxies {
		if proxy.ID != id {
			newProxies = append(newProxies, proxy)
		} else {
			found = true
		}
	}

	if !found {
		comm.ResponseError(c.Ctx, "代理不存在")
		return
	}

	// 保存更新后的列表
	if err := saveProxiesToRedis(newProxies); err != nil {
		comm.ResponseError(c.Ctx, "删除代理失败: "+err.Error())
		return
	}

	comm.ResponseSuccess(c.Ctx, nil, "代理删除成功")
}

// ClearProxies 清空所有代理
// @Title 清空所有代理
// @Description 清空所有代理数据
// @Success 200 {object} comm.BaseResponse
// @router /ClearProxies [post]
func (c *ProxyController) ClearProxies() {
	if err := comm.RedisClient.Del("proxy_list").Err(); err != nil {
		comm.ResponseError(c.Ctx, "清空代理失败: "+err.Error())
		return
	}

	comm.ResponseSuccess(c.Ctx, nil, "代理清空成功")
}

// saveProxiesToRedis 保存代理列表到Redis
func saveProxiesToRedis(proxies []Proxy.ProxyInfo) error {
	data, err := json.Marshal(proxies)
	if err != nil {
		return err
	}

	return comm.RedisClient.Set("proxy_list", string(data), 0).Err()
}

// getProxiesFromRedis 从Redis获取代理列表
func getProxiesFromRedis() ([]Proxy.ProxyInfo, error) {
	data, err := comm.RedisClient.Get("proxy_list").Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return []Proxy.ProxyInfo{}, nil
		}
		return nil, err
	}

	var proxies []Proxy.ProxyInfo
	if err := json.Unmarshal([]byte(data), &proxies); err != nil {
		return nil, err
	}

	return proxies, nil
}

// testProxyConnection 测试代理连接
func testProxyConnection(proxy Proxy.ProxyInfo) (int, error) {
	start := time.Now()

	// 简单的连接测试
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", proxy.IP, proxy.Port), 10*time.Second)
	if err != nil {
		return 0, err
	}
	defer conn.Close()

	responseTime := int(time.Since(start).Milliseconds())
	return responseTime, nil
}

// UpdateProxyCountries 批量更新代理地区信息
// @Title 批量更新代理地区信息
// @Description 为所有没有地区信息的代理自动识别地区
// @Success 200 {object} comm.BaseResponse
// @router /UpdateProxyCountries [post]
func (c *ProxyController) UpdateProxyCountries() {
	// 获取代理列表
	proxies, err := getProxiesFromRedis()
	if err != nil {
		comm.ResponseError(c.Ctx, "获取代理列表失败: "+err.Error())
		return
	}

	// 异步更新地区信息
	go func() {
		updatedCount := 0
		for i, proxy := range proxies {
				if country := Proxy.GetCountryByIP(proxy.IP); country != "" {
					proxies[i].Country = country
					proxies[i].UpdateTime = time.Now()
					updatedCount++
					log.Infof("代理地区更新成功 %s:%d - %s", proxy.IP, proxy.Port, country)
				}
		}

		// 保存更新后的代理列表
		if updatedCount > 0 {
			saveProxiesToRedis(proxies)
			log.Infof("批量更新代理地区完成，共更新 %d 个代理", updatedCount)
		}
	}()

	comm.ResponseSuccess(c.Ctx, nil, "代理地区信息更新已开始，请稍后查看结果")
}
