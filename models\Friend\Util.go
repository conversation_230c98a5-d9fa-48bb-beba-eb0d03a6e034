package Friend

type DefaultParam struct {
	Wxid   string
	ToWxid string
}

type BlacklistParam struct {
	Wxid   string
	ToWxid string
	Val    uint32
}

type LbsFindParam struct {
	Wxid   string
	Latitude float32
	Longitude float32
	OpCode uint32
}

type GetContractListparameter struct {
	Wxid                      string
	CurrentWxcontactSeq       int32
	CurrentChatRoomContactSeq int32
	ForceRefresh              bool `json:"force_refresh,omitempty"` // 强制刷新缓存，默认false
}

type GetContractDetailparameter struct {
	Wxid         string
	Towxids      string
	ChatRoom     string
	ForceRefresh bool `json:"force_refresh,omitempty"` // 强制刷新缓存，默认false
}

type GetContactListParams struct {
	Wxid                      string
	CurrentWxcontactSeq       int32
	CurrentChatRoomContactSeq int32
	Offset                    int32 // 偏移量参数
	Limit                     int32 // 限制数量参数
	ForceRefresh              bool  `json:"force_refresh,omitempty"` // 强制刷新缓存
}

