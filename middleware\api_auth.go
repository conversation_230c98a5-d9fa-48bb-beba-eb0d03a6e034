package middleware

import (
	"crypto/subtle"
	"strings"
	"sync"
	"time"
	"wechatdll/comm"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/context"
	log "github.com/sirupsen/logrus"
)

// 会话缓存，避免重复验证和日志记录
var (
	sessionCache = make(map[string]time.Time)
	cacheMutex   sync.RWMutex
	cacheExpiry  = 5 * time.Minute // 5分钟过期
)

// APIKeyAuthMiddleware API密钥认证中间件
// 用于保护API文档页面和敏感接口
func APIKeyAuthMiddleware(ctx *context.Context) {
	// 检查是否启用API密钥验证
	if !comm.GetUnifiedAPIKeyEnabled() {
		return
	}

	// 获取配置的API密钥
	configuredKey := comm.GetUnifiedAPIKey()
	if configuredKey == "" {
		log.Error("API密钥未配置")
		sendUnauthorizedResponse(ctx, "API密钥配置错误")
		return
	}

	// 从多个来源获取API密钥，优先从Cookie获取
	var providedKey string

	// 1. 从Cookie中获取（优先级最高）
	if cookie, err := ctx.Request.Cookie("api_key"); err == nil {
		providedKey = cookie.Value
	}

	// 3. 从Query参数中获取
	if providedKey == "" {
		providedKey = ctx.Input.Query("api_key")
	}

	// 4. 从POST参数中获取
	if providedKey == "" {
		providedKey = ctx.Input.Query("apikey")
	}

	// 验证密钥
	if providedKey == "" {
		log.WithFields(log.Fields{
			"ip":   ctx.Input.IP(),
			"path": ctx.Request.URL.Path,
		}).Warn("API密钥缺失")
		sendUnauthorizedResponse(ctx, "缺少API密钥")
		return
	}

	// 使用常量时间比较防止时序攻击
	if subtle.ConstantTimeCompare([]byte(providedKey), []byte(configuredKey)) != 1 {
		// 如果密钥验证失败，清除可能存在的旧Cookie
		ctx.SetCookie("api_key", "", -1, "/", "", false, true) // 设置过期时间为-1来删除Cookie

		log.WithFields(log.Fields{
			"ip":   ctx.Input.IP(),
			"path": ctx.Request.URL.Path,
			"key":  maskKey(providedKey),
		}).Warn("API密钥验证失败，已清除旧Cookie")
		sendUnauthorizedResponse(ctx, "API密钥无效")
		return
	}

	// 验证成功，记录日志
	log.WithFields(log.Fields{
		"ip":   ctx.Input.IP(),
		"path": ctx.Request.URL.Path,
	}).Info("API密钥验证成功")
}

// APIDocAuthMiddleware API文档页面认证中间件
// 专门用于保护API文档页面访问
func APIDocAuthMiddleware(ctx *context.Context) {
	// 检查是否启用API文档密钥验证
	if !comm.GetUnifiedAPIKeyEnabled() {
		return
	}

	// 检查会话缓存，避免重复验证
	clientIP := ctx.Input.IP()
	if isSessionValid(clientIP) {
		return // 会话有效，直接通过
	}

	// 获取配置的API文档密钥
	configuredKey := comm.GetUnifiedAPIKey()
	if configuredKey == "" {
		log.Error("API文档密钥未配置")
		sendDocUnauthorizedResponse(ctx)
		return
	}

	// 从多个来源获取API密钥，优先从Cookie获取
	var providedKey string

	// 1. 从Cookie中获取（优先级最高）
	if cookie, err := ctx.Request.Cookie("api_key"); err == nil {
		providedKey = cookie.Value
	}

	// 3. 从Query参数中获取
	if providedKey == "" {
		providedKey = ctx.Input.Query("api_key")
	}

	// 验证密钥
	if providedKey == "" {
		log.WithFields(log.Fields{
			"ip":   ctx.Input.IP(),
			"path": ctx.Request.URL.Path,
		}).Warn("API文档访问：密钥缺失")
		sendDocUnauthorizedResponse(ctx)
		return
	}

	// 使用常量时间比较防止时序攻击
	if subtle.ConstantTimeCompare([]byte(providedKey), []byte(configuredKey)) != 1 {
		// 如果密钥验证失败，清除可能存在的旧Cookie
		ctx.SetCookie("api_key", "", -1, "/", "", false, true) // 设置过期时间为-1来删除Cookie

		log.WithFields(log.Fields{
			"ip":   ctx.Input.IP(),
			"path": ctx.Request.URL.Path,
			"key":  maskKey(providedKey),
		}).Warn("API文档访问：密钥验证失败，已清除旧Cookie")
		sendDocUnauthorizedResponse(ctx)
		return
	}

	// 验证成功，设置Cookie以便后续访问
	ctx.SetCookie("api_key", providedKey, 3600*24, "/", "", false, true) // 24小时有效期

	// 更新会话缓存
	updateSession(clientIP)

	// 只对主要页面记录日志，避免静态资源重复记录
	if shouldLogAccess(ctx.Request.URL.Path) {
		log.WithFields(log.Fields{
			"ip":   ctx.Input.IP(),
			"path": ctx.Request.URL.Path,
		}).Info("API文档访问：密钥验证成功")
	}
}

// sendUnauthorizedResponse 发送未授权响应（JSON格式）
func sendUnauthorizedResponse(ctx *context.Context, message string) {
	response := map[string]interface{}{
		"code":    401,
		"success": false,
		"message": message,
		"data":    nil,
	}

	ctx.Output.SetStatus(401)
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	ctx.Output.JSON(response, false, false)
}

// sendDocUnauthorizedResponse 发送API文档页面未授权响应（重定向到验证页面）
func sendDocUnauthorizedResponse(ctx *context.Context) {
	// 获取当前请求的URL作为重定向参数
	currentURL := ctx.Request.URL.String()
	authURL := "/api/auth?redirect=" + currentURL

	// 重定向到验证页面
	ctx.Redirect(302, authURL)
}

// isSessionValid 检查会话是否有效
func isSessionValid(clientIP string) bool {
	cacheMutex.RLock()
	defer cacheMutex.RUnlock()

	if lastAccess, exists := sessionCache[clientIP]; exists {
		return time.Since(lastAccess) < cacheExpiry
	}
	return false
}

// updateSession 更新会话缓存
func updateSession(clientIP string) {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	sessionCache[clientIP] = time.Now()

	// 清理过期的会话（简单的清理策略）
	if len(sessionCache) > 100 { // 当缓存超过100个条目时清理
		for ip, lastAccess := range sessionCache {
			if time.Since(lastAccess) > cacheExpiry {
				delete(sessionCache, ip)
			}
		}
	}
}

// shouldLogAccess 判断是否应该记录访问日志
// 只对主要页面记录，避免静态资源重复记录
func shouldLogAccess(path string) bool {
	// 只对主要页面记录日志
	mainPaths := []string{
		"/",             // 主API文档页面
		"/swagger.json", // API规范数据
	}

	for _, mainPath := range mainPaths {
		if path == mainPath {
			return true
		}
	}

	return false
}

// maskKey 遮蔽密钥用于日志记录
func maskKey(key string) string {
	if len(key) <= 8 {
		return strings.Repeat("*", len(key))
	}
	return key[:4] + strings.Repeat("*", len(key)-8) + key[len(key)-4:]
}

// CreateAPIDocAuthMiddleware 创建自定义API文档认证中间件
// 允许使用指定的密钥进行验证
func CreateAPIDocAuthMiddleware(customKey string) func(*context.Context) {
	return func(ctx *context.Context) {
		// 检查会话缓存，避免重复验证
		clientIP := ctx.Input.IP()
		if isSessionValid(clientIP) {
			return // 会话有效，直接通过
		}

		// 使用传入的自定义密钥
		configuredKey := customKey
		if configuredKey == "" {
			log.Error("API文档密钥未配置")
			sendDocUnauthorizedResponse(ctx)
			return
		}

		// 从多个来源获取API密钥，优先从Cookie获取
		var providedKey string

		// 1. 从Cookie中获取（优先级最高）
		if cookie, err := ctx.Request.Cookie("api_key"); err == nil {
			providedKey = cookie.Value
		}

		// 2. 从Query参数中获取
		if providedKey == "" {
			providedKey = ctx.Input.Query("api_key")
		}

		// 验证密钥
		if providedKey == "" {
			log.WithFields(log.Fields{
				"ip":   ctx.Input.IP(),
				"path": ctx.Request.URL.Path,
			}).Warn("API文档访问：密钥缺失")
			sendDocUnauthorizedResponse(ctx)
			return
		}

		// 使用常量时间比较防止时序攻击
		if subtle.ConstantTimeCompare([]byte(providedKey), []byte(configuredKey)) != 1 {
			// 如果密钥验证失败，清除可能存在的旧Cookie
			ctx.SetCookie("api_key", "", -1, "/", "", false, true) // 设置过期时间为-1来删除Cookie

			log.WithFields(log.Fields{
				"ip":   ctx.Input.IP(),
				"path": ctx.Request.URL.Path,
				"key":  maskKey(providedKey),
			}).Warn("API文档访问：密钥验证失败，已清除旧Cookie")
			sendDocUnauthorizedResponse(ctx)
			return
		}

		// 验证成功，设置Cookie以便后续访问
		// 检查是否有remember_me参数
		rememberMe := ctx.Input.Query("remember_me") == "true"
		cookieExpiry := 3600 * 24 // 默认24小时
		if rememberMe {
			cookieExpiry = 3600 * 24 * 30 // 记住密钥30天
		}

		// 设置Cookie（根据环境决定是否使用Secure标志）
		isProduction := beego.BConfig.RunMode == "prod"
		ctx.SetCookie("api_key", providedKey, cookieExpiry, "/", "", isProduction, true)

		// 更新会话缓存
		updateSession(clientIP)

		// 只对主要页面记录日志，避免静态资源重复记录
		if shouldLogAccess(ctx.Request.URL.Path) {
			log.WithFields(log.Fields{
				"ip":   ctx.Input.IP(),
				"path": ctx.Request.URL.Path,
			}).Info("API文档访问：密钥验证成功")
		}
	}
}
