package comm

import (
	"fmt"
	"log"
	"time"
)

// HeartBeatStarter 心跳启动器接口
// 用于避免循环导入问题
type HeartBeatStarter interface {
	StartAutoHeartBeat(wxid string) error
}

// 全局心跳启动器实例
var globalHeartBeatStarter HeartBeatStarter

// SetHeartBeatStarter 设置心跳启动器
func SetHeartBeatStarter(starter HeartBeatStarter) {
	globalHeartBeatStarter = starter
}

// AutoStartHeartBeat 自动启动心跳功能
// 在用户登录成功后自动调用此函数来开启心跳
func AutoStartHeartBeat(wxid string) error {
	if wxid == "" {
		return fmt.Errorf("wxid不能为空")
	}

	// 延迟启动心跳，给登录流程一些时间完成
	go func() {
		// 等待3秒确保登录数据已经保存
		time.Sleep(3 * time.Second)

		// 记录日志
		log.Printf("[AutoHeartBeat] 正在为用户 %s 启动自动心跳...", wxid)

		// 调用心跳接口
		err := callAutoHeartBeatAPI(wxid)
		if err != nil {
			log.Printf("[AutoHeartBeat] 为用户 %s 启动自动心跳失败: %v", wxid, err)
			// 记录到心跳日志中
			AutoHeartBeatListAdd(wxid, fmt.Sprintf("[%s] 自动启动心跳失败: %v",
				time.Now().Format("2006-01-02 15:04:05"), err.Error()))
		} else {
			log.Printf("[AutoHeartBeat] 为用户 %s 启动自动心跳成功", wxid)
			// 记录到心跳日志中
			AutoHeartBeatListAdd(wxid, fmt.Sprintf("[%s] 登录成功后自动启动心跳成功",
				time.Now().Format("2006-01-02 15:04:05")))
		}
	}()

	return nil
}

// callAutoHeartBeatAPI 调用心跳API接口
func callAutoHeartBeatAPI(wxid string) error {
	// 获取登录数据
	D, err := GetLoginata(wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		return fmt.Errorf("未找到登录信息: %v", err)
	}

	// 使用全局心跳启动器
	if globalHeartBeatStarter == nil {
		return fmt.Errorf("心跳启动器未初始化")
	}

	return globalHeartBeatStarter.StartAutoHeartBeat(wxid)
}
