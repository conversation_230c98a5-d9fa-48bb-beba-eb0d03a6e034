package TcpPoll

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// ==================== 通知管理 ====================

// SendNotificationToWebSocket 发送通知到WebSocket连接
func SendNotificationToWebSocket(wxid string, notification map[string]interface{}) {
	// 检查连接管理器是否初始化
	if connManager == nil {
		fmt.Printf("[NOTIFICATION_ERROR] 连接管理器未初始化\n")
		return
	}

	// 发送到WebSocket连接
	conn, exists := connManager.GetClient(wxid)

	if exists {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("[NOTIFICATION_ERROR] 发送WebSocket通知时发生错误: %v\n", r)
				}
			}()

			if err := conn.WriteJSON(notification); err != nil {
				fmt.Printf("[NOTIFICATION_ERROR] 发送WebSocket通知失败: %v\n", err)
			} else {
				fmt.Printf("[NOTIFICATION] 已发送通知到WebSocket: wxid=%s, 类型=%s\n",
					wxid, notification["type"])
			}
		}()
	} else {
		fmt.Printf("[NOTIFICATION_DEBUG] 未找到WebSocket连接: wxid=%s\n", wxid)
	}
}

// ==================== 红包通知相关 ====================

// parseRedPacketResult 解析红包领取结果
func parseRedPacketResult(response map[string]interface{}) map[string]interface{} {
	result := map[string]interface{}{
		"success": false,
		"amount":  0,
		"message": "解析失败",
	}

	// 尝试从Data.retText.buffer中解析
	if data, ok := response["Data"].(map[string]interface{}); ok {
		if retText, ok := data["retText"].(map[string]interface{}); ok {
			if buffer, ok := retText["buffer"].(string); ok {
				// Base64解码
				decoded, err := base64DecodeString(buffer)
				if err == nil {
					// 解析JSON
					var jsonData map[string]interface{}
					if json.Unmarshal([]byte(decoded), &jsonData) == nil {
						// 提取关键信息
						if retcode, ok := jsonData["retcode"].(float64); ok && retcode == 0 {
							result["success"] = true
							result["message"] = "红包领取成功"

							if amount, ok := jsonData["amount"].(float64); ok {
								result["amount"] = int(amount)
							}

							if wishing, ok := jsonData["wishing"].(string); ok {
								result["wishing"] = wishing
							}

							if sendUserName, ok := jsonData["sendUserName"].(string); ok {
								result["sendUserName"] = sendUserName
							}

							if sendId, ok := jsonData["sendId"].(string); ok {
								result["sendId"] = sendId
							}
						}
					}
				}
			}
		}
	}

	return result
}

// sendRedPacketNotification 发送红包领取通知到WebSocket
func sendRedPacketNotification(wxid string, redpacketInfo map[string]interface{}) {
	// 构造通知消息
	notification := map[string]interface{}{
		"type":      "redpacket_received",
		"wxid":      wxid,
		"timestamp": time.Now().Unix(),
		"data":      redpacketInfo,
	}

	// 发送到WebSocket连接
	SendNotificationToWebSocket(wxid, notification)

	// 记录详细信息
	if success, ok := redpacketInfo["success"].(bool); ok && success {
		amount := redpacketInfo["amount"]
		wishing := redpacketInfo["wishing"]
		sendUserName := redpacketInfo["sendUserName"]

		fmt.Printf("[AUTO_REDPACKET_SUCCESS] 🧧 红包领取成功详情:\n")
		fmt.Printf("  - 微信ID: %s\n", wxid)
		fmt.Printf("  - 金额: %v分\n", amount)
		fmt.Printf("  - 祝福语: %v\n", wishing)
		fmt.Printf("  - 发送者: %v\n", sendUserName)
		fmt.Printf("  - 时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	}
}

// ==================== 辅助函数 ====================

// base64DecodeString Base64解码（从AutoFeatures.go移动过来避免重复）
func base64DecodeString(s string) (string, error) {
	// 尝试标准Base64解码
	if decoded, err := base64.StdEncoding.DecodeString(s); err == nil {
		return string(decoded), nil
	}

	// 尝试URL安全的Base64解码
	if decoded, err := base64.URLEncoding.DecodeString(s); err == nil {
		return string(decoded), nil
	}

	// 尝试无填充的Base64解码
	if decoded, err := base64.RawStdEncoding.DecodeString(s); err == nil {
		return string(decoded), nil
	}

	return "", fmt.Errorf("无法解码Base64字符串")
}

// getStringFromMap 从嵌套map中获取字符串值（从AutoFeatures.go移动过来避免重复）
func getStringFromMap(data map[string]interface{}, path string) string {
	keys := strings.Split(path, ".")
	current := data

	for i, key := range keys {
		if i == len(keys)-1 {
			if val, ok := current[key].(string); ok {
				return val
			}
		} else {
			if next, ok := current[key].(map[string]interface{}); ok {
				current = next
			} else {
				return ""
			}
		}
	}
	return ""
}
