package TcpPoll

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// ==================== 自动收款确认功能 ====================

// ProcessCollectMoneyMessage 处理收款消息并自动确认
func ProcessCollectMoneyMessage(wxid string, msg map[string]interface{}) {
	// 检查是否开启了自动收款确认
	if !GetAutoCollectMoneyStatus(wxid) {
		return
	}
	processCollectMoneyMessageInternal(wxid, msg)
}

// ProcessCollectMoneyMessageWithStatus 处理收款消息并自动确认（带预设状态）
func ProcessCollectMoneyMessageWithStatus(wxid string, msg map[string]interface{}, enabled bool) {
	if !enabled {
		return
	}
	processCollectMoneyMessageInternal(wxid, msg)
}

// processCollectMoneyMessageInternal 内部收款处理逻辑
func processCollectMoneyMessageInternal(wxid string, msg map[string]interface{}) {

	// 检查消息类型 - 转账消息通常是49类型
	msgType := msg["msgType"]
	var isMsgType49 bool
	switch v := msgType.(type) {
	case int32:
		isMsgType49 = v == 49
	case int:
		isMsgType49 = v == 49
	case uint32:
		isMsgType49 = v == 49
	case float64:
		isMsgType49 = int(v) == 49
	default:
		return
	}

	if !isMsgType49 {
		return
	}

	// 检查内容类型
	contentType, ok := msg["contentType"].(string)
	if !ok || contentType != "link" {
		return
	}

	// 检查额外数据
	extraData, ok := msg["extraData"].(map[string]interface{})
	if !ok {
		return
	}

	_, ok = extraData["xmlData"].(map[string]interface{})
	if !ok {
		return
	}

	// 检查原始内容中是否包含转账关键词
	originalContent, ok := msg["originalContent"].(string)
	if !ok {
		return
	}

	// 检查是否包含转账相关关键词
	isTransfer := strings.Contains(originalContent, "微信转账") ||
		strings.Contains(originalContent, "transferid") ||
		strings.Contains(originalContent, "transcationid") ||
		strings.Contains(originalContent, "invalidtime") ||
		strings.Contains(originalContent, "begintransfertime")

	if !isTransfer {
		return
	}

	fmt.Printf("[AUTO_COLLECT] 检测到转账消息，开始自动确认收款: wxid=%s\n", wxid)

	// 获取发送者信息
	fromUser := ""
	if fromUserVal, ok := msg["fromUser"].(string); ok {
		fromUser = fromUserVal
	}

	// 执行自动收款确认
	err := autoConfirmCollectMoneyWithSender(wxid, originalContent, fromUser)
	if err != nil {
		fmt.Printf("[AUTO_COLLECT] 自动确认收款失败: %v\n", err)
	} else {
		fmt.Printf("[AUTO_COLLECT] 自动确认收款成功: wxid=%s\n", wxid)
	}
}

// autoConfirmCollectMoney 自动确认收款
func autoConfirmCollectMoney(wxid, xmlContent string) error {
	return autoConfirmCollectMoneyWithSender(wxid, xmlContent, "")
}

// autoConfirmCollectMoneyWithSender 自动确认收款（带发送者信息）
func autoConfirmCollectMoneyWithSender(wxid, xmlContent, fromUser string) error {
	fmt.Printf("[AUTO_COLLECT] 开始自动确认收款: wxid=%s, 发送者=%s\n", wxid, fromUser)

	// 解析XML内容，提取转账信息
	transferInfo := extractTransferInfoFromXML(xmlContent)
	if transferInfo == nil {
		return fmt.Errorf("无法从XML中提取转账信息")
	}

	// 如果XML中没有发送者信息，使用传入的fromUser
	if transferInfo["tousername"] == "" && fromUser != "" {
		transferInfo["tousername"] = fromUser
	}

	fmt.Printf("[AUTO_COLLECT] 提取到转账信息: 金额=%s, 备注=%s, 发送者=%s\n",
		transferInfo["feedesc"], transferInfo["pay_memo"], transferInfo["tousername"])

	// 构造收款确认请求参数
	requestData := map[string]interface{}{
		"Wxid":          wxid,
		"InvalidTime":   transferInfo["invalidtime"],
		"TransFerId":    transferInfo["transferid"],
		"TransactionId": transferInfo["transcationid"],
		"ToUserName":    transferInfo["tousername"],
	}

	// 将请求数据转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("构造请求数据失败: %v", err)
	}

	fmt.Printf("[AUTO_COLLECT] 收款确认请求: 转账ID=%s, 交易ID=%s\n",
		transferInfo["transferid"], transferInfo["transcationid"])

	// 发送HTTP请求到收款确认API
	resp, err := http.Post("http://localhost:8059/api/TenPay/Collectmoney", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Errorf("发送收款确认请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("收款确认请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err == nil {
		if success, ok := response["Success"].(bool); ok && success {
			fmt.Printf("[AUTO_COLLECT] 收款确认API调用成功\n")

			// 发送WebSocket通知
			sendCollectMoneyNotification(wxid, transferInfo, response)
		} else {
			message := "未知错误"
			if msg, ok := response["Message"].(string); ok {
				message = msg
			}
			return fmt.Errorf("收款确认失败: %s", message)
		}
	}

	return nil
}

// extractTransferInfoFromXML 从XML中提取转账信息
func extractTransferInfoFromXML(xmlContent string) map[string]string {
	result := make(map[string]string)

	// 提取transferid
	if start := strings.Index(xmlContent, "<transferid><![CDATA["); start != -1 {
		start += len("<transferid><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></transferid>"); end != -1 {
			result["transferid"] = xmlContent[start : start+end]
		}
	}

	// 提取transcationid
	if start := strings.Index(xmlContent, "<transcationid><![CDATA["); start != -1 {
		start += len("<transcationid><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></transcationid>"); end != -1 {
			result["transcationid"] = xmlContent[start : start+end]
		}
	}

	// 提取invalidtime
	if start := strings.Index(xmlContent, "<invalidtime><![CDATA["); start != -1 {
		start += len("<invalidtime><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></invalidtime>"); end != -1 {
			result["invalidtime"] = xmlContent[start : start+end]
		}
	}

	// 提取转账备注 (pay_memo)
	if start := strings.Index(xmlContent, "<pay_memo><![CDATA["); start != -1 {
		start += len("<pay_memo><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></pay_memo>"); end != -1 {
			result["pay_memo"] = xmlContent[start : start+end]
		}
	}

	// 提取转账金额描述 (feedesc)
	if start := strings.Index(xmlContent, "<feedesc><![CDATA["); start != -1 {
		start += len("<feedesc><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></feedesc>"); end != -1 {
			result["feedesc"] = xmlContent[start : start+end]
		}
	}

	// 提取发送者信息 - 从payer_username或receiver_username判断
	if start := strings.Index(xmlContent, "<payer_username><![CDATA["); start != -1 {
		start += len("<payer_username><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></payer_username>"); end != -1 {
			payerUsername := xmlContent[start : start+end]
			if payerUsername != "" {
				result["tousername"] = payerUsername
			}
		}
	}

	// 如果payer_username为空，尝试从receiver_username获取
	if result["tousername"] == "" {
		if start := strings.Index(xmlContent, "<receiver_username><![CDATA["); start != -1 {
			start += len("<receiver_username><![CDATA[")
			if end := strings.Index(xmlContent[start:], "]]></receiver_username>"); end != -1 {
				receiverUsername := xmlContent[start : start+end]
				if receiverUsername != "" {
					result["tousername"] = receiverUsername
				}
			}
		}
	}

	// 如果还是没有，尝试从fromusername获取
	if result["tousername"] == "" {
		if start := strings.Index(xmlContent, "<fromusername><![CDATA["); start != -1 {
			start += len("<fromusername><![CDATA[")
			if end := strings.Index(xmlContent[start:], "]]></fromusername>"); end != -1 {
				result["tousername"] = xmlContent[start : start+end]
			}
		}
	}

	// 检查是否提取到了必要信息
	if result["transferid"] == "" || result["transcationid"] == "" {
		return nil
	}

	return result
}

// sendCollectMoneyNotification 发送收款确认通知到WebSocket
func sendCollectMoneyNotification(wxid string, transferInfo map[string]string, response map[string]interface{}) {
	// 构造通知消息
	notification := map[string]interface{}{
		"type":      "collect_money_confirmed",
		"wxid":      wxid,
		"timestamp": time.Now().Unix(),
		"data": map[string]interface{}{
			"transferId":    transferInfo["transferid"],
			"transactionId": transferInfo["transcationid"],
			"fromUser":      transferInfo["tousername"],
			"invalidTime":   transferInfo["invalidtime"],
			"amount":        transferInfo["feedesc"],
			"memo":          transferInfo["pay_memo"],
			"response":      response,
		},
	}

	// 发送到WebSocket连接
	SendNotificationToWebSocket(wxid, notification)

	// 记录详细信息
	fmt.Printf("[AUTO_COLLECT_SUCCESS] 💰 收款确认成功详情:\n")
	fmt.Printf("  - 微信ID: %s\n", wxid)
	fmt.Printf("  - 转账金额: %s\n", transferInfo["feedesc"])
	fmt.Printf("  - 转账备注: %s\n", transferInfo["pay_memo"])
	fmt.Printf("  - 转账ID: %s\n", transferInfo["transferid"])
	fmt.Printf("  - 交易ID: %s\n", transferInfo["transcationid"])
	fmt.Printf("  - 发送者: %s\n", transferInfo["tousername"])
	fmt.Printf("  - 时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
}
