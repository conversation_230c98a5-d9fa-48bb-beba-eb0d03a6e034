package controllers

import (
	"io/ioutil"
	"path/filepath"
	"wechatdll/comm"
)

// APIController API文档控制器
type APIController struct {
	BaseController
}

// Get 获取API文档页面
func (c *APIController) Get() {
	config := comm.GetAPIDocsConfig()

	// 如果启用了密钥验证
	if config.KeyEnabled {
		// 从多个来源获取API密钥
		var providedKey string

		// 1. 从Cookie中获取（优先级最高）
		if cookie, err := c.Ctx.Request.Cookie("api_key"); err == nil {
			providedKey = cookie.Value
		}

		// 2. 从Query参数中获取
		if providedKey == "" {
			providedKey = c.GetString("api_key")
		}

		// 验证密钥
		if providedKey == "" || providedKey != config.Key {
			// 密钥无效，重定向到验证页面
			currentURL := c.Ctx.Request.URL.String()
			c.Redirect("/api/auth?redirect="+currentURL, 302)
			return
		}
	}

	// 读取API文档HTML文件
	htmlPath := filepath.Join("docs", "api-docs.html")
	content, err := ioutil.ReadFile(htmlPath)
	if err != nil {
		c.Ctx.WriteString("API documentation not found.")
		return
	}

	c.Ctx.ResponseWriter.Header().Set("Content-Type", "text/html; charset=utf-8")
	c.Ctx.WriteString(string(content))
}

// APIJSONController Swagger JSON控制器
type APIJSONController struct {
	BaseController
}

// Get 获取Swagger JSON
func (c *APIJSONController) Get() {
	config := comm.GetAPIDocsConfig()

	// 如果启用了密钥验证
	if config.KeyEnabled {
		// 从多个来源获取API密钥
		var providedKey string

		// 1. 从Cookie中获取（优先级最高）
		if cookie, err := c.Ctx.Request.Cookie("api_key"); err == nil {
			providedKey = cookie.Value
		}

		// 2. 从Query参数中获取
		if providedKey == "" {
			providedKey = c.GetString("api_key")
		}

		// 验证密钥
		if providedKey == "" || providedKey != config.Key {
			// 密钥无效，返回401错误
			c.Ctx.ResponseWriter.WriteHeader(401)
			c.Data["json"] = map[string]interface{}{
				"error": "Unauthorized: API key required",
				"auth_url": "/api/auth?redirect=/swagger.json",
			}
			c.ServeJSON()
			return
		}
	}

	// 读取swagger.json文件
	jsonPath := filepath.Join("docs", "swagger.json")
	content, err := ioutil.ReadFile(jsonPath)
	if err != nil {
		c.Data["json"] = map[string]string{"error": "Swagger JSON not found"}
		c.ServeJSON()
		return
	}

	c.Ctx.ResponseWriter.Header().Set("Content-Type", "application/json")
	c.Ctx.WriteString(string(content))
}
