package comm

import (
	"fmt"
	"sync"
	"time"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// ConfigManager 配置管理器
//
// 提供线程安全的配置管理功能，支持配置的读取、更新和验证
// 使用读写锁确保并发安全，支持配置热更新
//
// 主要功能：
// - 从配置文件加载应用配置
// - 提供线程安全的配置读取和更新
// - 配置参数验证和默认值设置
// - 支持配置变更通知
type ConfigManager struct {
	mu     sync.RWMutex // 读写锁，保证并发安全
	config *AppConfig   // 应用配置实例
}

// AppConfig 应用配置结构
//
// 包含微信协议API服务的所有配置参数，按功能模块分组
// 支持JSON序列化，便于配置文件读写和API接口返回
//
// 配置分类：
// - 服务器配置：HTTP服务相关参数
// - Redis配置：缓存和会话存储配置
// - 日志配置：日志级别和格式设置
// - 业务配置：微信协议相关的业务参数
// - 安全配置：限流、认证等安全相关配置
// - 监控配置：性能监控和健康检查配置
type AppConfig struct {
	// 服务器配置
	HTTPPort    string `json:"http_port"`    // HTTP服务端口，默认8080
	HTTPAddr    string `json:"http_addr"`    // HTTP服务监听地址，默认0.0.0.0
	RunMode     string `json:"run_mode"`     // 运行模式：dev/test/prod
	EnableHTTPS bool   `json:"enable_https"` // 是否启用HTTPS

	// Redis配置
	RedisAddr         string        `json:"redis_addr"`
	RedisPassword     string        `json:"redis_password"`
	RedisDB           int           `json:"redis_db"`
	RedisPoolSize     int           `json:"redis_pool_size"`
	RedisMinIdle      int           `json:"redis_min_idle"`
	RedisMaxRetries   int           `json:"redis_max_retries"`
	RedisDialTimeout  time.Duration `json:"redis_dial_timeout"`
	RedisReadTimeout  time.Duration `json:"redis_read_timeout"`
	RedisWriteTimeout time.Duration `json:"redis_write_timeout"`
	RedisIdleTimeout  time.Duration `json:"redis_idle_timeout"`

	// 业务配置
	LongLinkEnabled   bool          `json:"long_link_enabled"`
	LongLinkTimeout   time.Duration `json:"long_link_timeout"`
	HeartBeatInterval time.Duration `json:"heartbeat_interval"`
	MaxLoginRetries   int           `json:"max_login_retries"`
	RequestTimeout    time.Duration `json:"request_timeout"`
}

var (
	configManager     *ConfigManager
	configManagerOnce sync.Once
)

// GetConfigManager 获取配置管理器单例
func GetConfigManager() *ConfigManager {
	configManagerOnce.Do(func() {
		configManager = &ConfigManager{}
		configManager.loadConfig()
	})
	return configManager
}

// loadConfig 加载配置
func (cm *ConfigManager) loadConfig() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.config = &AppConfig{
		// 服务器配置
		HTTPPort:    beego.AppConfig.DefaultString("httpport", "8059"),
		HTTPAddr:    beego.AppConfig.DefaultString("httpaddr", "0.0.0.0"),
		RunMode:     beego.AppConfig.DefaultString("runmode", "dev"),
		EnableHTTPS: beego.AppConfig.DefaultBool("enable_https", false),

		// Redis配置
		RedisAddr:         beego.AppConfig.DefaultString("redislink", "localhost:6379"),
		RedisPassword:     beego.AppConfig.DefaultString("redispass", ""),
		RedisDB:           beego.AppConfig.DefaultInt("redisdbnum", 0),
		RedisPoolSize:     beego.AppConfig.DefaultInt("redis_pool_size", 20),
		RedisMinIdle:      beego.AppConfig.DefaultInt("redis_min_idle_conns", 5),
		RedisMaxRetries:   beego.AppConfig.DefaultInt("redis_max_retries", 3),
		RedisDialTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_dial_timeout", 5)) * time.Second,
		RedisReadTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_read_timeout", 3)) * time.Second,
		RedisWriteTimeout: time.Duration(beego.AppConfig.DefaultInt("redis_write_timeout", 3)) * time.Second,
		RedisIdleTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_idle_timeout", 300)) * time.Second,

		// 业务配置
		LongLinkEnabled:   beego.AppConfig.DefaultBool("longlinkenabled", false),
		LongLinkTimeout:   time.Duration(beego.AppConfig.DefaultInt("longlinkconnecttimeout", 600)) * time.Second,
		HeartBeatInterval: time.Duration(beego.AppConfig.DefaultInt("heartbeat_interval", 180)) * time.Second,
		MaxLoginRetries:   beego.AppConfig.DefaultInt("max_login_retries", 3),
		RequestTimeout:    time.Duration(beego.AppConfig.DefaultInt("request_timeout", 30)) * time.Second,
	}

	log.Info("配置加载完成")
}

// GetConfig 获取配置
func (cm *ConfigManager) GetConfig() *AppConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.config
}

// ReloadConfig 重新加载配置
func (cm *ConfigManager) ReloadConfig() {
	log.Info("重新加载配置...")
	cm.loadConfig()
}

// 便捷方法
func (cm *ConfigManager) GetHTTPPort() string {
	return cm.GetConfig().HTTPPort
}

func (cm *ConfigManager) GetRedisAddr() string {
	return cm.GetConfig().RedisAddr
}





// ValidateConfig 验证配置
func (cm *ConfigManager) ValidateConfig() error {
	config := cm.GetConfig()

	// 验证必要的配置项
	if config.HTTPPort == "" {
		return fmt.Errorf("HTTP端口不能为空")
	}

	if config.RedisAddr == "" {
		return fmt.Errorf("Redis地址不能为空")
	}

	if config.RedisPoolSize <= 0 {
		return fmt.Errorf("Redis连接池大小必须大于0")
	}



	return nil
}

// UpdateConfig 更新配置（运行时）
func (cm *ConfigManager) UpdateConfig(updates map[string]interface{}) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 这里可以实现运行时配置更新逻辑
	// 暂时只记录日志
	log.Infof("配置更新请求: %+v", updates)

	return nil
}

// GetConfigSummary 获取配置摘要（用于监控和调试）
func (cm *ConfigManager) GetConfigSummary() map[string]interface{} {
	config := cm.GetConfig()

	return map[string]interface{}{
		"http_port":         config.HTTPPort,
		"run_mode":          config.RunMode,
		"redis_addr":        config.RedisAddr,
		"redis_pool_size":   config.RedisPoolSize,
		"long_link_enabled": config.LongLinkEnabled,
	}
}
