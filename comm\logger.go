package comm

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// LogLevel 日志级别
type LogLevel int

const (
	DebugLevel LogLevel = iota
	InfoLevel
	WarnLevel
	ErrorLevel
	FatalLevel
)

// Logger 统一日志管理器
type Logger struct {
	*logrus.Logger
	module string
}

var (
	defaultLogger *Logger
	loggerOnce    sync.Once
)

// GetLogger 获取日志实例
func GetLogger(module string) *Logger {
	loggerOnce.Do(func() {
		defaultLogger = NewLogger("default")
	})

	if module == "" {
		return defaultLogger
	}

	return NewLogger(module)
}

// NewLogger 创建新的日志实例
func NewLogger(module string) *Logger {
	logger := logrus.New()

	// 设置日志格式
	logger.SetFormatter(&CustomFormatter{
		Module: module,
	})

	// 设置日志级别
	logger.SetLevel(logrus.InfoLevel)

	// 设置输出
	logFile := setupLogFile(module)
	if logFile != nil {
		logger.SetOutput(logFile)
	}

	return &Logger{
		Logger: logger,
		module: module,
	}
}

// CustomFormatter 自定义日志格式
type CustomFormatter struct {
	Module string
}

// Format 格式化日志
func (f *CustomFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format("2006-01-02 15:04:05")
	level := strings.ToUpper(entry.Level.String())

	// 获取调用者信息
	caller := ""
	if pc, file, line, ok := runtime.Caller(8); ok {
		funcName := runtime.FuncForPC(pc).Name()
		fileName := filepath.Base(file)
		caller = fmt.Sprintf("%s:%d %s", fileName, line, funcName)
	}

	msg := fmt.Sprintf("[%s] [%s] [%s] [%s] %s\n",
		timestamp, level, f.Module, caller, entry.Message)

	return []byte(msg), nil
}

// setupLogFile 设置日志文件
func setupLogFile(module string) *os.File {
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil
	}

	fileName := fmt.Sprintf("%s/%s_%s.log",
		logDir, module, time.Now().Format("2006-01-02"))

	file, err := os.OpenFile(fileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return nil
	}

	return file
}

// LogWithContext 带上下文的日志记录
func (l *Logger) LogWithContext(level LogLevel, wxid, action, message string, extra map[string]interface{}) {
	fields := logrus.Fields{
		"wxid":   wxid,
		"action": action,
		"module": l.module,
	}

	// 添加额外字段
	for k, v := range extra {
		fields[k] = v
	}

	entry := l.WithFields(fields)

	switch level {
	case DebugLevel:
		entry.Debug(message)
	case InfoLevel:
		entry.Info(message)
	case WarnLevel:
		entry.Warn(message)
	case ErrorLevel:
		entry.Error(message)
	case FatalLevel:
		entry.Fatal(message)
	}
}

// LogAPIRequest 记录API请求日志
func (l *Logger) LogAPIRequest(wxid, endpoint, method string, duration time.Duration, statusCode int) {
	l.LogWithContext(InfoLevel, wxid, "api_request",
		fmt.Sprintf("%s %s completed", method, endpoint),
		map[string]interface{}{
			"endpoint":    endpoint,
			"method":      method,
			"duration_ms": duration.Milliseconds(),
			"status_code": statusCode,
		})
}

// LogError 记录错误日志
func (l *Logger) LogError(wxid, action string, err error, extra map[string]interface{}) {
	if extra == nil {
		extra = make(map[string]interface{})
	}
	extra["error"] = err.Error()

	l.LogWithContext(ErrorLevel, wxid, action, err.Error(), extra)
}

// LogSecurity 记录安全相关日志
func (l *Logger) LogSecurity(wxid, event, message string, extra map[string]interface{}) {
	if extra == nil {
		extra = make(map[string]interface{})
	}
	extra["security_event"] = event

	l.LogWithContext(WarnLevel, wxid, "security", message, extra)
}
