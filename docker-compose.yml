version: '3.8'

services:
  # 微信API服务
  wxapi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wxapi
    ports:
      - "8059:8059"  # HTTP端口
      - "8088:8088"  # WebSocket端口
    environment:
      - TZ=Asia/Shanghai
      - DEPLOY_MODE=docker
      - REDIS_LINK=redis:6379
      - REDIS_PASS=123456aa
      - REDIS_DB=2
    volumes:
      - ./conf:/app/conf:ro
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - wxapi-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8059/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis 服务
  redis:
    image: redis:7-alpine
    container_name: wxapi-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass 123456aa --appendonly yes
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - wxapi-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

volumes:
  redis-data:
    driver: local

networks:
  wxapi-network:
    driver: bridge
