package comm

import (
	"encoding/json"
	"fmt"
	"time"
	"wechatdll/models"

	"github.com/astaxie/beego/context"
	log "github.com/sirupsen/logrus"
)

// ResponseHelper 响应助手，用于减少重复的响应处理代码
type ResponseHelper struct {
	ctx *context.Context
}

// NewResponseHelper 创建响应助手
func NewResponseHelper(ctx *context.Context) *ResponseHelper {
	return &ResponseHelper{ctx: ctx}
}

// SendJSONResponse 发送JSON响应的通用方法
func (rh *ResponseHelper) SendJSONResponse(statusCode int, response *models.StandardResponse) {
	rh.ctx.Output.Status = statusCode
	rh.ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	rh.ctx.Output.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	rh.ctx.Output.Header("Pragma", "no-cache")
	rh.ctx.Output.Header("Expires", "0")

	// 设置请求ID
	if response.RequestID == "" {
		response.RequestID = rh.generateRequestID()
	}

	rh.ctx.Output.JSON(response, false, false)
}

// SendSuccess 发送成功响应
func (rh *ResponseHelper) SendSuccess(data interface{}, message string) {
	response := models.NewSuccessResponse(data, message)
	rh.SendJSONResponse(200, response)
}

// SendError 发送错误响应
func (rh *ResponseHelper) SendError(code int64, message string, statusCode int) {
	response := models.NewErrorResponse(code, message)
	rh.SendJSONResponse(statusCode, response)
}

// SendBusinessError 发送业务错误响应
func (rh *ResponseHelper) SendBusinessError(err error) {
	var code int64 = models.ErrCodeOperationFailed
	message := "操作失败"

	if apiErr, ok := err.(*models.APIError); ok {
		code = apiErr.Code
		message = apiErr.Message
	} else {
		message = err.Error()
	}

	// 记录业务错误
	rh.logError(err, "业务错误")

	rh.SendError(code, message, 400)
}

// SendWXError 发送微信API错误响应
func (rh *ResponseHelper) SendWXError(wxErr error, wxCode int32, defaultMessage string) {
	var code int64 = models.ErrCodeWXAPIError
	message := defaultMessage

	// 根据微信错误码映射
	switch wxCode {
	case -1:
		code = models.ErrCodeWXProtocolError
		message = "微信协议错误"
	case -2:
		code = models.ErrCodeWXAccountError
		message = "微信账号异常"
	case -100:
		code = models.ErrCodeLoginExpired
		message = "登录已过期，请重新登录"
	default:
		if wxErr != nil {
			message = fmt.Sprintf("微信API错误: %v", wxErr)
		}
	}

	// 记录微信API错误
	log.WithFields(log.Fields{
		"wx_code":    wxCode,
		"wx_error":   wxErr,
		"method":     rh.ctx.Request.Method,
		"path":       rh.ctx.Request.URL.Path,
		"request_id": rh.generateRequestID(),
	}).Warn("微信API错误")

	rh.SendError(code, message, 400)
}

// SendNetworkError 发送网络错误响应
func (rh *ResponseHelper) SendNetworkError(err error) {
	var code int64 = models.ErrCodeNetworkError
	message := "网络连接错误"

	// 根据错误类型细分
	errStr := err.Error()
	if contains(errStr, "timeout") {
		code = models.ErrCodeTimeout
		message = "请求超时"
	} else if contains(errStr, "connection refused") {
		code = models.ErrCodeWXConnectFailed
		message = "连接被拒绝"
	}

	// 记录网络错误
	rh.logError(err, "网络错误")

	rh.SendError(code, message, 503)
}

// SendValidationError 发送参数验证错误响应
func (rh *ResponseHelper) SendValidationError(err error) {
	message := "参数验证失败"
	if err != nil {
		message = fmt.Sprintf("参数验证失败: %s", err.Error())
	}

	rh.logError(err, "参数验证错误")
	rh.SendError(models.ErrCodeInvalidParam, message, 400)
}

// generateRequestID 生成请求ID
func (rh *ResponseHelper) generateRequestID() string {
	return fmt.Sprintf("%d-%s", time.Now().UnixNano(), rh.ctx.Input.IP())
}

// logError 记录错误日志
func (rh *ResponseHelper) logError(err error, errorType string) {
	if err != nil {
		log.WithFields(log.Fields{
			"error":      err.Error(),
			"error_type": errorType,
			"method":     rh.ctx.Request.Method,
			"path":       rh.ctx.Request.URL.Path,
			"ip":         rh.ctx.Input.IP(),
			"user_agent": rh.ctx.Request.UserAgent(),
			"request_id": rh.generateRequestID(),
		}).Error(errorType)
	}
}

// contains 检查字符串是否包含子字符串（避免导入strings包）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(len(substr) == 0 || findSubstring(s, substr) >= 0)
}

// findSubstring 查找子字符串位置
func findSubstring(s, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(substr) > len(s) {
		return -1
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// JSONHelper JSON处理助手，减少重复的JSON处理代码
type JSONHelper struct{}

// NewJSONHelper 创建JSON助手
func NewJSONHelper() *JSONHelper {
	return &JSONHelper{}
}

// ParseRequest 解析请求体到结构体
func (jh *JSONHelper) ParseRequest(requestBody []byte, v interface{}) error {
	if len(requestBody) == 0 {
		return fmt.Errorf("请求体为空")
	}

	if err := json.Unmarshal(requestBody, v); err != nil {
		return fmt.Errorf("JSON解析失败: %w", err)
	}

	return nil
}

// MarshalResponse 序列化响应数据
func (jh *JSONHelper) MarshalResponse(v interface{}) ([]byte, error) {
	data, err := json.Marshal(v)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %w", err)
	}
	return data, nil
}

// ConvertType 类型转换助手（用于减少bts/Util.go中的重复代码）
func (jh *JSONHelper) ConvertType(src interface{}, dst interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return fmt.Errorf("序列化源数据失败: %w", err)
	}

	if err := json.Unmarshal(data, dst); err != nil {
		return fmt.Errorf("反序列化到目标类型失败: %w", err)
	}

	return nil
}

// ValidationHelper 验证助手，减少重复的验证代码
type ValidationHelper struct{}

// NewValidationHelper 创建验证助手
func NewValidationHelper() *ValidationHelper {
	return &ValidationHelper{}
}

// ValidateRequired 验证必填字段
func (vh *ValidationHelper) ValidateRequired(value, fieldName string) error {
	if value == "" || value == "string" {
		return fmt.Errorf("%s不能为空", fieldName)
	}
	return nil
}

// ValidateWxid 验证微信ID
func (vh *ValidationHelper) ValidateWxid(wxid string) error {
	return vh.ValidateRequired(wxid, "微信ID")
}

// ValidateContent 验证内容字段
func (vh *ValidationHelper) ValidateContent(content string) error {
	return vh.ValidateRequired(content, "内容")
}

// 全局实例
var (
	globalJSONHelper       *JSONHelper
	globalValidationHelper *ValidationHelper
)

// GetJSONHelper 获取全局JSON助手
func GetJSONHelper() *JSONHelper {
	if globalJSONHelper == nil {
		globalJSONHelper = NewJSONHelper()
	}
	return globalJSONHelper
}

// GetValidationHelper 获取全局验证助手
func GetValidationHelper() *ValidationHelper {
	if globalValidationHelper == nil {
		globalValidationHelper = NewValidationHelper()
	}
	return globalValidationHelper
}

// 全局响应函数，用于向后兼容
// ResponseSuccess 发送成功响应
func ResponseSuccess(ctx *context.Context, data interface{}, message string) {
	helper := NewResponseHelper(ctx)
	helper.SendSuccess(data, message)
}

// ResponseError 发送错误响应
func ResponseError(ctx *context.Context, message string) {
	helper := NewResponseHelper(ctx)
	helper.SendError(-1, message, 400)
}
