package Tools

import (
	"fmt"
	"net"
	"net/http"
	"net/url"
	"time"
	"wechatdll/comm"
	"wechatdll/models"
	"golang.org/x/net/proxy"
)

type SetProxyParam struct {
	Wxid  string
	Proxy models.ProxyInfo
}

type GetProxyParam struct {
	Wxid string
}

type TestProxyParam struct {
	Type          string `json:"Type"`
	Host          string `json:"Host"`
	Port          int    `json:"Port"`
	ProxyUser     string `json:"ProxyUser,omitempty"`
	ProxyPassword string `json:"ProxyPassword,omitempty"`
}

func SetProxy(Data SetProxyParam) models.ResponseResult {
	loginDataMu := comm.GetLoginLock(Data.Wxid)
	loginDataMu.Lock()
	defer loginDataMu.Unlock()
	D, err := comm.GetLoginata(Data.Wxid, loginDataMu)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//初始化Mmtls
	_, MmtlsClient, err := comm.MmtlsInitialize(Data.Proxy, D.ShortHost)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	D.Proxy = Data.Proxy
	D.MmtlsKey = MmtlsClient

	if Data.Proxy.ProxyIp == "" {
		D.Proxy = models.ProxyInfo{}
	}

	err = comm.CreateLoginData(D, Data.Wxid, 0, loginDataMu)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "失败",
			Data:    err.Error(),
		}
	}

	return models.ResponseResult{
		Code:    1,
		Success: true,
		Message: "成功",
		Data:    nil,
	}

}

func GetProxy(Data GetProxyParam) models.ResponseResult {
	loginDataMu := comm.GetLoginLock(Data.Wxid)
	loginDataMu.Lock()
	defer loginDataMu.Unlock()
	D, err := comm.GetLoginata(Data.Wxid, loginDataMu)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	// 返回当前设置的代理信息
	return models.ResponseResult{
		Code:    1,
		Success: true,
		Message: "成功",
		Data:    D.Proxy,
	}
}

func TestProxy(Data TestProxyParam) models.ResponseResult {
	// 构建代理地址
	proxyAddr := fmt.Sprintf("%s:%d", Data.Host, Data.Port)

	// 记录开始时间
	startTime := time.Now()

	// 测试代理连接
	var client *http.Client
	var err error

	if Data.Type == "SOCKS5" {
		// 设置代理认证
		var proxyAuth *proxy.Auth
		if Data.ProxyUser != "" && Data.ProxyPassword != "" {
			proxyAuth = &proxy.Auth{
				User:     Data.ProxyUser,
				Password: Data.ProxyPassword,
			}
		}

		// 创建SOCKS5代理拨号器
		dialer, err := proxy.SOCKS5("tcp", proxyAddr, proxyAuth, &net.Dialer{
			Timeout: 10 * time.Second,
		})
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("创建代理连接失败：%v", err.Error()),
				Data: map[string]interface{}{
					"status": "error",
					"error":  err.Error(),
				},
			}
		}

		// 创建HTTP客户端
		client = &http.Client{
			Transport: &http.Transport{
				Dial:                dialer.Dial,
				TLSHandshakeTimeout: 10 * time.Second,
			},
			Timeout: 15 * time.Second,
		}
	} else {
		// HTTP代理
		proxyURL := fmt.Sprintf("http://%s", proxyAddr)
		if Data.ProxyUser != "" && Data.ProxyPassword != "" {
			proxyURL = fmt.Sprintf("http://%s:%s@%s", Data.ProxyUser, Data.ProxyPassword, proxyAddr)
		}

		// 解析代理URL
		proxyURLParsed, err := url.Parse(proxyURL)
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("代理URL解析失败：%v", err.Error()),
				Data: map[string]interface{}{
					"status": "error",
					"error":  err.Error(),
				},
			}
		}

		client = &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxyURLParsed),
			},
			Timeout: 15 * time.Second,
		}
	}

	// 测试连接到一个可靠的网站
	resp, err := client.Get("https://www.baidu.com")
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("代理连接测试失败：%v", err.Error()),
			Data: map[string]interface{}{
				"status": "error",
				"error":  err.Error(),
			},
		}
	}
	defer resp.Body.Close()

	// 计算响应时间
	responseTime := int(time.Since(startTime).Milliseconds())

	// 检查响应状态
	if resp.StatusCode != 200 {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("代理连接测试失败，HTTP状态码：%d", resp.StatusCode),
			Data: map[string]interface{}{
				"status":       "error",
				"responseTime": responseTime,
				"error":        fmt.Sprintf("HTTP %d", resp.StatusCode),
			},
		}
	}

	return models.ResponseResult{
		Code:    1,
		Success: true,
		Message: "代理连接测试成功",
		Data: map[string]interface{}{
			"status":       "active",
			"responseTime": responseTime,
		},
	}
}
