package controllers

import (
	"encoding/json"
	"fmt"
	"strings"
	"wechatdll/TcpPoll"
	"wechatdll/comm"
	"wechatdll/models"
	"wechatdll/models/Friend"
)

// 朋友模块
type FriendController struct {
	BaseController
}

// @Summary 搜索联系人
// @Param	body			body	Friend.SearchParam	 true		"爆粉情况下特殊通道请自行填写,默认时FromScene=0,SearchScene=1"
// @Failure 200
// @router /Search [post]
func (c *FriendController) Search() {
	var Data Friend.SearchParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.Search(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// 判断好友关系/拉黑/删除
func (c *FriendController) GetFriendRelation() {
	var Data Friend.FriendRelationParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.FriendRelation(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 添加联系人(发送好友请求)
// @Param	body			body	Friend.SendRequestParam	 true		"V1 V2是必填项"
// @Failure 200
// @router /SendRequest [post]
func (c *FriendController) SendRequest() {
	var Data Friend.SendRequestParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.SendRequest(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 通过好友请求
// @Param	body			body	Friend.PassVerifyParam	 true		"Scene：代表来源,请在消息中的xml中获取"
// @Failure 200
// @router /PassVerify [post]
func (c *FriendController) PassVerify() {
	var Data Friend.PassVerifyParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.PassVerify(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 上传通讯录
// @Param	body			body	Friend.UploadParam	 true		"PhoneNo多个手机号请用,隔开   CurrentPhoneNo自己的手机号  Opcode == 1上传 2删除"
// @Failure 200
// @router /Upload [post]
func (c *FriendController) Upload() {
	var Data Friend.UploadParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.Upload(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取手机通讯录
// @Param	wxid		query 	string	true		"请输入登录后的wxid"
// @Failure 200
// @router /GetMFriend [post]
func (c *FriendController) GetMFriend() {
	wxid := c.GetString("wxid")
	WXDATA := Friend.GetMFriend(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取通讯录好友
// @Param	body			body	Friend.GetContactListParams	 true		"CurrentWxcontactSeq和CurrentChatRoomContactSeq没有的情况下请填写0，Offset和Limit用于分页控制"
// @Failure 200
// @router /GetContractList [post]
func (c *FriendController) GetContractList() {
	var Data Friend.GetContractListparameter
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.GetContractList(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取全部通讯录好友（支持文件缓存）
// @Param	body			body	Friend.GetContactListParams	 true		"CurrentWxcontactSeq和CurrentChatRoomContactSeq没有的情况下请填写0，Offset和Limit用于分页控制，force_refresh为true时强制刷新缓存"
// @Failure 200
// @router /GetTotalContractList [post]
func (c *FriendController) GetTotalContractList() {
	var Data Friend.GetContactListParams
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.GetTotalContractList(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取简化格式的通讯录好友列表
// @Param	body			body	Friend.GetContactListParams	 true		"CurrentWxcontactSeq和CurrentChatRoomContactSeq没有的情况下请填写0，force_refresh为true时强制刷新缓存"
// @Failure 200
// @router /GetSimplifiedContractList [post]
func (c *FriendController) GetSimplifiedContractList() {
	var Data Friend.GetContactListParams
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.GetSimplifiedContractList(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 清除联系人缓存
// @Param	body			body	Friend.ClearCacheParams	 true		"微信ID，留空则清除所有缓存"
// @Failure 200
// @router /ClearContactCache [post]
func (c *FriendController) ClearContactCache() {
	type ClearCacheParams struct {
		Wxid string `json:"Wxid,omitempty"` // 留空则清除所有缓存
	}

	var Data ClearCacheParams
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	if Data.Wxid == "" {
		Friend.ClearAllContactCache()
	} else {
		Friend.ClearContactCache(Data.Wxid)
	}

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "缓存清除成功",
		Data:    nil,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 获取通讯录好友详情（默认启用文件缓存）
// @Param	body			body	Friend.GetContractDetailparameter	 true		"多个微信请用,隔开(最多20个),ChatRoom请留空,缓存默认启用,force_refresh强制刷新缓存"
// @Failure 200
// @router /GetContractDetail [post]
func (c *FriendController) GetContractDetail() {
	var Data Friend.GetContractDetailparameter
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.GetContractDetail(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取通讯录缓存统计
// @Failure 200
// @router /GetContactCacheStats [get]
func (c *FriendController) GetContactCacheStats() {
	cache := comm.GetContactCache()
	stats := cache.GetStats()

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "获取缓存统计成功",
		Data:    stats,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 设置好友备注
// @Param	body			body	Friend.SetRemarksParam	 true		""
// @Failure 200
// @router /SetRemarks [post]
func (c *FriendController) SetRemarks() {
	var Data Friend.SetRemarksParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.SetRemarks(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 删除好友
// @Param	body			body	Friend.DefaultParam	 true		""
// @Failure 200
// @router /Delete [post]
func (c *FriendController) Delete() {
	var Data Friend.DefaultParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.Delete(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 添加/移除黑名单
// @Param	body			body	Friend.BlacklistParam	 true		"Val == 15添加  7移除"
// @Failure 200
// @router /Blacklist [post]
func (c *FriendController) Blacklist() {
	var Data Friend.BlacklistParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.Blacklist(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 附近人
// @Param	body			body	Friend.LbsFindParam	 true		"OpCode == 1"
// @Failure 200
// @router /LbsFind [post]
func (c *FriendController) LbsFind() {
	var Data Friend.LbsFindParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.LbsFind(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 查询好友状态
// @Param	body			body	Friend.FriendRelationParam	 true		"OpCode == 1"
// @Failure 200
// @router /GetFriendstate [post]
func (c *FriendController) GetFriendstate() {
	var Data Friend.FriendRelationParam
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &Data)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Friend.FriendRelation(Data)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// ==================== 自动通过好友请求功能 ====================

// validateWxidParam 验证wxid参数
func (c *FriendController) validateWxidParam() (string, bool) {
	wxid := c.GetString("wxid")
	if wxid == "" {
		c.Data["json"] = &models.ResponseResult{
			Code:    -9,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.ServeJSON()
		return "", false
	}
	return wxid, true
}

// parseEnableParam 解析enable参数
func (c *FriendController) parseEnableParam() (bool, bool) {
	enableStr := c.GetString("enable")
	if enableStr == "" {
		c.Data["json"] = &models.ResponseResult{
			Code:    -9,
			Success: false,
			Message: "enable参数不能为空",
			Data:    nil,
		}
		c.ServeJSON()
		return false, false
	}

	if enableStr == "true" || enableStr == "1" {
		return true, true
	} else if enableStr == "false" || enableStr == "0" {
		return false, true
	}

	c.Data["json"] = &models.ResponseResult{
		Code:    -9,
		Success: false,
		Message: "enable参数必须是true/false或1/0",
		Data:    nil,
	}
	c.ServeJSON()
	return false, false
}

// @Summary 设置自动通过好友请求状态
// @Param	wxid	query	string	true	"微信ID"
// @Param	enable	query	bool	true	"是否开启自动通过好友请求"
// @Success 200
// @Failure 200
// @router /AutoAcceptFriendSetStatus [get]
func (c *FriendController) AutoAcceptFriendSetStatus() {
	wxid, ok := c.validateWxidParam()
	if !ok {
		return
	}

	enable, ok := c.parseEnableParam()
	if !ok {
		return
	}

	// 设置自动通过好友请求状态
	err := TcpPoll.SetAutoAcceptFriendStatus(wxid, enable)
	if err != nil {
		result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("设置失败：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	status := "关闭"
	if enable {
		status = "开启"
	}

	result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: fmt.Sprintf("自动通过好友请求已%s", status),
		Data: map[string]interface{}{
			"wxid":   wxid,
			"enable": enable,
			"status": status,
		},
	}
	c.Data["json"] = &result
	c.ServeJSON()
}

// @Summary 获取自动通过好友请求状态
// @Param	wxid	query	string	true	"微信ID"
// @Success 200
// @Failure 200
// @router /AutoAcceptFriendGetStatus [get]
func (c *FriendController) AutoAcceptFriendGetStatus() {
	wxid, ok := c.validateWxidParam()
	if !ok {
		return
	}

	statusInfo := TcpPoll.GetAutoAcceptFriendStatusWithDetails(wxid)
	c.Data["json"] = &models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "查询成功",
		Data:    statusInfo,
	}
	c.ServeJSON()
}

// @Summary 批量设置自动通过好友请求状态
// @Param	wxids	query	string	true	"微信ID列表，用逗号分隔"
// @Param	enable	query	bool	true	"是否开启自动通过好友请求"
// @Success 200
// @Failure 200
// @router /AutoAcceptFriendBatchSetStatus [get]
func (c *FriendController) AutoAcceptFriendBatchSetStatus() {
	wxidsStr := c.GetString("wxids")
	if wxidsStr == "" {
		c.Data["json"] = &models.ResponseResult{
			Code:    -9,
			Success: false,
			Message: "wxids参数不能为空",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	enable, ok := c.parseEnableParam()
	if !ok {
		return
	}

	wxidList := strings.Split(wxidsStr, ",")
	if len(wxidList) == 0 {
		c.Data["json"] = &models.ResponseResult{
			Code:    -9,
			Success: false,
			Message: "wxid列表不能为空",
			Data:    nil,
		}
		c.ServeJSON()
		return
	}

	// 批量设置状态
	results := make([]map[string]interface{}, 0)
	successCount := 0
	failCount := 0

	for _, wxid := range wxidList {
		wxid = strings.TrimSpace(wxid)
		if wxid == "" {
			continue
		}

		err := TcpPoll.SetAutoAcceptFriendStatus(wxid, enable)
		if err != nil {
			results = append(results, map[string]interface{}{
				"wxid":    wxid,
				"success": false,
				"message": err.Error(),
			})
			failCount++
		} else {
			results = append(results, map[string]interface{}{
				"wxid":    wxid,
				"success": true,
				"message": "设置成功",
			})
			successCount++
		}
	}

	status := "关闭"
	if enable {
		status = "开启"
	}

	result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: fmt.Sprintf("批量设置完成，成功：%d，失败：%d", successCount, failCount),
		Data: map[string]interface{}{
			"enable":       enable,
			"status":       status,
			"total":        len(wxidList),
			"successCount": successCount,
			"failCount":    failCount,
			"results":      results,
		},
	}
	c.Data["json"] = &result
	c.ServeJSON()
}


