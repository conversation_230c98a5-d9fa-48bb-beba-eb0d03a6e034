package controllers

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// APIDocsConfigController API文档配置控制器
type APIDocsConfigController struct {
	BaseController
}

// APIDocsConfigInfo API文档配置信息
type APIDocsConfigInfo struct {
	Route      string `json:"route"`       // API文档路由路径
	Key        string `json:"key"`         // API文档访问密钥
	KeyEnabled bool   `json:"key_enabled"` // 是否启用密钥验证
	URL        string `json:"url"`         // 完整访问URL
}

// UpdateAPIDocsConfigRequest 更新API文档配置请求
type UpdateAPIDocsConfigRequest struct {
	Route      string `json:"route"`       // API文档路由路径
	Key        string `json:"key"`         // API文档访问密钥
	KeyEnabled bool   `json:"key_enabled"` // 是否启用密钥验证
}

// GetAPIDocsConfig 获取API文档配置
//
// @Summary 获取API文档配置信息
// @Description 获取当前API文档的路由、密钥等配置信息
// @Tags Config
// @Success 200 {object} models.StandardResponse{data=APIDocsConfigInfo} "配置信息"
// @Failure 500 {object} models.StandardResponse "服务器内部错误"
// @router /GetAPIDocsConfig [get]
func (c *APIDocsConfigController) GetAPIDocsConfig() {
	config := comm.GetAPIDocsConfig()
	url := comm.GetAPIDocsURL()

	configInfo := APIDocsConfigInfo{
		Route:      config.Route,
		Key:        config.Key,
		KeyEnabled: config.KeyEnabled,
		URL:        url,
	}

	result := models.StandardResponse{
		Code:    0,
		Success: true,
		Message: "获取API文档配置成功",
		Data:    configInfo,
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// UpdateAPIDocsConfig 更新API文档配置
//
// @Summary 更新API文档配置
// @Description 动态更新API文档的路由和密钥配置（需要重启服务生效）
// @Tags Config
// @Param body body UpdateAPIDocsConfigRequest true "配置信息"
// @Success 200 {object} models.StandardResponse "更新成功"
// @Failure 400 {object} models.StandardResponse "参数错误"
// @Failure 500 {object} models.StandardResponse "服务器内部错误"
// @router /UpdateAPIDocsConfig [post]
func (c *APIDocsConfigController) UpdateAPIDocsConfig() {
	var req UpdateAPIDocsConfigRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result := models.StandardResponse{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("参数解析失败：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 验证路由格式
	if req.Route == "" {
		result := models.StandardResponse{
			Code:    -2,
			Success: false,
			Message: "路由路径不能为空",
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 确保路由以 / 开头
	if !strings.HasPrefix(req.Route, "/") {
		req.Route = "/" + req.Route
	}

	// 移除末尾的 /
	req.Route = strings.TrimSuffix(req.Route, "/")

	// 验证密钥（如果启用密钥验证）
	if req.KeyEnabled && req.Key == "" {
		result := models.StandardResponse{
			Code:    -3,
			Success: false,
			Message: "启用密钥验证时，密钥不能为空",
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 更新配置
	err = c.updateConfigFile(req)
	if err != nil {
		log.WithError(err).Error("更新API文档配置失败")
		result := models.StandardResponse{
			Code:    -4,
			Success: false,
			Message: fmt.Sprintf("更新配置失败：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 记录配置更改
	log.WithFields(log.Fields{
		"old_route":       beego.AppConfig.DefaultString("api_docs_route", "/api"),
		"new_route":       req.Route,
		"old_key_enabled": beego.AppConfig.DefaultBool("api_docs_key_enabled", true),
		"new_key_enabled": req.KeyEnabled,
		"operator_ip":     c.Ctx.Input.IP(),
	}).Info("API文档配置已更新")

	result := models.StandardResponse{
		Code:    0,
		Success: true,
		Message: "API文档配置更新成功，请重启服务使配置生效",
		Data: map[string]interface{}{
			"route":            req.Route,
			"key_enabled":      req.KeyEnabled,
			"restart_required": true,
		},
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// updateConfigFile 更新配置文件
func (c *APIDocsConfigController) updateConfigFile(req UpdateAPIDocsConfigRequest) error {
	log.WithFields(log.Fields{
		"route":       req.Route,
		"key":         "***masked***",
		"key_enabled": req.KeyEnabled,
	}).Info("配置更新请求（需要重启服务生效）")

	// TODO(kedaya): 实现配置文件写入逻辑 - 已实现
	// 使用配置文件直接写入方式来实现动态配置更新
	return c.writeConfigToFile(req)
}

// GetCurrentAPIDocsURL 获取当前API文档访问URL
//
// @Summary 获取当前API文档访问URL
// @Description 获取当前配置下的API文档完整访问地址
// @Tags Config
// @Success 200 {object} models.StandardResponse{data=string} "API文档URL"
// @router /GetCurrentAPIDocsURL [get]
func (c *APIDocsConfigController) GetCurrentAPIDocsURL() {
	url := comm.GetAPIDocsURL()

	result := models.StandardResponse{
		Code:    0,
		Success: true,
		Message: "获取API文档URL成功",
		Data:    url,
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// ValidateAPIDocsConfig 验证API文档配置
//
// @Summary 验证API文档配置
// @Description 验证提供的API文档配置是否有效
// @Tags Config
// @Param body body UpdateAPIDocsConfigRequest true "配置信息"
// @Success 200 {object} models.StandardResponse "验证结果"
// @Failure 400 {object} models.StandardResponse "配置无效"
// @router /ValidateAPIDocsConfig [post]
func (c *APIDocsConfigController) ValidateAPIDocsConfig() {
	var req UpdateAPIDocsConfigRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result := models.StandardResponse{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("参数解析失败：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 验证逻辑
	errors := []string{}

	if req.Route == "" {
		errors = append(errors, "路由路径不能为空")
	} else {
		// 检查路由格式
		if !strings.HasPrefix(req.Route, "/") {
			req.Route = "/" + req.Route
		}
		if strings.Contains(req.Route, " ") {
			errors = append(errors, "路由路径不能包含空格")
		}
	}

	if req.KeyEnabled && req.Key == "" {
		errors = append(errors, "启用密钥验证时，密钥不能为空")
	}

	if req.KeyEnabled && len(req.Key) < 8 {
		errors = append(errors, "密钥长度至少为8个字符")
	}

	if len(errors) > 0 {
		result := models.StandardResponse{
			Code:    -2,
			Success: false,
			Message: "配置验证失败",
			Data: map[string]interface{}{
				"errors": errors,
			},
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	result := models.StandardResponse{
		Code:    0,
		Success: true,
		Message: "配置验证通过",
		Data: map[string]interface{}{
			"route":       req.Route,
			"key_enabled": req.KeyEnabled,
			"valid":       true,
		},
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// writeConfigToFile 写入配置到文件
func (c *APIDocsConfigController) writeConfigToFile(req UpdateAPIDocsConfigRequest) error {
	configPath := filepath.Join("conf", "app.conf")

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取现有配置文件
	file, err := os.Open(configPath)
	if err != nil {
		return fmt.Errorf("无法打开配置文件: %v", err)
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 更新配置项
	lines = c.updateConfigLines(lines, req)

	// 写入更新后的配置
	return c.writeLinesToFile(configPath, lines)
}

// updateConfigLines 更新配置行
func (c *APIDocsConfigController) updateConfigLines(lines []string, req UpdateAPIDocsConfigRequest) []string {
	routePattern := regexp.MustCompile(`^api_docs_route\s*=\s*.*$`)
	// 使用新的统一配置项
	keyPattern := regexp.MustCompile(`^api_key\s*=\s*.*$`)
	keyEnabledPattern := regexp.MustCompile(`^api_key_enabled\s*=\s*.*$`)
	// 向后兼容：也检查旧的配置项
	oldKeyPattern := regexp.MustCompile(`^api_docs_key\s*=\s*.*$`)
	oldKeyEnabledPattern := regexp.MustCompile(`^api_docs_key_enabled\s*=\s*.*$`)

	routeUpdated := false
	keyUpdated := false
	keyEnabledUpdated := false

	for i, line := range lines {
		if routePattern.MatchString(line) {
			lines[i] = fmt.Sprintf("api_docs_route = \"%s\"", req.Route)
			routeUpdated = true
		} else if keyPattern.MatchString(line) {
			// 更新新的统一配置项
			lines[i] = fmt.Sprintf("api_key = \"%s\"", req.Key)
			keyUpdated = true
		} else if keyEnabledPattern.MatchString(line) {
			// 更新新的统一配置项
			lines[i] = fmt.Sprintf("api_key_enabled = %t", req.KeyEnabled)
			keyEnabledUpdated = true
		} else if oldKeyPattern.MatchString(line) {
			// 注释掉旧的配置项
			lines[i] = fmt.Sprintf("# api_docs_key = \"%s\"  # 已废弃，请使用api_key", req.Key)
		} else if oldKeyEnabledPattern.MatchString(line) {
			// 注释掉旧的配置项
			lines[i] = fmt.Sprintf("# api_docs_key_enabled = %t  # 已废弃，请使用api_key_enabled", req.KeyEnabled)
		}
	}

	// 如果配置项不存在，添加到API文档配置部分
	if !routeUpdated || !keyUpdated || !keyEnabledUpdated {
		lines = c.addMissingConfigs(lines, req, routeUpdated, keyUpdated, keyEnabledUpdated)
	}

	return lines
}

// addMissingConfigs 添加缺失的配置项
func (c *APIDocsConfigController) addMissingConfigs(lines []string, req UpdateAPIDocsConfigRequest,
	routeUpdated, keyUpdated, keyEnabledUpdated bool) []string {

	// 查找API文档配置部分
	apiDocsIndex := -1
	for i, line := range lines {
		if strings.Contains(line, "API文档配置") {
			apiDocsIndex = i
			break
		}
	}

	if apiDocsIndex == -1 {
		// 如果没有找到API文档配置部分，添加到文件末尾
		lines = append(lines, "", "# ==================== API文档配置 ====================")
		apiDocsIndex = len(lines) - 1
	}

	// 在API文档配置部分后添加缺失的配置
	insertIndex := apiDocsIndex + 1
	if !routeUpdated {
		lines = c.insertLine(lines, insertIndex, fmt.Sprintf("api_docs_route = \"%s\"", req.Route))
		insertIndex++
	}

	// 查找API安全配置部分，将密钥配置添加到那里
	apiSecurityIndex := -1
	for i, line := range lines {
		if strings.Contains(line, "API安全配置") {
			apiSecurityIndex = i
			break
		}
	}

	if apiSecurityIndex == -1 {
		// 如果没有找到API安全配置部分，在API文档配置前添加
		lines = c.insertLine(lines, apiDocsIndex, "")
		lines = c.insertLine(lines, apiDocsIndex, "# ==================== API安全配置 ====================")
		apiSecurityIndex = apiDocsIndex
		insertIndex += 2 // 调整插入位置
	}

	// 在API安全配置部分添加统一的密钥配置
	securityInsertIndex := apiSecurityIndex + 1
	if !keyUpdated {
		lines = c.insertLine(lines, securityInsertIndex, fmt.Sprintf("api_key = \"%s\"", req.Key))
		securityInsertIndex++
	}
	if !keyEnabledUpdated {
		lines = c.insertLine(lines, securityInsertIndex, fmt.Sprintf("api_key_enabled = %t", req.KeyEnabled))
	}

	return lines
}

// insertLine 在指定位置插入行
func (c *APIDocsConfigController) insertLine(lines []string, index int, line string) []string {
	if index >= len(lines) {
		return append(lines, line)
	}

	lines = append(lines, "")
	copy(lines[index+1:], lines[index:])
	lines[index] = line
	return lines
}

// writeLinesToFile 将行写入文件
func (c *APIDocsConfigController) writeLinesToFile(configPath string, lines []string) error {
	// 创建临时文件
	tempPath := configPath + ".tmp"
	tempFile, err := os.Create(tempPath)
	if err != nil {
		return fmt.Errorf("无法创建临时文件: %v", err)
	}
	defer tempFile.Close()

	// 写入内容
	writer := bufio.NewWriter(tempFile)
	for _, line := range lines {
		if _, err := writer.WriteString(line + "\n"); err != nil {
			return fmt.Errorf("写入配置失败: %v", err)
		}
	}

	if err := writer.Flush(); err != nil {
		return fmt.Errorf("刷新缓冲区失败: %v", err)
	}

	// 原子性替换原文件
	if err := os.Rename(tempPath, configPath); err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("替换配置文件失败: %v", err)
	}

	log.WithFields(log.Fields{
		"config_path": configPath,
		"route":       "***masked***",
		"key_enabled": "***masked***",
	}).Info("配置文件更新成功")

	return nil
}
