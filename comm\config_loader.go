package comm

import (
	"fmt"
	"os"
	"strconv"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// ConfigLoader 配置加载器
// 支持环境变量覆盖和部署模式特定配置
type ConfigLoader struct {
	deployMode string
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader() *ConfigLoader {
	return &ConfigLoader{}
}

// LoadConfig 加载配置
func (cl *ConfigLoader) LoadConfig() error {
	// 获取部署模式
	cl.deployMode = cl.getDeployMode()
	log.Infof("当前部署模式: %s", cl.deployMode)

	// 根据部署模式应用特定配置
	if err := cl.applyModeSpecificConfig(); err != nil {
		return fmt.Errorf("应用部署模式配置失败: %v", err)
	}

	// 应用环境变量覆盖
	cl.applyEnvironmentOverrides()

	return nil
}

// getDeployMode 获取部署模式
func (cl *ConfigLoader) getDeployMode() string {
	// 优先从环境变量获取
	if mode := os.Getenv("DEPLOY_MODE"); mode != "" {
		return mode
	}

	// 从配置文件获取
	mode := beego.AppConfig.DefaultString("deploy_mode", "local")

	// 自动检测Docker环境
	if mode == "local" && os.Getenv("DOCKER") == "1" {
		return "docker"
	}

	return mode
}

// applyModeSpecificConfig 应用部署模式特定配置
func (cl *ConfigLoader) applyModeSpecificConfig() error {
	switch cl.deployMode {
	case "http":
		cl.applyHTTPModeConfig()
	case "tcp":
		cl.applyTCPModeConfig()
	case "docker":
		cl.applyDockerModeConfig()
	case "local":
		// 本地模式使用默认配置
		log.Info("使用本地开发模式配置")
	default:
		log.Warnf("未知的部署模式: %s，使用默认配置", cl.deployMode)
	}
	return nil
}

// applyHTTPModeConfig 应用HTTP模式配置
func (cl *ConfigLoader) applyHTTPModeConfig() {
	log.Info("应用HTTP模式配置")

	// 覆盖基本配置
	beego.AppConfig.Set("httpport", "8005")
	beego.AppConfig.Set("redisdbnum", "1")
	beego.AppConfig.Set("syncmessagebusinessuri", "http://150.158.2.208:9100/wic/wechat/{0}/SyncMessage")
}

// applyTCPModeConfig 应用TCP模式配置
func (cl *ConfigLoader) applyTCPModeConfig() {
	log.Info("应用TCP模式配置")

	beego.AppConfig.Set("httpport", "8057")
	beego.AppConfig.Set("redisdbnum", "7")
	beego.AppConfig.Set("syncmessagebusinessuri", "http://150.158.2.208:9100/wic/wechat/{0}/SyncMessage")
}

// applyDockerModeConfig 应用Docker模式配置
func (cl *ConfigLoader) applyDockerModeConfig() {
	log.Info("应用Docker模式配置")

	// Docker环境通常使用环境变量，这里设置一些默认值
	beego.AppConfig.Set("httpaddr", "0.0.0.0")
	beego.AppConfig.Set("runmode", "prod")
}

// applyEnvironmentOverrides 应用环境变量覆盖
func (cl *ConfigLoader) applyEnvironmentOverrides() {
	log.Info("应用环境变量覆盖配置")

	// 定义环境变量映射
	envMappings := map[string]string{
		"HTTP_PORT":        "httpport",
		"HTTP_ADDR":        "httpaddr",
		"WS_PORT":          "wsport",
		"RUN_MODE":         "runmode",
		"REDIS_LINK":       "redislink",
		"REDIS_PASS":       "redispass",
		"REDIS_DB":         "redisdbnum",
		"OCR_URL":          "ocrurl",
		"SYNC_MESSAGE_URI": "syncmessagebusinessuri",
		"API_KEY":          "api_key",          // 新的统一API密钥
		"API_KEY_ENABLED":  "api_key_enabled",
		// 向后兼容的环境变量映射
		"API_ACCESS_KEY":   "api_access_key",   // 已废弃，保留向后兼容
		"API_DOCS_KEY":     "api_docs_key",     // 已废弃，保留向后兼容
	}

	// 应用环境变量覆盖
	for envKey, configKey := range envMappings {
		if value := os.Getenv(envKey); value != "" {
			beego.AppConfig.Set(configKey, value)
			log.Debugf("环境变量覆盖: %s = %s", configKey, value)
		}
	}

	// 处理布尔类型的环境变量
	boolEnvMappings := map[string]string{
		"API_KEY_ENABLED":      "api_key_enabled",
		"API_DOCS_KEY_ENABLED": "api_docs_key_enabled",
		"LONG_LINK_ENABLED":    "longlinkenabled",
		"SESSION_ENABLED":      "sessionon",
	}

	for envKey, configKey := range boolEnvMappings {
		if value := os.Getenv(envKey); value != "" {
			if boolValue, err := strconv.ParseBool(value); err == nil {
				beego.AppConfig.Set(configKey, fmt.Sprintf("%t", boolValue))
				log.Debugf("环境变量覆盖(布尔): %s = %t", configKey, boolValue)
			}
		}
	}

	// 处理整数类型的环境变量
	intEnvMappings := map[string]string{
		"REDIS_DB": "redisdbnum",
	}

	for envKey, configKey := range intEnvMappings {
		if value := os.Getenv(envKey); value != "" {
			if intValue, err := strconv.Atoi(value); err == nil {
				beego.AppConfig.Set(configKey, fmt.Sprintf("%d", intValue))
				log.Debugf("环境变量覆盖(整数): %s = %d", configKey, intValue)
			}
		}
	}
}

// GetDeployMode 获取当前部署模式
func (cl *ConfigLoader) GetDeployMode() string {
	return cl.deployMode
}

// ValidateConfig 验证配置
func (cl *ConfigLoader) ValidateConfig() error {
	// 验证Redis配置
	redisLink := beego.AppConfig.DefaultString("redislink", "")
	if redisLink == "" {
		return fmt.Errorf("Redis连接地址不能为空")
	}

	// 验证端口配置
	httpPort := beego.AppConfig.DefaultString("httpport", "8059")
	if port, err := strconv.Atoi(httpPort); err != nil || port <= 0 || port > 65535 {
		return fmt.Errorf("HTTP端口配置无效: %s", httpPort)
	}

	// 验证API密钥配置
	if GetUnifiedAPIKeyEnabled() {
		apiKey := GetUnifiedAPIKey()
		if apiKey == "" {
			return fmt.Errorf("启用API密钥验证时，API密钥不能为空")
		}
	}

	log.Info("配置验证通过")
	return nil
}

// PrintConfigSummary 打印配置摘要
func (cl *ConfigLoader) PrintConfigSummary() {
	log.Info("=== 配置摘要 ===")
	log.Infof("部署模式: %s", cl.deployMode)
	log.Infof("HTTP地址: %s", beego.AppConfig.DefaultString("httpaddr", "0.0.0.0"))
	log.Infof("HTTP端口: %s", beego.AppConfig.DefaultString("httpport", "8059"))
	log.Infof("运行模式: %s", beego.AppConfig.DefaultString("runmode", "dev"))
	log.Infof("长连接启用: %s", beego.AppConfig.DefaultString("longlinkenabled", "false"))
	log.Infof("Redis地址: %s", beego.AppConfig.DefaultString("redislink", ""))
	log.Infof("Redis数据库: %s", beego.AppConfig.DefaultString("redisdbnum", "0"))
	log.Infof("API密钥验证: %t", GetUnifiedAPIKeyEnabled())
	log.Info("===============")
}
