package Friend

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
	"wechatdll/models/Tools"

	"github.com/golang/protobuf/proto"
	log "github.com/sirupsen/logrus"
)

// 注意：已移除内存缓存，只使用文件缓存



// 缓存文件结构
type ContactCacheData struct {
	Wxid       string        `json:"wxid"`
	Usernames  []string      `json:"usernames"`
	Details    []interface{} `json:"details"`
	CacheTime  time.Time     `json:"cache_time"`
	TotalCount int           `json:"total_count"`
}

// 获取缓存文件路径
func getCacheFilePath(wxid string) string {
	// 获取当前执行文件的目录
	execPath, err := os.Executable()
	if err != nil {
		log.WithError(err).Error("获取执行文件路径失败")
		return ""
	}

	execDir := filepath.Dir(execPath)
	cacheDir := filepath.Join(execDir, "cache")

	// 确保缓存目录存在
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		log.WithError(err).Error("创建缓存目录失败")
		return ""
	}

	return filepath.Join(cacheDir, fmt.Sprintf("%s.json", wxid))
}

// 从文件加载缓存
func loadCacheFromFile(wxid string) (*ContactCacheData, error) {
	filePath := getCacheFilePath(wxid)
	if filePath == "" {
		return nil, fmt.Errorf("无法获取缓存文件路径")
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("缓存文件不存在")
	}

	// 读取文件内容
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取缓存文件失败: %v", err)
	}

	// 解析JSON
	var cacheData ContactCacheData
	if err := json.Unmarshal(data, &cacheData); err != nil {
		return nil, fmt.Errorf("解析缓存文件失败: %v", err)
	}

	return &cacheData, nil
}

// 保存缓存到文件
func saveCacheToFile(wxid string, usernames []string, details []interface{}) error {
	filePath := getCacheFilePath(wxid)
	if filePath == "" {
		return fmt.Errorf("无法获取缓存文件路径")
	}

	cacheData := ContactCacheData{
		Wxid:       wxid,
		Usernames:  usernames,
		Details:    details,
		CacheTime:  time.Now(),
		TotalCount: len(details),
	}

	// 转换为JSON
	jsonData, err := json.MarshalIndent(cacheData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化缓存数据失败: %v", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(filePath, jsonData, 0644); err != nil {
		return fmt.Errorf("写入缓存文件失败: %v", err)
	}

	log.WithFields(log.Fields{
		"wxid":        wxid,
		"file_path":   filePath,
		"total_count": len(details),
		"cache_time":  cacheData.CacheTime.Format("2006-01-02 15:04:05"),
	}).Info("💾 联系人缓存已保存到文件")

	return nil
}

func GetTotalContractList(Data GetContactListParams) models.ResponseResult {
	// 如果不是强制刷新，先检查文件缓存
	if !Data.ForceRefresh {
		if cacheData, err := loadCacheFromFile(Data.Wxid); err == nil {
			// 直接返回缓存文件的完整内容，不做任何处理
			log.WithFields(log.Fields{
				"wxid":             Data.Wxid,
				"cached_count":     cacheData.TotalCount,
				"cache_time":       cacheData.CacheTime.Format("2006-01-02 15:04:05"),
				"file_path":        getCacheFilePath(Data.Wxid),
			}).Info("📁 使用文件缓存的联系人详细信息（完整内容）")

			return models.ResponseResult{
				Code:    0,
				Success: true,
				Message: fmt.Sprintf("成功（来自文件缓存，缓存时间：%s）", cacheData.CacheTime.Format("2006-01-02 15:04:05")),
				Data:    cacheData, // 返回完整的缓存数据结构
			}
		} else {
			log.WithFields(log.Fields{
				"wxid":  Data.Wxid,
				"error": err.Error(),
			}).Info("📁 文件缓存不存在或无效，将重新获取")
		}

	} else {
		log.WithFields(log.Fields{
			"wxid": Data.Wxid,
		}).Info("🔄 强制刷新缓存，跳过缓存检查")
	}

	var allContactUsernames []string // 存储所有联系人用户名

	// 直接重新获取联系人列表（不使用内存缓存）
	currentOffset := Data.Offset

	log.WithFields(log.Fields{
		"wxid": Data.Wxid,
	}).Info("开始获取联系人列表（无缓存）")

	for {
		D, err := comm.GetLoginata(Data.Wxid, nil)
		if err != nil || D == nil || D.Wxid == "" {
			errorMsg := fmt.Sprintf("异常：%v [%v]", "未找到登录信息", Data.Wxid)
			if err != nil {
				errorMsg = fmt.Sprintf("异常：%v", err.Error())
			}
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: errorMsg,
				Data:    nil,
			}
		}

		// 使用现有的InitContactRequest结构体
		req := &mm.InitContactRequest{
			Username:                  proto.String(Data.Wxid),
			CurrentWxcontactSeq:       proto.Int32(Data.CurrentWxcontactSeq),
			CurrentChatRoomContactSeq: proto.Int32(Data.CurrentChatRoomContactSeq),
		}

		reqdata, err := proto.Marshal(req)
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		// 发包
		protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
			Ip:     D.Mmtlsip,
			Host:   D.ShortHost,
			Cgiurl: "/cgi-bin/micromsg-bin/initcontact",
			Proxy:  D.Proxy,
			PackData: Algorithm.PackData{
				Reqdata:          reqdata,
				Cgi:              851,
				Uin:              D.Uin,
				Cookie:           D.Cooike,
				Sessionkey:       D.Sessionkey,
				EncryptType:      5,
				Loginecdhkey:     D.RsaPublicKey,
				Clientsessionkey: D.Clientsessionkey,
				UseCompress:      false,
			},
		}, D.MmtlsKey)

		if err != nil {
			return models.ResponseResult{
				Code:    errtype,
				Success: false,
				Message: err.Error(),
				Data:    nil,
			}
		}

		// 使用现有的InitContactResponse结构体
		Response := mm.InitContactResponse{}
		err = proto.Unmarshal(protobufdata, &Response)
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
				Data:    nil,
			}
		}

		// 将本次获取的联系人用户名添加到列表中
		allContactUsernames = append(allContactUsernames, Response.ContactUsernameList...)

		log.WithFields(log.Fields{
			"wxid":          Data.Wxid,
			"current_page":  len(Response.ContactUsernameList),
			"total_so_far":  len(allContactUsernames),
			"continue_flag": Response.GetCountinueFlag(),
		}).Info("获取通讯录好友分页数据")

		// 检查是否还有更多数据
		if Response.GetCountinueFlag() == 0 || len(Response.ContactUsernameList) == 0 {
			break
		}

		// 更新序列号以获取下一页
		if Response.GetCurrentWxcontactSeq() > 0 {
			Data.CurrentWxcontactSeq = Response.GetCurrentWxcontactSeq()
		}
		if Response.GetCurrentChatRoomContactSeq() > 0 {
			Data.CurrentChatRoomContactSeq = Response.GetCurrentChatRoomContactSeq()
		}

		// 防止无限循环
		currentOffset++
		if currentOffset > 100 { // 最多循环100次
			log.WithFields(log.Fields{
				"wxid": Data.Wxid,
			}).Warn("达到最大循环次数，停止获取")
			break
		}
	}

	log.WithFields(log.Fields{
		"wxid":           Data.Wxid,
		"total_contacts": len(allContactUsernames),
	}).Info("获取全部通讯录好友完成")

	// 检查是否获取到联系人用户名
	if len(allContactUsernames) == 0 {
		log.WithFields(log.Fields{
			"wxid": Data.Wxid,
		}).Warn("⚠️ 没有获取到任何联系人用户名，返回空结果")
		return models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "成功（但没有联系人数据）",
			Data:    []map[string]interface{}{},
		}
	}

	log.WithFields(log.Fields{
		"wxid":           Data.Wxid,
		"total_contacts": len(allContactUsernames),
		"first_few_contacts": func() []string {
			if len(allContactUsernames) > 5 {
				return allContactUsernames[:5]
			}
			return allContactUsernames
		}(),
	}).Info("开始获取详细信息")

	// 并发批量获取联系人详细信息
	var allContactDetails []interface{}
	var detailsMutex sync.Mutex
	batchSize := 20     // 每批最多20个联系人
	maxConcurrency := 5 // 最大并发数

	// 创建批次
	var batches [][]string
	for i := 0; i < len(allContactUsernames); i += batchSize {
		end := i + batchSize
		if end > len(allContactUsernames) {
			end = len(allContactUsernames)
		}
		batches = append(batches, allContactUsernames[i:end])
	}

	// 使用带缓冲的channel控制并发数
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup

	log.WithFields(log.Fields{
		"wxid":          Data.Wxid,
		"total_batches": len(batches),
		"concurrency":   maxConcurrency,
		"batch_size":    batchSize,
	}).Info("🔥 开始并发获取联系人详细信息")

	startTime := time.Now()

	// 立即启动所有goroutine
	log.WithFields(log.Fields{
		"wxid":                 Data.Wxid,
		"launching_goroutines": len(batches),
	}).Info("🚀 正在启动所有并发goroutine...")

	for batchIndex, batch := range batches {
		wg.Add(1)
		go func(batchIdx int, userBatch []string) {
			defer wg.Done()

			userNameListStr := strings.Join(userBatch, ",")

			// 先记录goroutine启动
			log.WithFields(log.Fields{
				"wxid":         Data.Wxid,
				"batch_num":    batchIdx + 1,
				"goroutine_id": fmt.Sprintf("batch_%d", batchIdx+1),
			}).Info("🚀 [并发] Goroutine已启动，等待信号量...")

			// 获取信号量（这里可能会等待）
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			batchStartTime := time.Now()
			log.WithFields(log.Fields{
				"wxid":          Data.Wxid,
				"batch_size":    len(userBatch),
				"batch_num":     batchIdx + 1,
				"total_batches": len(batches),
				"goroutine_id":  fmt.Sprintf("batch_%d", batchIdx+1),
			}).Info("🔥 [并发] 获得信号量，开始处理批次")

			// 调用GetContact获取详细信息
			contactResult := Tools.GetContact(Tools.GetContactParam{
				Wxid:         Data.Wxid,
				UserNameList: userNameListStr,
			})

			// 线程安全地添加结果
			detailsMutex.Lock()
			if contactResult.Success && contactResult.Data != nil {
				// 将结果添加到总列表中
				if dataSlice, ok := contactResult.Data.([]interface{}); ok {
					allContactDetails = append(allContactDetails, dataSlice...)
				} else {
					// 如果不是切片，直接添加
					allContactDetails = append(allContactDetails, contactResult.Data)
				}
			} else {
				// 如果获取详细信息失败，使用基本信息
				for _, username := range userBatch {
					allContactDetails = append(allContactDetails, map[string]interface{}{
						"Wxid":       username,
						"Nickname":   "",
						"RemarkName": "",
					})
				}
			}
			detailsMutex.Unlock()

			batchElapsed := time.Since(batchStartTime)
			log.WithFields(log.Fields{
				"wxid":         Data.Wxid,
				"batch_num":    batchIdx + 1,
				"elapsed":      batchElapsed.String(),
				"goroutine_id": fmt.Sprintf("batch_%d", batchIdx+1),
			}).Info("✅ [并发] 完成联系人详细信息批次")

		}(batchIndex, batch)
	}

	log.WithFields(log.Fields{
		"wxid":             Data.Wxid,
		"total_goroutines": len(batches),
	}).Info("✅ 所有Goroutine已启动，等待完成...")

	// 等待所有goroutine完成
	wg.Wait()

	elapsed := time.Since(startTime)
	log.WithFields(log.Fields{
		"wxid":          Data.Wxid,
		"total_batches": len(batches),
		"elapsed_time":  elapsed.String(),
	}).Info("并发获取联系人详细信息完成")

	// 保存缓存到文件
	if err := saveCacheToFile(Data.Wxid, allContactUsernames, allContactDetails); err != nil {
		log.WithFields(log.Fields{
			"wxid":  Data.Wxid,
			"error": err.Error(),
		}).Error("💾 保存缓存到文件失败")
	}

	// 创建完整的缓存数据结构
	cacheData := ContactCacheData{
		Wxid:       Data.Wxid,
		Usernames:  allContactUsernames,
		Details:    allContactDetails,
		CacheTime:  time.Now(),
		TotalCount: len(allContactDetails),
	}

	log.WithFields(log.Fields{
		"wxid":            Data.Wxid,
		"total_contacts":  len(allContactUsernames),
		"detail_contacts": len(allContactDetails),
	}).Info("获取全部通讯录好友详细信息完成")

	// 返回完整的缓存数据结构
	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    cacheData,
	}
}

// ClearContactCache 清除指定微信号的联系人缓存（仅文件缓存）
func ClearContactCache(wxid string) {
	// 删除文件缓存
	filePath := getCacheFilePath(wxid)
	if filePath != "" {
		if err := os.Remove(filePath); err != nil {
			if !os.IsNotExist(err) {
				log.WithFields(log.Fields{
					"wxid":      wxid,
					"file_path": filePath,
					"error":     err.Error(),
				}).Error("删除缓存文件失败")
			}
		} else {
			log.WithFields(log.Fields{
				"wxid":      wxid,
				"file_path": filePath,
			}).Info("📁 已删除缓存文件")
		}
	}

	log.WithFields(log.Fields{
		"wxid": wxid,
	}).Info("已清除联系人缓存（仅文件）")
}

// ClearAllContactCache 清除所有联系人缓存（仅文件缓存）
func ClearAllContactCache() {
	// 删除所有缓存文件
	execPath, err := os.Executable()
	if err != nil {
		log.WithError(err).Error("获取执行文件路径失败")
		return
	}

	execDir := filepath.Dir(execPath)
	cacheDir := filepath.Join(execDir, "cache")

	if _, err := os.Stat(cacheDir); !os.IsNotExist(err) {
		files, err := ioutil.ReadDir(cacheDir)
		if err != nil {
			log.WithError(err).Error("读取缓存目录失败")
			return
		}

		deletedCount := 0
		for _, file := range files {
			if filepath.Ext(file.Name()) == ".json" {
				filePath := filepath.Join(cacheDir, file.Name())
				if err := os.Remove(filePath); err != nil {
					log.WithFields(log.Fields{
						"file_path": filePath,
						"error":     err.Error(),
					}).Error("删除缓存文件失败")
				} else {
					deletedCount++
				}
			}
		}

		log.WithFields(log.Fields{
			"deleted_files": deletedCount,
			"cache_dir":     cacheDir,
		}).Info("📁 已删除所有缓存文件")
	}

	log.Info("已清除所有联系人缓存（仅文件）")
}

// ClearContactDetailCache 清除指定参数的联系人详情缓存
func ClearContactDetailCache(wxid, towxids, chatRoom string) {
	// 删除文件缓存
	filePath := getDetailCacheFilePath(wxid, towxids, chatRoom)
	if filePath != "" {
		if err := os.Remove(filePath); err != nil {
			if !os.IsNotExist(err) {
				log.WithFields(log.Fields{
					"wxid":      wxid,
					"towxids":   towxids,
					"chat_room": chatRoom,
					"file_path": filePath,
					"error":     err.Error(),
				}).Error("删除联系人详情缓存文件失败")
			}
		} else {
			log.WithFields(log.Fields{
				"wxid":      wxid,
				"towxids":   towxids,
				"chat_room": chatRoom,
				"file_path": filePath,
			}).Info("📁 已删除联系人详情缓存文件")
		}
	}
}

// ClearAllContactDetailCache 清除所有联系人详情缓存
func ClearAllContactDetailCache() {
	// 删除所有联系人详情缓存文件
	execPath, err := os.Executable()
	if err != nil {
		log.WithError(err).Error("获取执行文件路径失败")
		return
	}

	execDir := filepath.Dir(execPath)
	cacheDir := filepath.Join(execDir, "cache")

	if _, err := os.Stat(cacheDir); !os.IsNotExist(err) {
		files, err := ioutil.ReadDir(cacheDir)
		if err != nil {
			log.WithError(err).Error("读取联系人详情缓存目录失败")
			return
		}

		deletedCount := 0
		for _, file := range files {
			if filepath.Ext(file.Name()) == ".json" {
				filePath := filepath.Join(cacheDir, file.Name())
				if err := os.Remove(filePath); err != nil {
					log.WithFields(log.Fields{
						"file_path": filePath,
						"error":     err.Error(),
					}).Error("删除联系人详情缓存文件失败")
				} else {
					deletedCount++
				}
			}
		}

		log.WithFields(log.Fields{
			"deleted_files": deletedCount,
			"cache_dir":     cacheDir,
		}).Info("📁 已删除所有联系人详情缓存文件")
	}

	log.Info("已清除所有联系人详情缓存")
}
