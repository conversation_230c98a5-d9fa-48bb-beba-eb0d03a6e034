//go:build linux
// +build linux

package TcpPoll

import (
	"fmt"
	"net"
	"reflect"
	"sync"
	"syscall"

	log "github.com/sirupsen/logrus"
	"golang.org/x/sys/unix"
)

type epoll struct {
	fd          int              // poll的文件描述
	connections map[int]net.Conn // 长连接列表, key: fd, value: conn
	lock        *sync.RWMutex    // 读写锁
}

// 获取连接池
func MkEpoll() (*epoll, error) {
	fd, err := unix.EpollCreate1(0)
	if err != nil {
		return nil, err
	}
	return &epoll{
		fd:          fd,
		lock:        &sync.RWMutex{},
		connections: make(map[int]net.Conn),
	}, nil
}

// 连接池增加长连接
func (e *epoll) Add(conn net.Conn) (int, error) {
	if conn == nil {
		return 0, fmt.Errorf("连接不能为空")
	}

	// Extract file descriptor associated with the connection
	fd := socketFD(conn)
	if fd <= 0 {
		return 0, fmt.Errorf("无效的文件描述符: %d", fd)
	}

	// 添加到epoll，监听读事件、挂起事件和优先级事件
	err := unix.EpollCtl(e.fd, syscall.EPOLL_CTL_ADD, fd, &unix.EpollEvent{
		Events: unix.POLLIN | unix.POLLHUP | unix.EPOLLPRI | unix.EPOLLERR,
		Fd:     int32(fd),
	})
	if err != nil {
		return 0, fmt.Errorf("添加到epoll失败: %w", err)
	}

	e.lock.Lock()
	defer e.lock.Unlock()
	e.connections[fd] = conn

	// 每100个连接记录一次日志
	if len(e.connections)%100 == 0 {
		log.Infof("当前连接数: %d", len(e.connections))
	}

	log.Debugf("成功添加连接到epoll: fd=%d", fd)
	return fd, nil
}

// 连接池移除长连接
func (e *epoll) Remove(conn net.Conn) error {
	if conn == nil {
		log.Warn("尝试移除空连接")
		return nil
	}

	fd := socketFD(conn)
	if fd <= 0 {
		log.Warnf("无效的文件描述符: %d", fd)
		return nil
	}

	// 从epoll中移除
	err := unix.EpollCtl(e.fd, syscall.EPOLL_CTL_DEL, fd, nil)
	if err != nil {
		log.Errorf("从epoll移除连接失败: fd=%d, error=%v", fd, err)
		// 即使epoll移除失败，也要从连接池中移除
	}

	e.lock.Lock()
	defer e.lock.Unlock()
	delete(e.connections, fd)

	// 每100个连接记录一次日志
	if len(e.connections)%100 == 0 {
		log.Infof("当前连接数: %d", len(e.connections))
	}

	log.Debugf("成功从epoll移除连接: fd=%d", fd)
	return err
}

// Wait 等待epoll事件
func (e *epoll) Wait() ([]int, error) {
	const maxEvents = 100
	events := make([]unix.EpollEvent, maxEvents)

retry:
	// 设置较短的超时时间（100ms），以便及时响应停止信号
	n, err := unix.EpollWait(e.fd, events, 100)
	if err != nil {
		if err == unix.EINTR {
			// 被信号中断，重试
			goto retry
		}
		return nil, fmt.Errorf("epoll wait 失败: %w", err)
	}

	if n == 0 {
		// 超时，返回空结果
		return []int{}, nil
	}

	// 使用读锁保护连接池
	e.lock.RLock()
	defer e.lock.RUnlock()

	var fds []int
	for i := 0; i < n; i++ {
		fd := int(events[i].Fd)

		// 检查事件类型
		if events[i].Events&(unix.POLLHUP|unix.EPOLLERR) != 0 {
			log.Debugf("检测到连接异常: fd=%d, events=0x%x", fd, events[i].Events)
		}

		// 确保fd对应的连接仍然存在
		if _, exists := e.connections[fd]; exists {
			fds = append(fds, fd)
		} else {
			log.Warnf("收到不存在连接的事件: fd=%d", fd)
		}
	}

	return fds, nil
}

// socketFD 获取长连接的文件描述符fd
func socketFD(conn net.Conn) int {
	if conn == nil {
		log.Error("连接为空，无法获取文件描述符")
		return 0
	}

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("获取文件描述符时发生panic: %v", r)
		}
	}()

	// 使用反射获取底层的文件描述符
	// 这种方法依赖于Go标准库的内部实现，可能在不同版本间有变化
	connVal := reflect.ValueOf(conn)
	if connVal.Kind() == reflect.Ptr {
		connVal = connVal.Elem()
	}

	// 尝试获取conn字段
	connField := connVal.FieldByName("conn")
	if !connField.IsValid() {
		log.Error("无法找到conn字段")
		return 0
	}

	// 获取fd字段
	fdField := connField.FieldByName("fd")
	if !fdField.IsValid() {
		log.Error("无法找到fd字段")
		return 0
	}

	// 获取pfd字段
	pfdField := reflect.Indirect(fdField).FieldByName("pfd")
	if !pfdField.IsValid() {
		log.Error("无法找到pfd字段")
		return 0
	}

	// 获取Sysfd字段
	sysfdField := pfdField.FieldByName("Sysfd")
	if !sysfdField.IsValid() {
		log.Error("无法找到Sysfd字段")
		return 0
	}

	fd := int(sysfdField.Int())
	if fd <= 0 {
		log.Errorf("获取到无效的文件描述符: %d", fd)
		return 0
	}

	return fd
}
