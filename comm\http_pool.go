package comm

import (
	"context"
	"crypto/tls"
	"net"
	"net/http"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// OptimizedHTTPPoolConfig 优化的HTTP连接池配置
type OptimizedHTTPPoolConfig struct {
	MaxIdleConns          int           `json:"max_idle_conns"`          // 最大空闲连接数
	MaxIdleConnsPerHost   int           `json:"max_idle_conns_per_host"` // 每个主机的最大空闲连接数
	MaxConnsPerHost       int           `json:"max_conns_per_host"`      // 每个主机的最大连接数
	IdleConnTimeout       time.Duration `json:"idle_conn_timeout"`       // 空闲连接超时
	DialTimeout           time.Duration `json:"dial_timeout"`            // 拨号超时
	KeepAlive             time.Duration `json:"keep_alive"`              // 保持连接时间
	TLSHandshakeTimeout   time.Duration `json:"tls_handshake_timeout"`   // TLS握手超时
	ResponseHeaderTimeout time.Duration `json:"response_header_timeout"` // 响应头超时
	ExpectContinueTimeout time.Duration `json:"expect_continue_timeout"` // Expect Continue超时
}

// DefaultOptimizedHTTPPoolConfig 默认优化HTTP连接池配置
var DefaultOptimizedHTTPPoolConfig = OptimizedHTTPPoolConfig{
	MaxIdleConns:          100,
	MaxIdleConnsPerHost:   10,
	MaxConnsPerHost:       50,
	IdleConnTimeout:       90 * time.Second,
	DialTimeout:           10 * time.Second,
	KeepAlive:             30 * time.Second,
	TLSHandshakeTimeout:   10 * time.Second,
	ResponseHeaderTimeout: 30 * time.Second,
	ExpectContinueTimeout: 1 * time.Second,
}

// HTTPPool HTTP连接池管理器
type HTTPPool struct {
	clients map[string]*http.Client
	mutex   sync.RWMutex
	config  OptimizedHTTPPoolConfig

	// 统计信息
	stats HTTPPoolStats
}

// HTTPPoolStats HTTP连接池统计信息
type HTTPPoolStats struct {
	TotalRequests   int64 `json:"total_requests"`
	ActiveClients   int   `json:"active_clients"`
	SuccessRequests int64 `json:"success_requests"`
	FailedRequests  int64 `json:"failed_requests"`
	AvgResponseTime int64 `json:"avg_response_time_ms"`
}

// NewHTTPPool 创建HTTP连接池
func NewHTTPPool(config OptimizedHTTPPoolConfig) *HTTPPool {
	return &HTTPPool{
		clients: make(map[string]*http.Client),
		config:  config,
	}
}

// GetClient 获取HTTP客户端
func (pool *HTTPPool) GetClient(key string) *http.Client {
	pool.mutex.RLock()
	client, exists := pool.clients[key]
	pool.mutex.RUnlock()

	if exists {
		return client
	}

	// 创建新的客户端
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// 双重检查
	if client, exists := pool.clients[key]; exists {
		return client
	}

	// 创建优化的HTTP客户端
	client = pool.createOptimizedClient()
	pool.clients[key] = client

	log.Debugf("创建新的HTTP客户端: %s", key)
	return client
}

// createOptimizedClient 创建优化的HTTP客户端
func (pool *HTTPPool) createOptimizedClient() *http.Client {
	// 创建自定义的Transport
	transport := &http.Transport{
		// 连接池配置
		MaxIdleConns:        pool.config.MaxIdleConns,
		MaxIdleConnsPerHost: pool.config.MaxIdleConnsPerHost,
		MaxConnsPerHost:     pool.config.MaxConnsPerHost,
		IdleConnTimeout:     pool.config.IdleConnTimeout,

		// 拨号配置
		DialContext: (&net.Dialer{
			Timeout:   pool.config.DialTimeout,
			KeepAlive: pool.config.KeepAlive,
		}).DialContext,

		// TLS配置
		TLSHandshakeTimeout: pool.config.TLSHandshakeTimeout,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false, // 生产环境应该验证证书
		},

		// 其他超时配置
		ResponseHeaderTimeout: pool.config.ResponseHeaderTimeout,
		ExpectContinueTimeout: pool.config.ExpectContinueTimeout,

		// 启用HTTP/2
		ForceAttemptHTTP2: true,

		// 禁用压缩（如果需要手动控制）
		DisableCompression: false,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second, // 总体请求超时
	}
}

// DoRequest 执行HTTP请求（带统计）
func (pool *HTTPPool) DoRequest(client *http.Client, req *http.Request) (*http.Response, error) {
	startTime := time.Now()

	// 增加请求计数
	pool.mutex.Lock()
	pool.stats.TotalRequests++
	pool.mutex.Unlock()

	// 执行请求
	resp, err := client.Do(req)

	// 更新统计信息
	duration := time.Since(startTime)
	pool.updateStats(err == nil, duration)

	return resp, err
}

// DoRequestWithContext 执行带上下文的HTTP请求
func (pool *HTTPPool) DoRequestWithContext(ctx context.Context, client *http.Client, req *http.Request) (*http.Response, error) {
	req = req.WithContext(ctx)
	return pool.DoRequest(client, req)
}

// updateStats 更新统计信息
func (pool *HTTPPool) updateStats(success bool, duration time.Duration) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	if success {
		pool.stats.SuccessRequests++
	} else {
		pool.stats.FailedRequests++
	}

	// 更新平均响应时间（简单移动平均）
	if pool.stats.TotalRequests > 0 {
		currentAvg := time.Duration(pool.stats.AvgResponseTime) * time.Millisecond
		newAvg := (currentAvg + duration) / 2
		pool.stats.AvgResponseTime = int64(newAvg / time.Millisecond)
	}
}

// GetStats 获取连接池统计信息
func (pool *HTTPPool) GetStats() HTTPPoolStats {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()

	stats := pool.stats
	stats.ActiveClients = len(pool.clients)
	return stats
}

// CloseIdleConnections 关闭空闲连接
func (pool *HTTPPool) CloseIdleConnections() {
	pool.mutex.RLock()
	clients := make([]*http.Client, 0, len(pool.clients))
	for _, client := range pool.clients {
		clients = append(clients, client)
	}
	pool.mutex.RUnlock()

	for _, client := range clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
	}

	log.Debug("关闭了所有空闲连接")
}

// Close 关闭连接池
func (pool *HTTPPool) Close() {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	for key, client := range pool.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
		delete(pool.clients, key)
	}

	log.Info("HTTP连接池已关闭")
}

// 全局HTTP连接池实例
var (
	globalHTTPPool *HTTPPool
	httpPoolOnce   sync.Once
)

// GetHTTPPool 获取全局HTTP连接池
func GetHTTPPool() *HTTPPool {
	httpPoolOnce.Do(func() {
		globalHTTPPool = NewHTTPPool(DefaultOptimizedHTTPPoolConfig)
	})
	return globalHTTPPool
}

// HTTPClientManager HTTP客户端管理器
type HTTPClientManager struct {
	pool *HTTPPool
}

// NewHTTPClientManager 创建HTTP客户端管理器
func NewHTTPClientManager() *HTTPClientManager {
	return &HTTPClientManager{
		pool: GetHTTPPool(),
	}
}

// GetClientForHost 为特定主机获取HTTP客户端
func (manager *HTTPClientManager) GetClientForHost(host string) *http.Client {
	return manager.pool.GetClient(host)
}

// GetDefaultClient 获取默认HTTP客户端
func (manager *HTTPClientManager) GetDefaultClient() *http.Client {
	return manager.pool.GetClient("default")
}

// DoRequest 执行HTTP请求
func (manager *HTTPClientManager) DoRequest(req *http.Request) (*http.Response, error) {
	client := manager.GetClientForHost(req.URL.Host)
	return manager.pool.DoRequest(client, req)
}

// DoRequestWithTimeout 执行带超时的HTTP请求
func (manager *HTTPClientManager) DoRequestWithTimeout(req *http.Request, timeout time.Duration) (*http.Response, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	client := manager.GetClientForHost(req.URL.Host)
	return manager.pool.DoRequestWithContext(ctx, client, req)
}

// GetStats 获取管理器统计信息
func (manager *HTTPClientManager) GetStats() HTTPPoolStats {
	return manager.pool.GetStats()
}

// 全局HTTP客户端管理器
var (
	globalHTTPClientManager *HTTPClientManager
	httpManagerOnce         sync.Once
)

// GetHTTPClientManager 获取全局HTTP客户端管理器
func GetHTTPClientManager() *HTTPClientManager {
	httpManagerOnce.Do(func() {
		globalHTTPClientManager = NewHTTPClientManager()
	})
	return globalHTTPClientManager
}
