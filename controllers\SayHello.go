package controllers

import (
	"wechatdll/models/SayHello"

	log "github.com/sirupsen/logrus"
)

// 打招呼模块
type SayHelloController struct {
	BaseController
}

// @Summary 模式1-扫码
// @Description 执行模式1扫码打招呼操作，注意请先执行1再执行2
// @Param	body			body	SayHello.Model1Param	 true		"扫码参数"
// @Success 200 {object} models.StandardResponse "操作成功"
// @Failure 400 {object} models.StandardResponse "参数错误"
// @Failure 500 {object} models.StandardResponse "系统错误"
// @router /Modelv1 [post]
func (c *SayHelloController) ModelV1() {
	var data SayHello.Model1Param

	// 使用统一的请求解析和验证
	if err := c.ValidateAndParseRequest(&data); err != nil {
		return // 错误已在ValidateAndParseRequest中处理
	}

	// 记录调试信息（仅开发模式）
	log.Debugf("SayHello ModelV1 请求参数: %+v", data)

	// 执行业务逻辑
	result := SayHello.Model1(data)

	// 返回结果 - 这里暂时保持兼容性，后续可以进一步优化
	c.Data["json"] = &result
	c.ServeJSON()
}

// @Summary 模式2-一键打招呼
// @Description 执行模式2一键打招呼操作，Scene为招呼通道，FromScene为搜索联系人场景
// @Param	body			body	SayHello.Model2Param	 true		"一键打招呼参数"
// @Success 200 {object} models.StandardResponse "操作成功"
// @Failure 400 {object} models.StandardResponse "参数错误"
// @Failure 500 {object} models.StandardResponse "系统错误"
// @router /Modelv2 [post]
func (c *SayHelloController) Modelv2() {
	var data SayHello.Model2Param

	// 使用统一的请求解析和验证
	if err := c.ValidateAndParseRequest(&data); err != nil {
		return // 错误已在ValidateAndParseRequest中处理
	}

	// 记录调试信息（仅开发模式）
	log.Debugf("SayHello ModelV2 请求参数: %+v", data)

	// 执行业务逻辑
	result := SayHello.Model2(data)

	// 返回结果 - 这里暂时保持兼容性，后续可以进一步优化
	c.Data["json"] = &result
	c.ServeJSON()
}

// @Summary 模式3-v3\v4打招呼
// @Description 执行模式3 v3/v4打招呼操作，Scene为招呼通道，v4参数可选
// @Param	body			body	SayHello.SendRequestParam1	 true		"v3/v4打招呼参数"
// @Success 200 {object} models.StandardResponse "操作成功"
// @Failure 400 {object} models.StandardResponse "参数错误"
// @Failure 500 {object} models.StandardResponse "系统错误"
// @router /Modelv3 [post]
func (c *SayHelloController) Modelv3() {
	var data SayHello.SendRequestParam1

	// 使用统一的请求解析和验证
	if err := c.ValidateAndParseRequest(&data); err != nil {
		return // 错误已在ValidateAndParseRequest中处理
	}

	// 参数预处理：v4参数可选，如果为空字符串或"string"则设为空
	if data.V4 == "" || data.V4 == "string" {
		data.V4 = ""
	}

	// 记录调试信息（仅开发模式）
	log.Debugf("SayHello ModelV3 请求参数: %+v", data)

	// 执行业务逻辑
	result := SayHello.Model3(data)

	// 返回结果 - 这里暂时保持兼容性，后续可以进一步优化
	c.Data["json"] = &result
	c.ServeJSON()
}
