package controllers

import (
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// EnhancedBaseController 增强的基础控制器
// 提供更多便利方法来减少重复代码
type EnhancedBaseController struct {
	BaseController
	responseHelper *comm.ResponseHelper
	jsonHelper     *comm.JSONHelper
	validator      *comm.ValidationHelper
}

// Prepare 控制器准备方法，在每个请求前调用
func (c *EnhancedBaseController) Prepare() {
	c.responseHelper = comm.NewResponseHelper(c.Ctx)
	c.jsonHelper = comm.GetJSONHelper()
	c.validator = comm.GetValidationHelper()
}

// ParseAndValidateRequest 解析并验证请求的通用方法
// 这个方法减少了大量重复的JSON解析和错误处理代码
func (c *EnhancedBaseController) ParseAndValidateRequest(v interface{}) bool {
	// 解析JSON请求
	if err := c.jsonHelper.ParseRequest(c.Ctx.Input.RequestBody, v); err != nil {
		c.responseHelper.SendValidationError(err)
		return false
	}
	
	// 如果实现了验证接口，则进行验证
	if validator, ok := v.(models.RequestValidator); ok {
		if err := validator.Validate(); err != nil {
			c.responseHelper.SendValidationError(err)
			return false
		}
	}
	
	// 记录请求参数（仅开发模式）
	if beego.BConfig.RunMode == "dev" {
		log.Debugf("请求参数: %+v", v)
	}
	
	return true
}

// SendSuccess 发送成功响应
func (c *EnhancedBaseController) SendSuccess(data interface{}, message string) {
	c.responseHelper.SendSuccess(data, message)
}

// SendError 发送错误响应
func (c *EnhancedBaseController) SendError(code int64, message string) {
	c.responseHelper.SendError(code, message, 400)
}

// SendBusinessError 发送业务错误响应
func (c *EnhancedBaseController) SendBusinessError(err error) {
	c.responseHelper.SendBusinessError(err)
}

// SendWXError 发送微信API错误响应
func (c *EnhancedBaseController) SendWXError(wxErr error, wxCode int32, defaultMessage string) {
	c.responseHelper.SendWXError(wxErr, wxCode, defaultMessage)
}

// SendNetworkError 发送网络错误响应
func (c *EnhancedBaseController) SendNetworkError(err error) {
	c.responseHelper.SendNetworkError(err)
}

// ExecuteWithErrorHandling 执行业务逻辑并处理错误的通用方法
// 这个方法可以大大减少控制器中的重复错误处理代码
func (c *EnhancedBaseController) ExecuteWithErrorHandling(businessLogic func() (interface{}, error), successMessage string) {
	result, err := businessLogic()
	if err != nil {
		// 根据错误类型自动选择合适的错误处理方式
		if apiErr, ok := err.(*models.APIError); ok {
			c.SendError(apiErr.Code, apiErr.Message)
		} else {
			c.SendBusinessError(err)
		}
		return
	}
	
	c.SendSuccess(result, successMessage)
}

// ExecuteWXAPIWithErrorHandling 执行微信API调用并处理错误
func (c *EnhancedBaseController) ExecuteWXAPIWithErrorHandling(
	wxAPICall func() (interface{}, error, int32), 
	successMessage string,
	defaultErrorMessage string) {
	
	result, err, wxCode := wxAPICall()
	if err != nil {
		c.SendWXError(err, wxCode, defaultErrorMessage)
		return
	}
	
	c.SendSuccess(result, successMessage)
}

// ValidateWxid 验证微信ID的便利方法
func (c *EnhancedBaseController) ValidateWxid(wxid string) bool {
	if err := c.validator.ValidateWxid(wxid); err != nil {
		c.responseHelper.SendValidationError(err)
		return false
	}
	return true
}

// ValidateRequired 验证必填字段的便利方法
func (c *EnhancedBaseController) ValidateRequired(value, fieldName string) bool {
	if err := c.validator.ValidateRequired(value, fieldName); err != nil {
		c.responseHelper.SendValidationError(err)
		return false
	}
	return true
}

// LogOperation 记录操作日志的便利方法
func (c *EnhancedBaseController) LogOperation(operation string, params interface{}) {
	log.WithFields(log.Fields{
		"operation":  operation,
		"params":     params,
		"method":     c.Ctx.Request.Method,
		"path":       c.Ctx.Request.URL.Path,
		"ip":         c.Ctx.Input.IP(),
		"user_agent": c.Ctx.Request.UserAgent(),
	}).Info("API操作")
}

// GetClientInfo 获取客户端信息
func (c *EnhancedBaseController) GetClientInfo() map[string]string {
	return map[string]string{
		"ip":         c.Ctx.Input.IP(),
		"user_agent": c.Ctx.Request.UserAgent(),
		"method":     c.Ctx.Request.Method,
		"path":       c.Ctx.Request.URL.Path,
	}
}

// IsDevMode 检查是否为开发模式
func (c *EnhancedBaseController) IsDevMode() bool {
	return beego.BConfig.RunMode == "dev"
}

// 示例：如何使用EnhancedBaseController重构现有控制器
/*
type ExampleController struct {
	EnhancedBaseController
}

func (c *ExampleController) SomeAction() {
	var req SomeRequest
	
	// 一行代码完成解析和验证
	if !c.ParseAndValidateRequest(&req) {
		return
	}
	
	// 记录操作
	c.LogOperation("SomeAction", req)
	
	// 执行业务逻辑并自动处理错误
	c.ExecuteWithErrorHandling(func() (interface{}, error) {
		return SomeBusinessLogic(req)
	}, "操作成功")
}

func (c *ExampleController) WXAPIAction() {
	var req WXAPIRequest
	
	if !c.ParseAndValidateRequest(&req) {
		return
	}
	
	// 执行微信API调用并自动处理错误
	c.ExecuteWXAPIWithErrorHandling(func() (interface{}, error, int32) {
		return CallWXAPI(req)
	}, "微信API调用成功", "微信API调用失败")
}
*/

// ControllerTemplate 控制器模板，展示最佳实践
type ControllerTemplate struct {
	EnhancedBaseController
}

// TemplateAction 模板方法，展示如何使用增强的基础控制器
func (c *ControllerTemplate) TemplateAction() {
	// 1. 定义请求结构
	var request struct {
		Wxid    string `json:"wxid" validate:"required"`
		Content string `json:"content" validate:"required"`
	}
	
	// 2. 解析和验证请求（一行代码）
	if !c.ParseAndValidateRequest(&request) {
		return // 错误已自动处理
	}
	
	// 3. 记录操作日志
	c.LogOperation("TemplateAction", request)
	
	// 4. 执行业务逻辑并自动处理错误
	c.ExecuteWithErrorHandling(func() (interface{}, error) {
		// 这里放置实际的业务逻辑
		return map[string]string{
			"result": "success",
			"wxid":   request.Wxid,
		}, nil
	}, "操作成功完成")
}
