package middleware

import (
	"compress/gzip"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
	"wechatdll/comm"

	"github.com/astaxie/beego/context"
)

// CompressionMiddleware 响应压缩中间件
type CompressionMiddleware struct {
	gzipWriterPool *sync.Pool
	minSize        int
	level          int
}

// gzipResponseWriter 压缩响应写入器
type gzipResponseWriter struct {
	http.ResponseWriter
	gzipWriter *gzip.Writer
	written    bool
}

var (
	compressionMiddleware *CompressionMiddleware
	compressionOnce       sync.Once
)

// GetCompressionMiddleware 获取压缩中间件实例
func GetCompressionMiddleware() *CompressionMiddleware {
	compressionOnce.Do(func() {
		compressionMiddleware = &CompressionMiddleware{
			minSize: 1024,           // 最小压缩大小 1KB
			level:   gzip.BestSpeed, // 使用最快压缩级别
		}

		// 创建gzip写入器池
		compressionMiddleware.gzipWriterPool = &sync.Pool{
			New: func() interface{} {
				writer, _ := gzip.NewWriterLevel(io.Discard, compressionMiddleware.level)
				return writer
			},
		}
	})
	return compressionMiddleware
}

// Handler 压缩中间件处理函数
func (cm *CompressionMiddleware) Handler(ctx *context.Context) {
	// 检查客户端是否支持gzip
	if !cm.shouldCompress(ctx) {
		return
	}

	// 设置压缩相关头部
	ctx.Output.Header("Vary", "Accept-Encoding")

	// 标记需要压缩（实际压缩在输出时进行）
	ctx.Input.SetData("compression_enabled", true)

	// 记录压缩指标
	perfMonitor := comm.GetPerformanceMonitor()
	if perfMonitor != nil {
		perfMonitor.RecordMetric("compression_requests", 1, "count", map[string]string{
			"type": "gzip",
		})
	}
}

// shouldCompress 检查是否应该压缩
func (cm *CompressionMiddleware) shouldCompress(ctx *context.Context) bool {
	// 检查Accept-Encoding头
	acceptEncoding := ctx.Input.Header("Accept-Encoding")
	if !strings.Contains(acceptEncoding, "gzip") {
		return false
	}

	// 检查Content-Type
	contentType := ctx.ResponseWriter.Header().Get("Content-Type")
	if contentType == "" {
		contentType = "application/json" // 默认类型
	}

	// 只压缩文本类型的响应
	compressibleTypes := []string{
		"application/json",
		"application/xml",
		"text/html",
		"text/plain",
		"text/css",
		"text/javascript",
		"application/javascript",
	}

	for _, cType := range compressibleTypes {
		if strings.Contains(contentType, cType) {
			return true
		}
	}

	return false
}

// getGzipWriter 从池中获取gzip写入器
func (cm *CompressionMiddleware) getGzipWriter(w http.ResponseWriter) *gzip.Writer {
	gzipWriter := cm.gzipWriterPool.Get().(*gzip.Writer)
	gzipWriter.Reset(w)
	return gzipWriter
}

// putGzipWriter 将gzip写入器归还到池
func (cm *CompressionMiddleware) putGzipWriter(gw *gzip.Writer) {
	gw.Close()
	cm.gzipWriterPool.Put(gw)
}

// GetGzipWriter 公开的获取gzip写入器方法
func (cm *CompressionMiddleware) GetGzipWriter(w http.ResponseWriter) *gzip.Writer {
	return cm.getGzipWriter(w)
}

// PutGzipWriter 公开的归还gzip写入器方法
func (cm *CompressionMiddleware) PutGzipWriter(gw *gzip.Writer) {
	cm.putGzipWriter(gw)
}

// Write 实现http.ResponseWriter接口
func (grw *gzipResponseWriter) Write(data []byte) (int, error) {
	if !grw.written {
		grw.written = true
		// 如果数据太小，不进行压缩
		if len(data) < 1024 {
			return grw.ResponseWriter.Write(data)
		}
	}

	return grw.gzipWriter.Write(data)
}

// WriteHeader 实现http.ResponseWriter接口
func (grw *gzipResponseWriter) WriteHeader(statusCode int) {
	grw.ResponseWriter.WriteHeader(statusCode)
}

// Header 实现http.ResponseWriter接口
func (grw *gzipResponseWriter) Header() http.Header {
	return grw.ResponseWriter.Header()
}

// Flush 实现http.Flusher接口
func (grw *gzipResponseWriter) Flush() {
	if grw.gzipWriter != nil {
		grw.gzipWriter.Flush()
	}
	if flusher, ok := grw.ResponseWriter.(http.Flusher); ok {
		flusher.Flush()
	}
}

// Close 关闭gzip写入器
func (grw *gzipResponseWriter) Close() error {
	if grw.gzipWriter != nil {
		return grw.gzipWriter.Close()
	}
	return nil
}

// CompressionFilter Beego过滤器函数
func CompressionFilter(ctx *context.Context) {
	GetCompressionMiddleware().Handler(ctx)
}

// 统计信息
type CompressionStats struct {
	TotalRequests      int64   `json:"total_requests"`
	CompressedRequests int64   `json:"compressed_requests"`
	CompressionRatio   float64 `json:"compression_ratio"`
	BytesSaved         int64   `json:"bytes_saved"`
}

// GetCompressionStats 获取压缩统计信息
func GetCompressionStats() *CompressionStats {
	// 这里可以从性能监控器获取实际统计数据
	return &CompressionStats{
		TotalRequests:      1000,
		CompressedRequests: 800,
		CompressionRatio:   0.65,
		BytesSaved:         1024000,
	}
}

// 缓存中间件
type CacheMiddleware struct {
	cache map[string]*CacheEntry
	mutex sync.RWMutex
	ttl   int64 // 缓存TTL（秒）
}

type CacheEntry struct {
	Data      []byte            `json:"data"`
	Headers   map[string]string `json:"headers"`
	Timestamp int64             `json:"timestamp"`
	TTL       int64             `json:"ttl"`
}

var (
	cacheMiddleware *CacheMiddleware
	cacheOnce       sync.Once
)

// GetCacheMiddleware 获取缓存中间件实例
func GetCacheMiddleware() *CacheMiddleware {
	cacheOnce.Do(func() {
		cacheMiddleware = &CacheMiddleware{
			cache: make(map[string]*CacheEntry),
			ttl:   300, // 默认5分钟TTL
		}

		// 启动缓存清理协程
		go cacheMiddleware.cleanupExpiredEntries()
	})
	return cacheMiddleware
}

// Handler 缓存中间件处理函数
func (cm *CacheMiddleware) Handler(ctx *context.Context) {
	// 只缓存GET请求
	if ctx.Input.Method() != "GET" {
		return
	}

	cacheKey := cm.generateCacheKey(ctx)

	// 尝试从缓存获取
	if entry := cm.getFromCache(cacheKey); entry != nil {
		// 设置响应头
		for key, value := range entry.Headers {
			ctx.Output.Header(key, value)
		}

		// 设置缓存命中头
		ctx.Output.Header("X-Cache", "HIT")

		// 写入缓存的响应
		ctx.ResponseWriter.Write(entry.Data)

		// 记录缓存命中指标
		perfMonitor := comm.GetPerformanceMonitor()
		if perfMonitor != nil {
			perfMonitor.RecordMetric("cache_hits", 1, "count", map[string]string{
				"endpoint": ctx.Input.URL(),
			})
		}

		return
	}

	// 缓存未命中，设置响应拦截
	ctx.Output.Header("X-Cache", "MISS")

	// 这里需要拦截响应并缓存，实际实现会更复杂
	// 暂时只记录缓存未命中
	perfMonitor := comm.GetPerformanceMonitor()
	if perfMonitor != nil {
		perfMonitor.RecordMetric("cache_misses", 1, "count", map[string]string{
			"endpoint": ctx.Input.URL(),
		})
	}
}

// generateCacheKey 生成缓存键
func (cm *CacheMiddleware) generateCacheKey(ctx *context.Context) string {
	return ctx.Input.Method() + ":" + ctx.Input.URL()
}

// getFromCache 从缓存获取数据
func (cm *CacheMiddleware) getFromCache(key string) *CacheEntry {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	entry, exists := cm.cache[key]
	if !exists {
		return nil
	}

	// 检查是否过期
	now := time.Now().Unix()
	if now > entry.Timestamp+entry.TTL {
		return nil
	}

	return entry
}

// setToCache 设置缓存数据
func (cm *CacheMiddleware) setToCache(key string, data []byte, headers map[string]string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.cache[key] = &CacheEntry{
		Data:      data,
		Headers:   headers,
		Timestamp: time.Now().Unix(),
		TTL:       cm.ttl,
	}
}

// SetToCache 公开的设置缓存方法
func (cm *CacheMiddleware) SetToCache(key string, data []byte, headers map[string]string) {
	cm.setToCache(key, data, headers)
}

// cleanupExpiredEntries 清理过期缓存条目
func (cm *CacheMiddleware) cleanupExpiredEntries() {
	ticker := time.NewTicker(time.Minute * 5) // 每5分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		cm.mutex.Lock()
		now := time.Now().Unix()

		for key, entry := range cm.cache {
			if now > entry.Timestamp+entry.TTL {
				delete(cm.cache, key)
			}
		}
		cm.mutex.Unlock()
	}
}

// CacheFilter Beego缓存过滤器函数
func CacheFilter(ctx *context.Context) {
	GetCacheMiddleware().Handler(ctx)
}
