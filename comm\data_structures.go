package comm

import (
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"
)

// FastMap 高性能Map实现，使用分片减少锁竞争
type FastMap[K comparable, V any] struct {
	shards    []*mapShard[K, V]
	shardMask uint32
}

type mapShard[K comparable, V any] struct {
	mu   sync.RWMutex
	data map[K]V
}

// NewFastMap 创建新的FastMap
func NewFastMap[K comparable, V any](shardCount int) *FastMap[K, V] {
	// 确保分片数量是2的幂
	if shardCount <= 0 {
		shardCount = 16
	}

	// 找到最接近的2的幂
	actualShardCount := 1
	for actualShardCount < shardCount {
		actualShardCount <<= 1
	}

	fm := &FastMap[K, V]{
		shards:    make([]*mapShard[K, V], actualShardCount),
		shardMask: uint32(actualShardCount - 1),
	}

	for i := 0; i < actualShardCount; i++ {
		fm.shards[i] = &mapShard[K, V]{
			data: make(map[K]V),
		}
	}

	return fm
}

// getShard 获取键对应的分片
func (fm *FastMap[K, V]) getShard(key K) *mapShard[K, V] {
	hash := fm.hash(key)
	return fm.shards[hash&fm.shardMask]
}

// hash 简单的哈希函数
func (fm *FastMap[K, V]) hash(key K) uint32 {
	// 这里使用简单的字符串哈希，实际项目中可以使用更好的哈希函数
	str := fmt.Sprintf("%v", key)
	var hash uint32
	for _, c := range str {
		hash = hash*31 + uint32(c)
	}
	return hash
}

// Set 设置键值对
func (fm *FastMap[K, V]) Set(key K, value V) {
	shard := fm.getShard(key)
	shard.mu.Lock()
	defer shard.mu.Unlock()
	shard.data[key] = value
}

// Get 获取值
func (fm *FastMap[K, V]) Get(key K) (V, bool) {
	shard := fm.getShard(key)
	shard.mu.RLock()
	defer shard.mu.RUnlock()
	value, exists := shard.data[key]
	return value, exists
}

// Delete 删除键
func (fm *FastMap[K, V]) Delete(key K) {
	shard := fm.getShard(key)
	shard.mu.Lock()
	defer shard.mu.Unlock()
	delete(shard.data, key)
}

// Len 获取总长度
func (fm *FastMap[K, V]) Len() int {
	total := 0
	for _, shard := range fm.shards {
		shard.mu.RLock()
		total += len(shard.data)
		shard.mu.RUnlock()
	}
	return total
}

// Keys 获取所有键
func (fm *FastMap[K, V]) Keys() []K {
	var keys []K
	for _, shard := range fm.shards {
		shard.mu.RLock()
		for k := range shard.data {
			keys = append(keys, k)
		}
		shard.mu.RUnlock()
	}
	return keys
}

// CircularBuffer 环形缓冲区，用于高效的FIFO操作
type CircularBuffer[T any] struct {
	buffer []T
	head   int
	tail   int
	size   int
	cap    int
	mu     sync.RWMutex
}

// NewCircularBuffer 创建新的环形缓冲区
func NewCircularBuffer[T any](capacity int) *CircularBuffer[T] {
	return &CircularBuffer[T]{
		buffer: make([]T, capacity),
		cap:    capacity,
	}
}

// Push 添加元素到缓冲区
func (cb *CircularBuffer[T]) Push(item T) bool {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	if cb.size == cb.cap {
		return false // 缓冲区已满
	}

	cb.buffer[cb.tail] = item
	cb.tail = (cb.tail + 1) % cb.cap
	cb.size++
	return true
}

// Pop 从缓冲区取出元素
func (cb *CircularBuffer[T]) Pop() (T, bool) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	var zero T
	if cb.size == 0 {
		return zero, false // 缓冲区为空
	}

	item := cb.buffer[cb.head]
	cb.buffer[cb.head] = zero // 清零避免内存泄漏
	cb.head = (cb.head + 1) % cb.cap
	cb.size--
	return item, true
}

// Size 获取当前大小
func (cb *CircularBuffer[T]) Size() int {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.size
}

// IsFull 检查是否已满
func (cb *CircularBuffer[T]) IsFull() bool {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.size == cb.cap
}

// IsEmpty 检查是否为空
func (cb *CircularBuffer[T]) IsEmpty() bool {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.size == 0
}

// LRUCache LRU缓存实现
type LRUCache[K comparable, V any] struct {
	capacity int
	cache    map[K]*lruNode[K, V]
	head     *lruNode[K, V]
	tail     *lruNode[K, V]
	mu       sync.RWMutex
}

type lruNode[K comparable, V any] struct {
	key   K
	value V
	prev  *lruNode[K, V]
	next  *lruNode[K, V]
}

// NewLRUCache 创建新的LRU缓存
func NewLRUCache[K comparable, V any](capacity int) *LRUCache[K, V] {
	lru := &LRUCache[K, V]{
		capacity: capacity,
		cache:    make(map[K]*lruNode[K, V]),
	}

	// 创建哨兵节点
	lru.head = &lruNode[K, V]{}
	lru.tail = &lruNode[K, V]{}
	lru.head.next = lru.tail
	lru.tail.prev = lru.head

	return lru
}

// Get 获取缓存值
func (lru *LRUCache[K, V]) Get(key K) (V, bool) {
	lru.mu.Lock()
	defer lru.mu.Unlock()

	var zero V
	node, exists := lru.cache[key]
	if !exists {
		return zero, false
	}

	// 移动到头部
	lru.moveToHead(node)
	return node.value, true
}

// Put 设置缓存值
func (lru *LRUCache[K, V]) Put(key K, value V) {
	lru.mu.Lock()
	defer lru.mu.Unlock()

	if node, exists := lru.cache[key]; exists {
		// 更新现有节点
		node.value = value
		lru.moveToHead(node)
		return
	}

	// 创建新节点
	newNode := &lruNode[K, V]{
		key:   key,
		value: value,
	}

	lru.cache[key] = newNode
	lru.addToHead(newNode)

	// 检查容量
	if len(lru.cache) > lru.capacity {
		tail := lru.removeTail()
		delete(lru.cache, tail.key)
	}
}

// moveToHead 移动节点到头部
func (lru *LRUCache[K, V]) moveToHead(node *lruNode[K, V]) {
	lru.removeNode(node)
	lru.addToHead(node)
}

// addToHead 添加节点到头部
func (lru *LRUCache[K, V]) addToHead(node *lruNode[K, V]) {
	node.prev = lru.head
	node.next = lru.head.next
	lru.head.next.prev = node
	lru.head.next = node
}

// removeNode 移除节点
func (lru *LRUCache[K, V]) removeNode(node *lruNode[K, V]) {
	node.prev.next = node.next
	node.next.prev = node.prev
}

// removeTail 移除尾部节点
func (lru *LRUCache[K, V]) removeTail() *lruNode[K, V] {
	lastNode := lru.tail.prev
	lru.removeNode(lastNode)
	return lastNode
}

// Size 获取缓存大小
func (lru *LRUCache[K, V]) Size() int {
	lru.mu.RLock()
	defer lru.mu.RUnlock()
	return len(lru.cache)
}

// TimedCache 带过期时间的缓存
type TimedCache[K comparable, V any] struct {
	cache map[K]*timedCacheItem[V]
	mu    sync.RWMutex
}

type timedCacheItem[V any] struct {
	value     V
	expiresAt time.Time
}

// NewTimedCache 创建新的定时缓存
func NewTimedCache[K comparable, V any]() *TimedCache[K, V] {
	tc := &TimedCache[K, V]{
		cache: make(map[K]*timedCacheItem[V]),
	}

	// 启动清理协程
	go tc.cleanup()

	return tc
}

// Set 设置缓存值
func (tc *TimedCache[K, V]) Set(key K, value V, ttl time.Duration) {
	tc.mu.Lock()
	defer tc.mu.Unlock()

	tc.cache[key] = &timedCacheItem[V]{
		value:     value,
		expiresAt: time.Now().Add(ttl),
	}
}

// Get 获取缓存值
func (tc *TimedCache[K, V]) Get(key K) (V, bool) {
	tc.mu.RLock()
	defer tc.mu.RUnlock()

	var zero V
	item, exists := tc.cache[key]
	if !exists {
		return zero, false
	}

	if time.Now().After(item.expiresAt) {
		return zero, false
	}

	return item.value, true
}

// Delete 删除缓存值
func (tc *TimedCache[K, V]) Delete(key K) {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	delete(tc.cache, key)
}

// cleanup 清理过期项
func (tc *TimedCache[K, V]) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		tc.mu.Lock()
		now := time.Now()
		for key, item := range tc.cache {
			if now.After(item.expiresAt) {
				delete(tc.cache, key)
			}
		}
		tc.mu.Unlock()
	}
}

// SortedSet 有序集合实现
type SortedSet[T any] struct {
	items []sortedSetItem[T]
	mu    sync.RWMutex
}

type sortedSetItem[T any] struct {
	value T
	score float64
}

// NewSortedSet 创建新的有序集合
func NewSortedSet[T any]() *SortedSet[T] {
	return &SortedSet[T]{
		items: make([]sortedSetItem[T], 0),
	}
}

// Add 添加元素
func (ss *SortedSet[T]) Add(value T, score float64) {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	item := sortedSetItem[T]{value: value, score: score}

	// 使用二分查找找到插入位置
	index := sort.Search(len(ss.items), func(i int) bool {
		return ss.items[i].score >= score
	})

	// 插入元素
	ss.items = append(ss.items, sortedSetItem[T]{})
	copy(ss.items[index+1:], ss.items[index:])
	ss.items[index] = item
}

// GetByRank 按排名获取元素
func (ss *SortedSet[T]) GetByRank(rank int) (T, bool) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	var zero T
	if rank < 0 || rank >= len(ss.items) {
		return zero, false
	}

	return ss.items[rank].value, true
}

// GetRange 获取范围内的元素
func (ss *SortedSet[T]) GetRange(start, end int) []T {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	if start < 0 {
		start = 0
	}
	if end >= len(ss.items) {
		end = len(ss.items) - 1
	}
	if start > end {
		return nil
	}

	result := make([]T, end-start+1)
	for i := start; i <= end; i++ {
		result[i-start] = ss.items[i].value
	}

	return result
}

// Size 获取大小
func (ss *SortedSet[T]) Size() int {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	return len(ss.items)
}

// JSONOptimizer JSON优化器，减少序列化开销
type JSONOptimizer struct {
	pool sync.Pool
}

// NewJSONOptimizer 创建JSON优化器
func NewJSONOptimizer() *JSONOptimizer {
	return &JSONOptimizer{
		pool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 0, 1024)
			},
		},
	}
}

// Marshal 优化的JSON序列化
func (jo *JSONOptimizer) Marshal(v interface{}) ([]byte, error) {
	buf := jo.pool.Get().([]byte)
	defer jo.pool.Put(buf[:0])

	// 使用更高效的JSON编码器
	data, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}

	// 复制数据到新的切片
	result := make([]byte, len(data))
	copy(result, data)

	return result, nil
}

// Unmarshal 优化的JSON反序列化
func (jo *JSONOptimizer) Unmarshal(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

// 全局实例
var (
	globalJSONOptimizer *JSONOptimizer
	jsonOptimizerOnce   sync.Once
)

// GetJSONOptimizer 获取全局JSON优化器
func GetJSONOptimizer() *JSONOptimizer {
	jsonOptimizerOnce.Do(func() {
		globalJSONOptimizer = NewJSONOptimizer()
	})
	return globalJSONOptimizer
}
