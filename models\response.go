package models

import (
	"time"
)

// StandardResponse 标准响应结构 - 统一所有API响应格式
type StandardResponse struct {
	Code      int64       `json:"code"`                 // 响应码：0表示成功，负数表示错误
	Success   bool        `json:"success"`              // 操作是否成功
	Message   string      `json:"message"`              // 响应消息
	Data      interface{} `json:"data,omitempty"`       // 响应数据
	Timestamp int64       `json:"timestamp"`            // 时间戳
	RequestID string      `json:"request_id,omitempty"` // 请求ID，用于追踪

	// 兼容旧版本的字段，逐步废弃
	Data62   string `json:"data62,omitempty"`    // 62数据，用于登录相关接口
	DeviceID string `json:"device_id,omitempty"` // 设备ID，用于登录相关接口
	Debug    string `json:"debug,omitempty"`     // 调试信息，仅开发模式显示
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}, message string) *StandardResponse {
	if message == "" {
		message = "操作成功"
	}
	return &StandardResponse{
		Code:      0,
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int64, message string) *StandardResponse {
	return &StandardResponse{
		Code:      code,
		Success:   false,
		Message:   message,
		Timestamp: time.Now().Unix(),
	}
}

// PaginatedResponse 分页响应结构
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// NewPaginatedResponse 创建分页响应
func NewPaginatedResponse(items interface{}, total int64, page, pageSize int) *PaginatedResponse {
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	return &PaginatedResponse{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
}

// APIError API错误结构
type APIError struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	return e.Message
}

// NewAPIError 创建API错误
func NewAPIError(code int64, message, details string) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewLegacyResponse 创建兼容旧版本的响应（用于逐步迁移）
func NewLegacyResponse(code int64, success bool, message string, data interface{}) *StandardResponse {
	return &StandardResponse{
		Code:      code,
		Success:   success,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewLegacyResponseWithData62 创建包含Data62的响应（用于登录相关接口）
func NewLegacyResponseWithData62(code int64, success bool, message string, data interface{}, data62, deviceID string) *StandardResponse {
	return &StandardResponse{
		Code:      code,
		Success:   success,
		Message:   message,
		Data:      data,
		Data62:    data62,
		DeviceID:  deviceID,
		Timestamp: time.Now().Unix(),
	}
}

// SetRequestID 设置请求ID
func (r *StandardResponse) SetRequestID(requestID string) *StandardResponse {
	r.RequestID = requestID
	return r
}

// SetDebug 设置调试信息（仅开发模式）
func (r *StandardResponse) SetDebug(debug string) *StandardResponse {
	r.Debug = debug
	return r
}

// 预定义的错误码 - 按功能模块分类
const (
	// 通用错误码 (-1000 ~ -1099)
	ErrCodeInvalidParam    = -1001 // 参数错误
	ErrCodeUnauthorized    = -1002 // 未授权
	ErrCodeNotFound        = -1003 // 资源不存在
	ErrCodeRateLimit       = -1004 // 请求频率限制
	ErrCodeSystemError     = -1005 // 系统错误
	ErrCodeOperationFailed = -1006 // 操作失败
	ErrCodeNetworkError    = -1007 // 网络错误
	ErrCodeTimeout         = -1008 // 请求超时
	ErrCodeServiceBusy     = -1009 // 服务繁忙

	// 登录相关错误码 (-1100 ~ -1199)
	ErrCodeLoginExpired   = -1101 // 登录过期
	ErrCodeLoginFailed    = -1102 // 登录失败
	ErrCodeQRCodeExpired  = -1103 // 二维码过期
	ErrCodeDeviceNotFound = -1104 // 设备不存在
	ErrCodeData62Invalid  = -1105 // 62数据无效

	// 微信API相关错误码 (-1200 ~ -1299)
	ErrCodeWXAPIError      = -1201 // 微信API错误
	ErrCodeWXConnectFailed = -1202 // 微信连接失败
	ErrCodeWXProtocolError = -1203 // 微信协议错误
	ErrCodeWXAccountError  = -1204 // 微信账号错误

	// 消息相关错误码 (-1300 ~ -1399)
	ErrCodeMessageSendFailed  = -1301 // 消息发送失败
	ErrCodeMessageTooLong     = -1302 // 消息过长
	ErrCodeMessageFormatError = -1303 // 消息格式错误

	// 好友相关错误码 (-1400 ~ -1499)
	ErrCodeFriendNotFound     = -1401 // 好友不存在
	ErrCodeFriendAddFailed    = -1402 // 添加好友失败
	ErrCodeFriendDeleteFailed = -1403 // 删除好友失败
)

// 预定义的错误消息
var ErrorMessages = map[int64]string{
	// 通用错误消息
	ErrCodeInvalidParam:    "参数错误",
	ErrCodeUnauthorized:    "未授权访问",
	ErrCodeNotFound:        "资源不存在",
	ErrCodeRateLimit:       "请求过于频繁",
	ErrCodeSystemError:     "系统错误",
	ErrCodeOperationFailed: "操作失败",
	ErrCodeNetworkError:    "网络连接错误",
	ErrCodeTimeout:         "请求超时",
	ErrCodeServiceBusy:     "服务繁忙，请稍后重试",

	// 登录相关错误消息
	ErrCodeLoginExpired:   "登录已过期，请重新登录",
	ErrCodeLoginFailed:    "登录失败",
	ErrCodeQRCodeExpired:  "二维码已过期，请重新获取",
	ErrCodeDeviceNotFound: "设备不存在",
	ErrCodeData62Invalid:  "62数据无效",

	// 微信API相关错误消息
	ErrCodeWXAPIError:      "微信API调用失败",
	ErrCodeWXConnectFailed: "微信连接失败",
	ErrCodeWXProtocolError: "微信协议错误",
	ErrCodeWXAccountError:  "微信账号异常",

	// 消息相关错误消息
	ErrCodeMessageSendFailed:  "消息发送失败",
	ErrCodeMessageTooLong:     "消息内容过长",
	ErrCodeMessageFormatError: "消息格式错误",

	// 好友相关错误消息
	ErrCodeFriendNotFound:     "好友不存在",
	ErrCodeFriendAddFailed:    "添加好友失败",
	ErrCodeFriendDeleteFailed: "删除好友失败",
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(code int64) string {
	if msg, exists := ErrorMessages[code]; exists {
		return msg
	}
	return "未知错误"
}

// NewErrorResponseWithCode 根据错误码创建错误响应
func NewErrorResponseWithCode(code int64) *StandardResponse {
	message := GetErrorMessage(code)
	return NewErrorResponse(code, message)
}

// IsSystemError 判断是否为系统错误
func IsSystemError(code int64) bool {
	return code >= -1099 && code <= -1000
}

// IsLoginError 判断是否为登录相关错误
func IsLoginError(code int64) bool {
	return code >= -1199 && code <= -1100
}

// IsWXAPIError 判断是否为微信API错误
func IsWXAPIError(code int64) bool {
	return code >= -1299 && code <= -1200
}
