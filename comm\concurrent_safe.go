package comm

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	log "github.com/sirupsen/logrus"
)

// SafeMap 并发安全的Map
type SafeMap[K comparable, V any] struct {
	mu   sync.RWMutex
	data map[K]V
}

// NewSafeMap 创建新的并发安全Map
func NewSafeMap[K comparable, V any]() *SafeMap[K, V] {
	return &SafeMap[K, V]{
		data: make(map[K]V),
	}
}

// Set 设置键值对
func (sm *SafeMap[K, V]) Set(key K, value V) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.data[key] = value
}

// Get 获取值
func (sm *SafeMap[K, V]) Get(key K) (V, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	value, exists := sm.data[key]
	return value, exists
}

// Delete 删除键
func (sm *SafeMap[K, V]) Delete(key K) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	delete(sm.data, key)
}

// Len 获取长度
func (sm *SafeMap[K, V]) Len() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return len(sm.data)
}

// Range 遍历Map
func (sm *SafeMap[K, V]) Range(fn func(K, V) bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	for k, v := range sm.data {
		if !fn(k, v) {
			break
		}
	}
}

// Keys 获取所有键
func (sm *SafeMap[K, V]) Keys() []K {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	keys := make([]K, 0, len(sm.data))
	for k := range sm.data {
		keys = append(keys, k)
	}
	return keys
}

// Values 获取所有值
func (sm *SafeMap[K, V]) Values() []V {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	values := make([]V, 0, len(sm.data))
	for _, v := range sm.data {
		values = append(values, v)
	}
	return values
}

// SafeSlice 并发安全的切片
type SafeSlice[T any] struct {
	mu   sync.RWMutex
	data []T
}

// NewSafeSlice 创建新的并发安全切片
func NewSafeSlice[T any]() *SafeSlice[T] {
	return &SafeSlice[T]{
		data: make([]T, 0),
	}
}

// Append 追加元素
func (ss *SafeSlice[T]) Append(items ...T) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	ss.data = append(ss.data, items...)
}

// Get 获取指定索引的元素
func (ss *SafeSlice[T]) Get(index int) (T, bool) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	var zero T
	if index < 0 || index >= len(ss.data) {
		return zero, false
	}
	return ss.data[index], true
}

// Set 设置指定索引的元素
func (ss *SafeSlice[T]) Set(index int, value T) bool {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	if index < 0 || index >= len(ss.data) {
		return false
	}
	ss.data[index] = value
	return true
}

// Len 获取长度
func (ss *SafeSlice[T]) Len() int {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	return len(ss.data)
}

// ToSlice 转换为普通切片（返回副本）
func (ss *SafeSlice[T]) ToSlice() []T {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	result := make([]T, len(ss.data))
	copy(result, ss.data)
	return result
}

// Clear 清空切片
func (ss *SafeSlice[T]) Clear() {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	ss.data = ss.data[:0]
}

// WorkerPool 工作池
type WorkerPool struct {
	workers    int
	jobQueue   chan Job
	workerPool chan chan Job
	quit       chan bool
	wg         sync.WaitGroup
	running    int32
}

// Job 工作任务
type Job struct {
	ID       string
	Function func() error
	Context  context.Context
	Callback func(error)
}

// NewWorkerPool 创建新的工作池
func NewWorkerPool(workers int, queueSize int) *WorkerPool {
	return &WorkerPool{
		workers:    workers,
		jobQueue:   make(chan Job, queueSize),
		workerPool: make(chan chan Job, workers),
		quit:       make(chan bool),
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start() {
	if !atomic.CompareAndSwapInt32(&wp.running, 0, 1) {
		return // 已经在运行
	}

	// 启动工作者
	for i := 0; i < wp.workers; i++ {
		worker := NewWorker(wp.workerPool, wp.quit)
		worker.Start()
		wp.wg.Add(1)
	}

	// 启动调度器
	go wp.dispatch()

	log.Infof("工作池已启动，工作者数量: %d", wp.workers)
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	if !atomic.CompareAndSwapInt32(&wp.running, 1, 0) {
		return // 已经停止
	}

	close(wp.quit)
	wp.wg.Wait()
	close(wp.jobQueue)

	log.Info("工作池已停止")
}

// Submit 提交任务
func (wp *WorkerPool) Submit(job Job) error {
	if atomic.LoadInt32(&wp.running) == 0 {
		return fmt.Errorf("工作池未运行")
	}

	select {
	case wp.jobQueue <- job:
		return nil
	case <-time.After(time.Second * 5):
		return fmt.Errorf("任务提交超时")
	}
}

// dispatch 任务调度
func (wp *WorkerPool) dispatch() {
	for {
		select {
		case job := <-wp.jobQueue:
			// 获取可用的工作者
			select {
			case jobChannel := <-wp.workerPool:
				jobChannel <- job
			case <-wp.quit:
				return
			}
		case <-wp.quit:
			return
		}
	}
}

// Worker 工作者
type Worker struct {
	workerPool chan chan Job
	jobChannel chan Job
	quit       chan bool
}

// NewWorker 创建新的工作者
func NewWorker(workerPool chan chan Job, quit chan bool) *Worker {
	return &Worker{
		workerPool: workerPool,
		jobChannel: make(chan Job),
		quit:       quit,
	}
}

// Start 启动工作者
func (w *Worker) Start() {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("工作者发生panic: %v", r)
			}
		}()

		for {
			// 将工作者注册到工作池
			w.workerPool <- w.jobChannel

			select {
			case job := <-w.jobChannel:
				// 执行任务
				w.executeJob(job)
			case <-w.quit:
				return
			}
		}
	}()
}

// executeJob 执行任务
func (w *Worker) executeJob(job Job) {
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("任务执行panic: %v", r)
			if job.Callback != nil {
				job.Callback(err)
			}
			log.Errorf("任务 %s 执行失败: %v", job.ID, err)
		}
	}()

	// 检查上下文是否已取消
	if job.Context != nil {
		select {
		case <-job.Context.Done():
			if job.Callback != nil {
				job.Callback(job.Context.Err())
			}
			return
		default:
		}
	}

	// 执行任务
	err := job.Function()
	if job.Callback != nil {
		job.Callback(err)
	}

	if err != nil {
		log.Errorf("任务 %s 执行失败: %v", job.ID, err)
	} else {
		log.Debugf("任务 %s 执行成功", job.ID)
	}
}

// RateLimiter 并发安全的限流器
type RateLimiter struct {
	rate     int64
	capacity int64
	tokens   int64
	lastTime int64
	mu       sync.Mutex
}

// NewRateLimiter 创建新的限流器
func NewRateLimiter(rate, capacity int64) *RateLimiter {
	return &RateLimiter{
		rate:     rate,
		capacity: capacity,
		tokens:   capacity,
		lastTime: time.Now().UnixNano(),
	}
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().UnixNano()
	elapsed := now - rl.lastTime
	rl.lastTime = now

	// 添加令牌
	tokensToAdd := elapsed * rl.rate / int64(time.Second)
	rl.tokens += tokensToAdd
	if rl.tokens > rl.capacity {
		rl.tokens = rl.capacity
	}

	// 检查是否有可用令牌
	if rl.tokens > 0 {
		rl.tokens--
		return true
	}

	return false
}

// GetTokens 获取当前令牌数
func (rl *RateLimiter) GetTokens() int64 {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	return rl.tokens
}

// 全局工作池实例
var (
	globalWorkerPool *WorkerPool
	workerPoolOnce   sync.Once
)

// GetWorkerPool 获取全局工作池
func GetWorkerPool() *WorkerPool {
	workerPoolOnce.Do(func() {
		workers := 10    // 默认10个工作者
		queueSize := 100 // 默认队列大小100

		globalWorkerPool = NewWorkerPool(workers, queueSize)
		globalWorkerPool.Start()
	})
	return globalWorkerPool
}

// SubmitJob 提交任务到全局工作池
func SubmitJob(id string, fn func() error, ctx context.Context, callback func(error)) error {
	job := Job{
		ID:       id,
		Function: fn,
		Context:  ctx,
		Callback: callback,
	}
	return GetWorkerPool().Submit(job)
}
