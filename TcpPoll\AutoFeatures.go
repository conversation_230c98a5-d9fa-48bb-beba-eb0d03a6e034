package TcpPoll

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// ==================== 自动功能状态管理 ====================

// 自动红包领取状态管理
var (
	autoRedPacketStatus = make(map[string]bool)
	autoRedPacketMutex  = &sync.RWMutex{}
)

// 自动收款确认状态管理
var (
	autoCollectMoneyStatus = make(map[string]bool)
	autoCollectMoneyMutex  = &sync.RWMutex{}
)

// 自动通过好友请求状态管理
var (
	autoAcceptFriendStatus = make(map[string]bool)
	autoAcceptFriendMutex  = &sync.RWMutex{}
)



// ==================== 自动红包领取功能 ====================

// ProcessRedPacketMessage 处理红包消息并自动领取
func ProcessRedPacketMessage(wxid string, msg map[string]interface{}) {
	if wxid == "" || msg == nil {
		log.Warn("ProcessRedPacketMessage: 参数无效")
		return
	}

	// 检查是否开启了自动红包领取
	if !GetAutoRedPacketStatus(wxid) {
		return
	}

	// 使用context控制超时，避免长时间阻塞
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	done := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("ProcessRedPacketMessage panic: wxid=%s, error=%v", wxid, r)
			}
			select {
			case done <- true:
			default:
			}
		}()

		processRedPacketMessageInternal(wxid, msg)
	}()

	// 等待完成或超时
	select {
	case <-done:
		// 正常完成
	case <-ctx.Done():
		log.Warnf("ProcessRedPacketMessage 超时: wxid=%s", wxid)
	}
}

// ProcessRedPacketMessageWithStatus 处理红包消息并自动领取（带预设状态）
func ProcessRedPacketMessageWithStatus(wxid string, msg map[string]interface{}, enabled bool) {
	if !enabled {
		return
	}
	processRedPacketMessageInternal(wxid, msg)
}

// processRedPacketMessageInternal 内部红包处理逻辑
func processRedPacketMessageInternal(wxid string, msg map[string]interface{}) {

	// 检查消息类型 - 支持多种类型匹配
	msgType := msg["msgType"]
	var isMsgType49 bool
	switch v := msgType.(type) {
	case int32:
		isMsgType49 = v == 49
	case int:
		isMsgType49 = v == 49
	case uint32:
		isMsgType49 = v == 49
	case float64:
		isMsgType49 = int(v) == 49
	default:
		return
	}

	if !isMsgType49 {
		return
	}

	// 检查内容类型
	contentType, ok := msg["contentType"].(string)
	if !ok || contentType != "link" {
		return
	}

	// 检查额外数据
	extraData, ok := msg["extraData"].(map[string]interface{})
	if !ok {
		return
	}

	_, ok = extraData["xmlData"].(map[string]interface{})
	if !ok {
		return
	}

	// 检查原始内容中是否包含红包关键词
	originalContent, ok := msg["originalContent"].(string)
	if !ok {
		return
	}

	// 检查是否包含红包相关关键词
	isRedPacket := strings.Contains(originalContent, "微信红包") ||
		strings.Contains(originalContent, "wxhb") ||
		strings.Contains(originalContent, "receivehongbao") ||
		strings.Contains(originalContent, "type>2001</type")

	if !isRedPacket {
		return
	}

	fmt.Printf("[AUTO_REDPACKET] 检测到红包消息，开始自动领取: wxid=%s\n", wxid)

	// 执行自动红包领取
	err := autoReceiveRedPacket(wxid, originalContent)
	if err != nil {
		fmt.Printf("[AUTO_REDPACKET] 自动领取红包失败: %v\n", err)
	} else {
		fmt.Printf("[AUTO_REDPACKET] 自动领取红包成功: wxid=%s\n", wxid)
	}
}

// autoReceiveRedPacket 自动领取红包
func autoReceiveRedPacket(wxid, xmlContent string) error {
	// 步骤1: 获取加密信息
	encryptInfo, err := getEncryptInfo(wxid)
	if err != nil {
		return fmt.Errorf("获取加密信息失败: %v", err)
	}

	// 步骤2: 打开红包 (Receivewxhb)
	receiveResult, err := receiveRedPacket(wxid, xmlContent, encryptInfo)
	if err != nil {
		return fmt.Errorf("打开红包失败: %v", err)
	}

	// 步骤3: 拆开红包 (Openwxhb) - 如果需要的话
	var openResult map[string]interface{}
	if receiveResult != nil {
		openResult, err = openRedPacketWithResult(wxid, xmlContent, encryptInfo, receiveResult)
		if err != nil {
			fmt.Printf("[AUTO_REDPACKET] 拆开红包失败: %v\n", err)
			// 不返回错误，因为打开红包已经成功了
		} else {
			// 解析红包结果并发送WebSocket通知
			redpacketInfo := parseRedPacketResult(openResult)
			sendRedPacketNotification(wxid, redpacketInfo)
		}
	}

	return nil
}

// getEncryptInfo 获取加密信息
func getEncryptInfo(wxid string) (map[string]interface{}, error) {
	requestData := map[string]interface{}{
		"Wxid": wxid,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("构造请求数据失败: %v", err)
	}

	resp, err := http.Post("http://localhost:8059/api/TenPay/GetEncryptInfo", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if success, ok := response["Success"].(bool); !ok || !success {
		message := "未知错误"
		if msg, ok := response["Message"].(string); ok {
			message = msg
		}
		return nil, fmt.Errorf("获取加密信息失败: %s", message)
	}

	return response, nil
}

// receiveRedPacket 打开红包
func receiveRedPacket(wxid, xmlContent string, encryptInfo map[string]interface{}) (map[string]interface{}, error) {
	// 构造Receivewxhb请求参数
	requestData := map[string]interface{}{
		"Wxid":             wxid,
		"Xml":              xmlContent,
		"Encrypt_key":      getStringFromMap(encryptInfo, "Data.Encrypt_key"),
		"Encrypt_userinfo": getStringFromMap(encryptInfo, "Data.Encrypt_userinfo"),
		"InWay":            "1", // 1表示个人红包
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("构造请求数据失败: %v", err)
	}

	resp, err := http.Post("http://localhost:8059/api/TenPay/Receivewxhb", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return response, nil
}

// openRedPacketWithResult 拆开红包并返回结果
func openRedPacketWithResult(wxid, xmlContent string, encryptInfo map[string]interface{}, receiveResult map[string]interface{}) (map[string]interface{}, error) {
	// 从receiveResult中提取必要信息
	sendUserName := extractSendUserNameFromXML(xmlContent)
	timingIdentifier := extractTimingIdentifierFromResponse(receiveResult)

	// 从encryptInfo中提取加密信息
	encryptKey := getStringFromMap(encryptInfo, "Data.Encrypt_key")
	encryptUserinfo := getStringFromMap(encryptInfo, "Data.Encrypt_userinfo")

	requestData := map[string]interface{}{
		"Wxid":             wxid,
		"Xml":              xmlContent,
		"SendUserName":     sendUserName,
		"TimingIdentifier": timingIdentifier,
		"Encrypt_key":      encryptKey,
		"Encrypt_userinfo": encryptUserinfo,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("构造请求数据失败: %v", err)
	}

	resp, err := http.Post("http://localhost:8059/api/TenPay/Openwxhb", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return response, nil
}

// extractSendUserNameFromXML 从XML中提取发送者用户名
func extractSendUserNameFromXML(xmlContent string) string {
	// 查找 <fromusername><![CDATA[...]]></fromusername>
	if start := strings.Index(xmlContent, "<fromusername><![CDATA["); start != -1 {
		start += len("<fromusername><![CDATA[")
		if end := strings.Index(xmlContent[start:], "]]></fromusername>"); end != -1 {
			return xmlContent[start : start+end]
		}
	}

	// 备选方案：查找 <fromusername>...</fromusername>
	if start := strings.Index(xmlContent, "<fromusername>"); start != -1 {
		start += len("<fromusername>")
		if end := strings.Index(xmlContent[start:], "</fromusername>"); end != -1 {
			content := xmlContent[start : start+end]
			// 移除CDATA标签
			content = strings.ReplaceAll(content, "<![CDATA[", "")
			content = strings.ReplaceAll(content, "]]>", "")
			return strings.TrimSpace(content)
		}
	}

	return ""
}

// extractTimingIdentifierFromResponse 从Receivewxhb响应中提取TimingIdentifier
func extractTimingIdentifierFromResponse(response map[string]interface{}) string {
	// 尝试从Data.retText.buffer中解析
	if data, ok := response["Data"].(map[string]interface{}); ok {
		if retText, ok := data["retText"].(map[string]interface{}); ok {
			if buffer, ok := retText["buffer"].(string); ok {
				// Base64解码
				decoded, err := base64DecodeString(buffer)
				if err == nil {
					// 解析JSON
					var jsonData map[string]interface{}
					if json.Unmarshal([]byte(decoded), &jsonData) == nil {
						if timingId, ok := jsonData["timingIdentifier"].(string); ok {
							return timingId
						}
					}
				}
			}
		}
	}
	return ""
}

// ==================== 自动通过好友请求功能 ====================

// ProcessFriendRequestMessage 处理好友请求消息并自动通过
func ProcessFriendRequestMessage(wxid string, msg map[string]interface{}) {
	// 使用超时控制，避免长时间阻塞
	done := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("ProcessFriendRequestMessage panic: %v", r)
			}
			done <- true
		}()

		// 检查是否开启了自动通过好友请求
		if !GetAutoAcceptFriendStatus(wxid) {
			return
		}

		processFriendRequestMessageInternal(wxid, msg)
	}()

	// 设置5秒超时
	select {
	case <-done:
		// 正常完成
	case <-time.After(5 * time.Second):
		log.Warnf("ProcessFriendRequestMessage 超时: wxid=%s", wxid)
	}
}

// ProcessFriendRequestMessageWithStatus 处理好友请求消息并自动通过（带预设状态）
func ProcessFriendRequestMessageWithStatus(wxid string, msg map[string]interface{}, enabled bool) {
	if !enabled {
		return
	}
	processFriendRequestMessageInternal(wxid, msg)
}

// processFriendRequestMessageInternal 内部好友请求处理逻辑
func processFriendRequestMessageInternal(wxid string, msg map[string]interface{}) {

	// 检查消息类型 - 好友请求通常是系统消息类型10000
	msgType := msg["msgType"]
	var isMsgType10000 bool
	switch v := msgType.(type) {
	case int32:
		isMsgType10000 = v == 10000
	case int:
		isMsgType10000 = v == 10000
	case uint32:
		isMsgType10000 = v == 10000
	case float64:
		isMsgType10000 = int(v) == 10000
	default:
		return
	}

	if !isMsgType10000 {
		return
	}

	// 检查原始内容中是否包含好友请求关键词
	originalContent, ok := msg["originalContent"].(string)
	if !ok {
		return
	}

	// 检查是否包含好友请求相关关键词
	isFriendRequest := strings.Contains(originalContent, "加你为朋友") ||
		strings.Contains(originalContent, "请求添加你为朋友") ||
		strings.Contains(originalContent, "想要添加你为朋友") ||
		strings.Contains(originalContent, "申请添加你为朋友") ||
		strings.Contains(originalContent, "verifyuser") ||
		strings.Contains(originalContent, "fromusername") ||
		(strings.Contains(originalContent, "<msg>") && strings.Contains(originalContent, "scene"))

	if !isFriendRequest {
		return
	}

	fmt.Printf("[AUTO_FRIEND] 检测到好友请求消息，开始自动通过: wxid=%s\n", wxid)

	// 执行自动通过好友请求
	err := autoAcceptFriendRequest(wxid, originalContent)
	if err != nil {
		fmt.Printf("[AUTO_FRIEND] 自动通过好友请求失败: %v\n", err)
	} else {
		fmt.Printf("[AUTO_FRIEND] 自动通过好友请求成功: wxid=%s\n", wxid)
	}
}

// autoAcceptFriendRequest 自动通过好友请求
func autoAcceptFriendRequest(wxid, xmlContent string) error {
	// 解析XML内容，提取好友请求信息
	friendInfo, err := parseFriendRequestXML(xmlContent)
	if err != nil {
		return fmt.Errorf("解析好友请求XML失败: %v", err)
	}

	if friendInfo.V1 == "" || friendInfo.V2 == "" {
		return fmt.Errorf("好友请求信息不完整: V1=%s, V2=%s", friendInfo.V1, friendInfo.V2)
	}

	// 调用PassVerify API通过好友请求
	requestData := map[string]interface{}{
		"Wxid":  wxid,
		"V1":    friendInfo.V1,
		"V2":    friendInfo.V2,
		"Scene": friendInfo.Scene,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("构造请求数据失败: %v", err)
	}

	// 使用相对路径调用本地API
	resp, err := http.Post("http://127.0.0.1:8059/api/Friend/PassVerify", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if success, ok := response["Success"].(bool); !ok || !success {
		message := "未知错误"
		if msg, ok := response["Message"].(string); ok {
			message = msg
		}
		return fmt.Errorf("通过好友请求失败: %s", message)
	}

	// 发送WebSocket通知
	sendFriendRequestNotification(wxid, friendInfo)

	return nil
}

// FriendRequestInfo 好友请求信息结构
type FriendRequestInfo struct {
	V1       string // 用户名或加密票据
	V2       string // 验证票据
	Scene    int    // 来源场景
	Nickname string // 昵称
	Content  string // 验证消息
}



// parseFriendRequestXML 解析好友请求XML内容
func parseFriendRequestXML(xmlContent string) (*FriendRequestInfo, error) {
	info := &FriendRequestInfo{Scene: 3} // 默认场景值

	// 提取必需字段
	info.V1 = extractXMLValue(xmlContent, "fromusername")
	info.V2 = extractXMLValue(xmlContent, "encryptusername")

	// 如果V2为空，尝试使用ticket
	if info.V2 == "" {
		info.V2 = extractXMLValue(xmlContent, "ticket")
	}

	// 提取可选字段
	if sceneStr := extractXMLValue(xmlContent, "scene"); sceneStr != "" {
		if scene, err := strconv.Atoi(sceneStr); err == nil {
			info.Scene = scene
		}
	}
	info.Nickname = extractXMLValue(xmlContent, "fromnickname")
	info.Content = extractXMLValue(xmlContent, "content")

	if info.V1 == "" {
		return nil, fmt.Errorf("无法提取用户名信息")
	}

	return info, nil
}

// sendFriendRequestNotification 发送好友请求通知
func sendFriendRequestNotification(wxid string, friendInfo *FriendRequestInfo) {
	notification := map[string]interface{}{
		"type":      "auto_friend_request",
		"wxid":      wxid,
		"timestamp": time.Now().Unix(),
		"data": map[string]interface{}{
			"action":   "accepted",
			"username": friendInfo.V1,
			"nickname": friendInfo.Nickname,
			"content":  friendInfo.Content,
			"scene":    friendInfo.Scene,
		},
	}

	// 发送到WebSocket（如果连接存在）
	SendNotificationToWebSocket(wxid, notification)
}

// base64DecodeString 和 getStringFromMap 函数已移动到 AutoNotifications.go 中
