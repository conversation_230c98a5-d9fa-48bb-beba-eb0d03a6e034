package Algorithm

import (
	"crypto/elliptic"
	"hash"
)

//浏览器版本
//[]byte("Windows-QQBrowser")

var MmtlsShortHost = "extshort.weixin.qq.com" // "extshort.weixin.qq.com"	// "szshort.weixin.qq.com"
var MmtlsLongHost = "long.weixin.qq.com"
var MmtlsLongPort = 80

// ipad 参数
var IosBuildVersion = "19H386"
var KernelType = "Darwin"
var KernelVersion = "21.6.0"
var KernelRelease = "Darwin Kernel Version 21.6.0: Sun Oct 15 00:17:39 PDT 2023; root:xnu-8020.241.42~8/RELEASE_ARM64_T7000"

// ipad
var IPadDeviceType = "iPad iPadOS18.0.1"
var IPadDeviceName = "iPad Pro 13(M4)"
var IPadModel = "iPad16,6"
var IPadOsVersion = "18.0.1"

// iphone
var IPhoneDeviceType = "iPhone iOS18.0.1"
var IPhoneDeviceName = "iPhone 16 Pro Max"
var IPhoneModel = "iPhone9,2"
var IPhoneOsVersion = "18.0.1"

// 安卓
var AndroidDeviceType = "android-34"
var AndroidManufacture = "HUAWEI Mate XT"
var AndroidDeviceName = "HUAWEI"
var AndroidModel = "GRL-AL10"
var AndroidOsVersion = "12"
var AndroidIncremental = "1"

// 安卓 pad
var AndroidPadDeviceType = "pad-android-34"
var AndroidPadModel = "HUAWEI MRO-W00" //HUAWEI MatePad Pro
var AndroidPadDeviceName = "HUAWEI MatePad Pro"
var AndroidPadOsVersion = "10"

var WinUnifiedDeviceType = "UnifiedPCWindows 11 x86_64"
var WinUnifiedDeviceName = "DESKTOP-P0QLAW8"
var WinUnifiedModel = "ASUS"
var WinUnifiedOsVersion = "11"

// win
var WinDeviceType = "Windows 11 x64"
var WinDeviceName = "DESKTOP-P0QLAW8" //
var WinModel = "ASUS"
var WinOsVersion = "11"

// 车载
var CarDeviceType = "car-31"
var CarDeviceName = "Xiaomi-M2012K11AC"
var CarModel = "Xiaomi-M2012K11AC"
var CarOsVersion = "10"

// mac
var MacDeviceType = "iMac MacBookPro16,1 OSX OSX11.5.2 build(20G95)"
var MacDeviceName = "MacBook Pro"
var MacModel = "iMac MacBookPro16,1"
var MacOsVersion = "11.5.2"

// 版本号
var IPadVersion = int32(0x18003926)  //ipad 8.0.57
var IPadVersionx = int32(0x18003926) //ipad绕过验证码int32(0x17000523)

var IPhoneVersion = int32(0x18003926) //62IPhone

var AndroidVersion = int32(0x28003933)  //A16Android 8.0.57
var AndroidVersion1 = int32(0x28003035) //A16Android848

var AndroidPadVersion = int32(0x28003933)  //安卓平板 8.0.57
var AndroidPadVersionx = int32(0x27001032) //安卓平板绕过验证码

var WinVersion = int32(0x63090C11)        //win
var WinUwpVersion = int32(0x620603C8)     //winuwp绕过验证码
var WinUnifiedVersion = int32(0x6400010D) //WinUnified

var CarVersion = int32(0x2100091B) //车载

var MacVersion = int32(0x1308080B) //mac

var RSA182_N = "D153E8A2B314D2110250A0A550DDACDCD77F5801F3D1CC21CB1B477E4F2DE8697D40F10265D066BE8200876BB7135EDC74CDBC7C4428064E0CDCBE1B6B92D93CEAD69EC27126DEBDE564AAE1519ACA836AA70487346C85931273E3AA9D24A721D0B854A7FCB9DED49EE03A44C189124FBEB8B17BB1DBE47A534637777D33EEC88802CD56D0C7683A796027474FEBF237FA5BF85C044ADC63885A70388CD3696D1F2E466EB6666EC8EFE1F91BC9353F8F0EAC67CC7B3281F819A17501E15D03291A2A189F6A35592130DE2FE5ED8E3ED59F65C488391E2D9557748D4065D00CBEA74EB8CA19867C65B3E57237BAA8BF0C0F79EBFC72E78AC29621C8AD61A2B79B"
var RSA182_E = "010001"

var Md5OfMachOHeader = string("a4d2a72915e1c2e39a86c8a11bf7f12b")
var AppUUID = string("12623847-C8BD-3445-834D-2A01ED0D89DB")

type HYBRID_STATUS int32

const (
	HYBRID_ENC HYBRID_STATUS = 0
	HYBRID_DEC HYBRID_STATUS = 1
)

type Client struct {
	PubKey     []byte
	Privkey    []byte
	InitPubKey []byte
	Externkey  []byte

	Version    int32
	DeviceType string

	clientHash hash.Hash
	serverHash hash.Hash

	curve elliptic.Curve

	IsAndroid bool

	Status HYBRID_STATUS
}

type PacketHeader struct {
	PacketCryptType byte
	Flag            uint16
	RetCode         uint32
	UICrypt         uint32
	Uin             uint32
	Cookies         []byte
	Data            []byte
}

type PackData struct {
	Reqdata          []byte
	Cgi              int
	Uin              uint32
	Cookie           []byte
	ClientVersion    int
	Sessionkey       []byte
	EncryptType      uint8
	Loginecdhkey     []byte
	Clientsessionkey []byte
	Serversessionkey []byte
	UseCompress      bool
	MMtlsClose       bool
}
