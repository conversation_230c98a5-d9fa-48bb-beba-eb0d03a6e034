/* 账号相关样式 - v2.0 暗色主题适配版本 */

/* 账号概览部分 */
.accounts-section {
  background: var(--bg-primary);
  padding: 32px;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  margin-bottom: 48px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.accounts-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 20px;
  width: 100%;
  min-height: 100px;
}

.account-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 160px;
  display: block;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-light);
}

.account-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-medium);
  background: var(--bg-hover);
}

.account-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.account-card:hover::before {
  opacity: 1;
}

.account-card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.account-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: var(--shadow-light);
  background: var(--primary-color);
}

.account-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
  display: block;
}

.account-avatar .avatar-fallback {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
  position: absolute;
  top: 0;
  left: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.account-info {
  flex: 1;
}

.account-nickname {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
  letter-spacing: -0.01em;
  line-height: 1.3;
}

.account-wxid {
  font-size: 13px;
  color: var(--text-tertiary);
  font-family: "Inter", "Nunito Sans", "SF Pro Rounded", sans-serif;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 220px;
  cursor: help;
  position: relative;
  font-weight: 400;
  letter-spacing: -0.01em;
}

/* 微信号hover显示完整内容 */
.account-wxid::after {
  content: attr(data-full-wxid);
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: var(--radius-small);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  pointer-events: none;
}

.account-wxid:hover::after {
  opacity: 1;
  visibility: visible;
  top: calc(100% + 5px);
}

.account-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.account-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.account-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-small);
  background: transparent;
  color: var(--text-tertiary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0.3;
  transform: scale(0.9);
}

.account-card:hover .account-action-btn {
  opacity: 1;
  transform: scale(1);
}

.account-action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  transform: scale(1.1);
  opacity: 1;
}

.account-action-btn.delete-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  transform: scale(1.1);
  opacity: 1;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 删除按钮的特殊样式 */
.account-action-btn.delete-btn {
  position: relative;
}

.account-action-btn.delete-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-small);
  background: rgba(239, 68, 68, 0.05);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.account-card:hover .account-action-btn.delete-btn::before {
  opacity: 1;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  box-shadow: 0 0 0 2px var(--bg-primary);
}

.status-online {
  color: var(--success-color);
}

.status-online .status-dot {
  background: var(--success-color);
}

.status-offline {
  color: var(--error-color);
}

.status-offline .status-dot {
  background: var(--error-color);
}

.status-away {
  color: var(--warning-color);
}

.status-away .status-dot {
  background: var(--warning-color);
}

.status-busy {
  color: #ff6b6b;
}

.status-busy .status-dot {
  background: #ff6b6b;
}

.status-unknown {
  color: var(--text-tertiary);
}

.status-unknown .status-dot {
  background: var(--text-tertiary);
}

.account-card-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
}

.account-card-body::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  width: 60px;
  height: 1px;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.3), transparent);
}

/* 账号详情行布局 */
.account-detail-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  padding: 4px 0;
}

/* 重要信息行（需要完整显示） */
.account-detail-row.important {
  grid-template-columns: 1fr;
  background: var(--primary-light);
  border-radius: 8px;
  padding: 12px 16px;
  margin: 8px -4px;
  border: 1px solid var(--primary-light);
}

.account-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

/* 水平布局的详情项 - 左对齐优化 */
.account-detail.horizontal {
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  padding: 8px 0;
}

.account-detail.horizontal .account-detail-label {
  flex-shrink: 0;
  min-width: 70px;
  text-align: left;
}

.account-detail.horizontal .account-detail-value {
  text-align: left;
  flex: 1;
  min-width: 0;
}

.account-detail-label {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
  letter-spacing: 0.02em;
  margin-bottom: 2px;
}

.account-detail-value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

/* 长文本处理 - 默认省略号显示 */
.account-detail-value.long-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
  transition: all 0.3s ease;
  position: relative;
}

/* Tooltip效果 */
.account-detail-value.long-text {
  position: relative;
}

.account-detail-value.long-text::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: var(--radius-small);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  pointer-events: none;
  max-width: 300px;
  word-break: break-all;
  white-space: normal;
}

.account-detail-value.long-text:hover::after {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 5px);
}

/* 重要信息完整显示 - 微信风格 */
.account-detail-value.important {
  word-break: break-all;
  white-space: normal;
  font-family: "Inter", "Nunito Sans", "SF Pro Rounded", sans-serif;
  font-size: 13px;
  font-weight: 600;
  color: var(--primary-color);
  line-height: 1.4;
  letter-spacing: -0.01em;
  cursor: pointer;
  transition: color 0.2s ease;
}

.account-detail-value.important:hover {
  color: var(--primary-hover);
}

/* 微信风格的ID显示 - 仅在需要时添加轻微背景 */
.account-detail-value.important.selectable {
  background: var(--primary-light);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  margin: 0 2px;
}

.account-detail-value.important.selectable:hover {
  background: var(--bg-hover);
}

/* 普通文本信息样式 - 微信风格 */
.account-detail-value {
  font-family: "Inter", "Nunito Sans", "SF Pro Rounded", sans-serif;
  letter-spacing: -0.01em;
}

/* 次要信息样式 */
.account-detail-value.secondary {
  color: var(--text-tertiary);
  font-size: 13px;
  font-weight: 400;
}

/* 状态信息样式 */
.account-detail-value.status {
  font-weight: 500;
}

.accounts-loading {
  text-align: center;
  padding: 40px;
  color: var(--text-tertiary);
}

.accounts-empty {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-tertiary);
}

.accounts-empty i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.accounts-empty h3 {
  font-size: 18px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.accounts-empty p {
  font-size: 14px;
}

/* 账号管理占位符 */
.accounts-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: var(--text-tertiary);
}

.accounts-placeholder i {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.3;
}

.accounts-placeholder h3 {
  font-size: 20px;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.accounts-placeholder p {
  font-size: 14px;
  max-width: 400px;
  margin: 0 auto;
}

/* 账号空状态样式 - 覆盖原有样式 */
.accounts-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
  background: var(--bg-secondary);
  border-radius: var(--radius-large);
  border: 2px dashed var(--border-color);
  margin: 20px 0;
  text-align: center;
  transition: all 0.3s ease;
}

.accounts-empty:hover {
  border-color: var(--primary-color);
  background: var(--bg-hover);
}

.accounts-empty .empty-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.2);
}

.accounts-empty .empty-icon i {
  font-size: 36px;
  color: white;
  margin: 0;
  opacity: 1;
}

.accounts-empty h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.accounts-empty p {
  font-size: 15px;
  color: var(--text-secondary);
  margin-bottom: 24px;
  max-width: 300px;
  line-height: 1.6;
}

.accounts-empty .empty-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 账号详情模态框 */
.account-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.account-detail-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.account-detail-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.account-detail-card h4 {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-light);
  position: relative;
  letter-spacing: -0.01em;
}

.account-detail-card h4::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  border-radius: 1px;
}

.account-detail-card h4 i {
  color: var(--primary-color);
  font-size: 16px;
}

.account-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.account-detail-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.account-detail-item:hover {
  background: var(--bg-hover);
  margin: 0 -12px;
  padding: 12px;
  border-radius: 8px;
  border-bottom: 1px solid var(--border-light);
}

/* 模态框中的标签样式 */
.account-detail-item .account-detail-label {
  font-size: 13px;
  color: var(--text-tertiary);
  font-weight: 500;
  letter-spacing: 0.02em;
  flex-shrink: 0;
  min-width: 80px;
}

/* 模态框中的数值样式 */
.account-detail-item .account-detail-value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 600;
  font-family: "SF Mono", "Monaco", "Menlo", "Ubuntu Mono", monospace;
  text-align: right;
  word-break: break-all;
  line-height: 1.4;
  max-width: 200px;
}

/* 重要信息的特殊样式（如设备ID） - 微信风格 */
.account-detail-item .account-detail-value.important {
  color: var(--primary-color);
  font-weight: 600;
  max-width: none;
  text-align: left;
  font-family: "Inter", "Nunito Sans", "SF Pro Rounded", sans-serif;
  letter-spacing: -0.01em;
  cursor: pointer;
  transition: color 0.2s ease;
}

.account-detail-item .account-detail-value.important:hover {
  color: var(--primary-hover);
}

.account-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
}

.account-status-badge.online {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
}

.account-status-badge.offline {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.account-status-badge.away {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.account-status-badge.busy {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
}

.account-status-badge.unknown {
  background: rgba(107, 114, 128, 0.1);
  color: var(--text-tertiary);
}

.account-status-badge .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.account-status-badge.online .status-dot {
  background: var(--success-color);
}

.account-status-badge.offline .status-dot {
  background: var(--error-color);
}

.account-status-badge.away .status-dot {
  background: var(--warning-color);
}

.account-status-badge.busy .status-dot {
  background: #ff6b6b;
}

.account-status-badge.unknown .status-dot {
  background: var(--text-tertiary);
}

.account-avatar-large {
  width: 96px;
  height: 96px;
  border-radius: 24px;
  position: relative;
  overflow: hidden;
  margin: 0 auto 20px;
  flex-shrink: 0;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 2px 0 rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.account-avatar-large img {
  width: 100%;
  height: 100%;
  border-radius: 24px;
  object-fit: cover;
  display: block;
}

.account-avatar-large .avatar-fallback {
  width: 100%;
  height: 100%;
  border-radius: 24px;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 36px;
  position: absolute;
  top: 0;
  left: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.account-basic-info {
  text-align: center;
  margin-bottom: 32px;
  padding: 32px 24px;
  background: var(--bg-primary);
  border-radius: 20px;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.account-basic-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-light), transparent);
}

.account-basic-info h3 {
  font-size: 26px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  letter-spacing: -0.02em;
}

.account-basic-info p {
  font-size: 15px;
  color: var(--text-tertiary);
  font-family: "SF Mono", "Monaco", "Menlo", "Ubuntu Mono", monospace;
  background: var(--bg-tertiary);
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-block;
  margin-bottom: 16px;
  font-weight: 500;
}

/* 账号错误状态样式 */
.accounts-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
  background: var(--error-light);
  border-radius: var(--radius-large);
  border: 2px dashed var(--error-color);
  margin: 20px 0;
  text-align: center;
  color: var(--error-color);
}

.accounts-error .error-icon {
  width: 80px;
  height: 80px;
  background: var(--error-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(255, 77, 79, 0.2);
}

.accounts-error .error-icon i {
  font-size: 36px;
  color: white;
}

.accounts-error h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
}

.accounts-error p {
  font-size: 15px;
  margin-bottom: 24px;
  max-width: 300px;
  line-height: 1.6;
}

.accounts-error .error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 账号详情操作按钮 */
.account-detail-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 账号详情页面的按钮继承全局简约样式 */

/* 删除确认模态框样式 */
.delete-confirm-content {
  max-width: 500px;
  width: 90%;
}

.delete-warning {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--warning-light);
  border: 1px solid var(--warning-color);
  border-radius: 8px;
}

.warning-icon {
  flex-shrink: 0;
  font-size: 24px;
  color: var(--warning-color);
  margin-top: 4px;
}

.warning-content h4 {
  margin: 0 0 12px 0;
  color: var(--warning-color);
  font-size: 16px;
  font-weight: 600;
}

.warning-content p {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  line-height: 1.5;
}

.warning-content ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
  color: var(--text-primary);
}

.warning-content li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.delete-options {
  margin-bottom: 24px;
}

.option-group {
  margin-bottom: 16px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  position: relative;
  flex-shrink: 0;
  margin-top: 1px;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.option-description {
  display: block;
  margin-top: 4px;
  color: var(--text-secondary);
  font-size: 12px;
  line-height: 1.3;
}

.delete-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
}

.delete-actions .btn {
  min-width: 100px;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.delete-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 暗色主题特殊优化 */
[data-theme="dark"] {
  /* 账号详情卡片在暗色主题下的特殊处理 */
  .account-detail-card {
    background: linear-gradient(145deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-color);
  }

  .account-detail-card:hover {
    border-color: var(--primary-color);
    box-shadow:
      0 8px 32px rgba(64, 150, 255, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.3),
      var(--shadow-medium);
  }

  /* 账号基本信息在暗色主题下的优化 */
  .account-basic-info {
    background: linear-gradient(145deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-color);
  }

  .account-basic-info::before {
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.6;
  }

  /* 账号卡片在暗色主题下的优化 */
  .account-card {
    background: linear-gradient(145deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-color);
  }

  .account-card:hover {
    border-color: var(--primary-color);
    background: linear-gradient(145deg, var(--bg-hover) 0%, var(--bg-tertiary) 100%);
    box-shadow:
      0 8px 32px rgba(64, 150, 255, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.3),
      var(--shadow-medium);
  }

  /* 状态徽章在暗色主题下的优化 */
  .account-status-badge {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
  }

  .account-status-badge.online {
    background: rgba(115, 209, 61, 0.15);
    border-color: var(--success-color);
  }

  .account-status-badge.offline {
    background: rgba(255, 120, 117, 0.15);
    border-color: var(--error-color);
  }

  .account-status-badge.away {
    background: rgba(255, 197, 61, 0.15);
    border-color: var(--warning-color);
  }
}
