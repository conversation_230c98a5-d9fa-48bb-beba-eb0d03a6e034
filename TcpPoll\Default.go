//go:build !linux
// +build !linux

package TcpPoll

import (
	"errors"
	"wechatdll/comm"
)

type TcpManager struct {
	running bool // 控制是否消息loop
}

type TcpClient struct {
	running bool // 控制是否消息loop
}

func GetTcpManager() (*TcpManager, error) {
	return nil, errors.New("windows不支持")
}

func (manager *TcpManager) Add(key string, client *TcpClient) error {
	return errors.New("windows不支持")
}

func (manager *TcpManager) Remove(client *TcpClient) {
	return
}

func (manager *TcpManager) GetClient(loginData *comm.LoginData, businessFunc interface{}) (*TcpClient, error) {
	return nil, errors.New("windows不支持")
}

func (manager *TcpManager) RunEventLoop() {
	return
}

func (client *TcpClient) MmtlsSend(data []byte, cmdId int, tag string) (*[]byte, error) {
	return nil, errors.New("windows不支持")
}

func (client *TcpClient) SetStartTime(time int64) {
	return
}

// 添加缺少的方法以满足 ClientInterface 接口
func (client *TcpClient) IsClosed() bool {
	return true // Windows版本默认返回已关闭
}

func (client *TcpClient) Terminate() {
	return // Windows版本空实现
}

func (client *TcpClient) Send(data []byte, tag string) error {
	return errors.New("windows不支持")
}

func (client *TcpClient) Connect() error {
	return errors.New("windows不支持")
}

func (client *TcpClient) IsHandshaking() bool {
	return false
}

func (client *TcpClient) GetStartTime() int64 {
	return 0
}

func (client *TcpClient) GetLastHeartbeatTime() int64 {
	return 0
}

// 添加管理器缺少的方法
func (manager *TcpManager) Stop() {
	return
}
