package controllers

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/clientsdk/baseutils"
	"wechatdll/comm"
	"wechatdll/models"
	"wechatdll/models/Login"
	"wechatdll/srv"
	"wechatdll/srv/wxcore"

	"github.com/bitly/go-simplejson"
)

// LoginController 登录控制器
//
// 功能概述：
// - 支持多种设备类型的微信登录（iPad、安卓Pad、Mac、Car等）
// - 支持二维码登录和62数据登录
// - 支持二次验证和唤醒登录
// - 支持代理连接（注意：代理必须使用SOCKS协议）
//
// 安全注意事项：
// - 所有登录操作都会记录日志
// - 支持登录状态检查和自动过期处理
// - 62数据包含敏感信息，需要妥善保管
type LoginController struct {
	BaseController
}

// LoginGetQR 获取iPad设备的登录二维码
//
// 功能说明：
// - 生成iPad设备专用的微信登录二维码
// - 支持设备ID复用，避免频繁创建新设备
// - 自动处理MMTLS连接初始化
// - 返回base64编码的二维码图片和相关信息
//
// 参数说明：
// - DeviceID: 设备标识符，用于设备复用
// - DeviceName: 设备名称，默认为"iPad"
// - Proxy: 代理配置，可选，必须使用SOCKS协议
//
// @Summary 获取iPad登录二维码
// @Description 生成iPad设备的微信登录二维码，支持设备复用和代理连接
// @Tags Login
// @Accept json
// @Produce json
// @Param body body Login.GetQRReq true "二维码获取参数，代理配置可选"
// @Success 200 {object} models.StandardResponse{data=Login.GetQRRes} "二维码获取成功"
// @Failure 400 {object} models.StandardResponse "参数错误"
// @Failure 500 {object} models.StandardResponse "系统错误或MMTLS初始化失败"
// @router /LoginGetQR [post]
func (c *LoginController) LoginGetQR() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODE(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(iPad-绕过验证码)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRx [post]
func (c *LoginController) LoginGetQRx() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody

	// 如果RequestBody为空，尝试直接从Request.Body读取
	if len(data) == 0 {
		bodyBytes, err := io.ReadAll(c.Ctx.Request.Body)
		if err != nil {
			Result := models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("读取请求体失败：%v", err.Error()),
				Data:    nil,
			}
			c.Data["json"] = &Result
			c.ServeJSON()
			return
		}
		data = bodyBytes
	}

	// 检查请求体是否为空
	if len(data) == 0 {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "请求体不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 检查请求体是否是有效的JSON
	if !json.Valid(data) {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "请求体不是有效的JSON格式",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 尝试解析JSON，如果失败则尝试修复常见问题
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		// 尝试修复常见的JSON问题
		dataStr := string(data)

		// 检查是否是截断的JSON，尝试补全
		if strings.Contains(err.Error(), "unexpected end of JSON input") {
			// 尝试补全常见的截断情况
			if strings.HasSuffix(dataStr, `"Proxy":{`) {
				dataStr += `}}`
				err = json.Unmarshal([]byte(dataStr), &GetQR)
			} else if strings.HasSuffix(dataStr, `"Proxy":`) {
				dataStr += `{}`
				err = json.Unmarshal([]byte(dataStr), &GetQR)
			} else if !strings.HasSuffix(dataStr, `}`) {
				dataStr += `}`
				err = json.Unmarshal([]byte(dataStr), &GetQR)
			}
		}

		// 如果修复后仍然失败
		if err != nil {
			Result := models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("JSON解析失败：%v", err.Error()),
				Data:    nil,
			}
			c.Data["json"] = &Result
			c.ServeJSON()
			return
		}
	}

	WXDATA := Login.GetQRCODEx(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(安卓Pad)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRPad [post]
func (c *LoginController) LoginGetQRPad() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODEPad(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(安卓Pad-绕过验证码)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRPadx [post]
func (c *LoginController) LoginGetQRPadx() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODEPadx(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(Windows)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRWin [post]
func (c *LoginController) LoginGetQRWin() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODEWin(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(WindowsUwp-绕过验证码)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRWinUwp [post]
func (c *LoginController) LoginGetQRWinUwp() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODEWinUwp(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(WinUnified-统一PC版)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRWinUnified [post]
func (c *LoginController) LoginGetQRWinUnified() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODEWinUnified(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(Car)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRCar [post]
func (c *LoginController) LoginGetQRCar() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Login.GetQRCODECar(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取二维码(Mac)
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /LoginGetQRMac [post]
func (c *LoginController) LoginGetQRMac() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.GetQRCODEMac(GetQR)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 检测二维码
// @Param	uuid		query 	string	true		"请输入取码时返回的UUID"
// @Success 200
// @router /LoginCheckQR [post]
func (c *LoginController) LoginCheckQR() {
	uuid := c.GetString("uuid")
	WXDATA := Login.CheckUuid(uuid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 二次登录
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Failure 200
// @router /LoginTwiceAutoAuth [post]
func (c *LoginController) LoginTwiceAutoAuth() {
	wxid := c.GetString("wxid")
	WXDATA, _ := Login.Secautoauth(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 62登录(账号或密码)
// @Param	body			body 	Login.Data62LoginReq	true		"不使用代理请留空"
// @Failure 200
// @router /Data62Login [post]
func (c *LoginController) Data62Login() {
	var reqdata Login.Data62LoginReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.Data62(reqdata, Algorithm.MmtlsShortHost)

	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 62登录(账号或密码), 并申请使用SMS验证
// @Param	body			body 	Login.Data62LoginReq	true		"不使用代理请留空"
// @Failure 200
// @router /Data62SMSApply [post]
func (c *LoginController) Data62SMSApply() {
	var reqdata Login.Data62LoginReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 生成62随机数据
	if reqdata.Data62 == "" || reqdata.Data62 == "string" {
		deviceId := baseutils.CreateDeviceId(reqdata.Data62)
		reqdata.Data62 = baseutils.Get62Data(deviceId)
	}
	if reqdata.DeviceName == "" || reqdata.DeviceName == "string" {
		reqdata.DeviceName = "iPad"
	}
	// 使用62数据登录并自动滑块
	WXDATA := Login.Data62(reqdata, Algorithm.MmtlsShortHost)

	// 记录62
	WXDATA.Data62 = reqdata.Data62

	// 二次验证使用短信验证
	message, transed := WXDATA.Data.(mm.UnifyAuthResponse)
	if transed && strings.Index(message.GetBaseResponse().GetErrMsg().GetString_(), "&ticket=") >= 0 {
		checkUrl, againUrl, setCookie := Login.WechatSMS1(message.GetBaseResponse().GetErrMsg().GetString_(), comm.GenDefaultIpadUA(), reqdata.Proxy)
		WXDATA = models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "已申请短信验证",
			Data: &map[string]string{
				"CheckUrl": checkUrl,
				"AgainUrl": againUrl,
				"Cookie":   setCookie,
			},
			Data62: reqdata.Data62,
		}
	}

	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 62登录(账号或密码), 重发验证码
// @Param	body			body 	Login.Data62SMSAgainReq	true		"不使用代理请留空"
// @Failure 200
// @router /Data62SMSAgain [post]
func (c *LoginController) Data62SMSAgain() {
	var reqdata Login.Data62SMSAgainReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 重发短信
	headers := &map[string]string{
		"Cookie": reqdata.Cookie,
	}
	res := comm.HttpGet1(reqdata.Url, headers, comm.GenDefaultIpadUA(), reqdata.Proxy)
	resJson, err := simplejson.NewJson([]byte(res))
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	title, err := resJson.Get("resultData").Get("title").String()
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "",
		Data:    title,
		Data62:  "",
	}
	c.Data["json"] = &WXDATA
	c.ServeJSON()
	return
}

// @Summary 62登录(账号或密码), 短信验证
// @Param	body			body 	Login.Data62SMSVerifyReq	true		"不使用代理请留空"
// @Failure 200
// @router /Data62SMSVerify [post]
func (c *LoginController) Data62SMSVerify() {
	var reqdata Login.Data62SMSVerifyReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 验证短信
	verifyUrl := strings.Replace(reqdata.Url, "[[[verifycode]]]", reqdata.Sms, -1)
	verifyUrl = strings.Replace(verifyUrl, "[[[currentMilliseStamp]]]", string(time.Now().Unix()), -1)
	headers := &map[string]string{
		"Cookie": reqdata.Cookie,
	}
	res := comm.HttpGet1(verifyUrl, headers, comm.GenDefaultIpadUA(), reqdata.Proxy)
	resJson, err := simplejson.NewJson([]byte(res))
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	title, err := resJson.Get("resultData").Get("title").String()
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "",
		Data:    title,
		Data62:  "",
	}
	c.Data["json"] = &WXDATA
	c.ServeJSON()
	return
}

// @Summary 62登录(账号或密码), 并申请使用二维码验证
// @Param	body			body 	Login.Data62LoginReq	true		"不使用代理请留空"
// @Failure 200
// @router /Data62QRCodeApply [post]
func (c *LoginController) Data62QRCodeApply() {
	var reqdata Login.Data62LoginReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 生成62随机数据
	if reqdata.Data62 == "" || reqdata.Data62 == "string" {
		deviceId := baseutils.CreateDeviceId(reqdata.Data62)
		reqdata.Data62 = baseutils.Get62Data(deviceId)
	}
	if reqdata.DeviceName == "" || reqdata.DeviceName == "string" {
		reqdata.DeviceName = "iPad"
	}
	// 使用62数据登录并自动滑块
	WXDATA := Login.Data62(reqdata, Algorithm.MmtlsShortHost)

	// 记录62
	WXDATA.Data62 = reqdata.Data62

	// 二次验证使用短信验证
	message, transed := WXDATA.Data.(mm.UnifyAuthResponse)
	if transed && strings.Index(message.GetBaseResponse().GetErrMsg().GetString_(), "&ticket=") >= 0 {
		qrUrl, checkUrl := Login.WeChatQrCode1(message.GetBaseResponse().GetErrMsg().GetString_(), comm.GenDefaultIpadUA(), reqdata.Proxy)
		WXDATA = models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "已申请短信验证",
			Data: &map[string]string{
				"QrUrl":    qrUrl,
				"CheckUrl": checkUrl,
			},
			Data62: reqdata.Data62,
		}
	}

	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 62登录(账号或密码), 二维码验证校验
// @Param	body			body 	Login.Data62SMSVerifyReq	true		"不使用代理请留空"
// @Failure 200
// @router /Data62QRCodeVerify [post]
func (c *LoginController) Data62QRCodeVerify() {
	var reqdata Login.Data62QRCodeVerifyReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 验证短信
	verifyUrl := reqdata.Url
	verifyUrl = strings.Replace(verifyUrl, "[[[currentMilliseStamp]]]", string(time.Now().Unix()), -1)
	res := comm.HttpGet1(verifyUrl, nil, comm.GenDefaultIpadUA(), reqdata.Proxy)
	WXDATA := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "",
		Data:    res,
		Data62:  "",
	}
	c.Data["json"] = &WXDATA
	c.ServeJSON()
	return
}

// @Summary A16登录(账号或密码)
// @Param	body			body 	Login.A16LoginParam	true		"不使用代理请留空"
// @Failure 200
// @router /A16Data [post]
func (c *LoginController) A16Data() {
	var reqdata Login.A16LoginParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.AndroidA16Login(reqdata, Algorithm.MmtlsShortHost)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary A16登录(账号或密码) - android == 新版云函数
// @Param	body			body 	Login.A16LoginParam	true		"不使用代理请留空"
// @Failure 200
// @router /A16Data1 [post]
func (c *LoginController) A16Data1() {
	var reqdata Login.A16LoginParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.AndroidA16Login1(reqdata, Algorithm.MmtlsShortHost)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 心跳包
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /HeartBeat [post]
func (c *LoginController) HeartBeat() {
	wxid := c.GetString("wxid")
	WXDATA, _ := Login.HeartBeat(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 心跳包
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /HeartBeatLong [post]
func (c *LoginController) HeartBeatLong() {
	wxid := c.GetString("wxid")
	WXDATA, _ := Login.HeartBeatLong(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 初始化
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Param	MaxSynckey		query 	string	false		"二次同步需要带入"
// @Param	CurrentSynckey	query 	string	false		"二次同步需要带入"
// @Success 200
// @router /Newinit [post]
func (c *LoginController) Newinit() {
	wxid := c.GetString("wxid")
	MaxSynckey := c.GetString("MaxSynckey")
	CurrentSynckey := c.GetString("CurrentSynckey")
	WXDATA := Login.Newinit(wxid, MaxSynckey, CurrentSynckey)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 唤醒登录(只限扫码登录)
// @Param	wxid		query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /LoginAwaken [post]
func (c *LoginController) LoginAwaken() {
	wxid := c.GetString("wxid")
	WXDATA := Login.AwakenLogin(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取登录缓存信息
// @Param	wxid		query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /GetCacheInfo [post]
func (c *LoginController) GetCacheInfo() {
	wxid := c.GetString("wxid")
	WXDATA := Login.CacheInfo(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取62数据
// @Param	wxid		query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /Get62Data [post]
func (c *LoginController) Get62Data() {
	wxid := c.GetString("wxid")
	Data62 := Login.Get62Data(wxid)
	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Data62,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
	return
}

// @Summary 获取A16数据
// @Param	wxid		query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /GetA16Data [post]
func (c *LoginController) GetA16Data() {
	wxid := c.GetString("wxid")
	Data62 := Login.GetA16Data(wxid)
	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Data62,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
	return
}

// @Summary 退出登录
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /LogOut [post]
func (c *LoginController) LogOut() {
	wxid := c.GetString("wxid")
	WXDATA := Login.LogOut(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 新设备扫码登录
// @Param	body			body 	Login.ExtDeviceLoginConfirmParam	true		"URL == MAC iPad Windows 的微信二维码解析出来的url"
// @Success 200
// @router /ExtDeviceLoginConfirmGet [post]
func (c *LoginController) ExtDeviceLoginConfirmGet() {
	var reqdata Login.ExtDeviceLoginConfirmParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.ExtDeviceLoginConfirmGet(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 新设备扫码确认登录
// @Param	body			body 	Login.ExtDeviceLoginConfirmParam	true		"URL == MAC iPad Windows 的微信二维码解析出来的url"
// @Success 200
// @router /ExtDeviceLoginConfirmOk [post]
func (c *LoginController) ExtDeviceLoginConfirmOk() {
	var reqdata Login.ExtDeviceLoginConfirmParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.ExtDeviceLoginConfirmOk(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 提交登录验证码
// @Param	body			body 	Login.VerificationcodeParam	true	""
// @Success 200
// @router /YPayVerificationcode [post]
func (c *LoginController) YPayVerificationcode() {
	var reqdata Login.VerificationcodeParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.Verificationcode2(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 开启自动心跳, 自动二次登录（linux 长连接，win 短链接）
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /AutoHeartBeat [post]
func (c *LoginController) AutoHeartBeat() {
	wxid := c.GetString("wxid")
	// 开启自动心跳
	D, err := comm.GetLoginata(wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		errorMsg := fmt.Sprintf("系统异常：%v [%v]", "未找到登录信息", wxid)
		if err != nil {
			errorMsg = fmt.Sprintf("系统异常：%v", err.Error())
		}
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: errorMsg,
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	wxConnectMgr := wxcore.GetWXConnectMgr()
	wXConnect := wxConnectMgr.GetWXConnectByWXID(wxid)
	if wXConnect == nil {
		wxAccount := srv.NewWXAccount(D)
		wXConnect = wxcore.NewWXConnect(wxConnectMgr, wxAccount)
		wxConnectMgr.Add(wXConnect)
	}
	wXConnect.Start()
	err = wXConnect.SendHeartBeat()
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("发送心跳失败：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "发送心跳成功",
		Data:    nil,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 关闭自动心跳、自动二次登录
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /CloseAutoHeartBeat [post]
func (c *LoginController) CloseAutoHeartBeat() {
	wxid := c.GetString("wxid")
	// 关闭自动心跳
	D, err := comm.GetLoginata(wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		errorMsg := fmt.Sprintf("系统异常：%v [%v]", "未找到登录信息", wxid)
		if err != nil {
			errorMsg = fmt.Sprintf("系统异常：%v", err.Error())
		}
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: errorMsg,
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	wxConnectMgr := wxcore.GetWXConnectMgr()
	wXConnect := wxConnectMgr.GetWXConnectByWXID(wxid)
	if wXConnect != nil {
		wXConnect.Stop()
	}
	comm.AutoHeartBeatListClear(wxid)
	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "关闭心跳成功",
		Data:    nil,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 自动心跳日志
// @Param	wxid			query 	string	true		"请输入登录成功的wxid"
// @Success 200
// @router /AutoHeartBeatLog [post]
func (c *LoginController) AutoHeartBeatLog() {
	wxid := c.GetString("wxid")
	// 清理首尾空格
	wxid = strings.TrimSpace(wxid)
	if wxid == "" || wxid == "string" || strings.Contains(wxid, "*") {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "wxid不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	logs := make([]string, 0)
	comm.GETObj("AutoHeartBeatList:"+wxid, &logs)
	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "获取成功, 只保留最新的 100 条心跳、二次登录日志",
		Data:    logs,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// GetLoggedAccounts 获取已登录账号列表
//
// 功能说明：
// - 获取当前所有已登录并保持连接的微信账号信息
// - 返回账号的基本信息、登录状态、设备类型等
// - 支持实时状态查询，便于管理和监控
//
// 返回信息包括：
// - wxid: 微信账号ID
// - nickname: 账号昵称
// - status: 连接状态（online/offline）
// - loginTime: 登录时间
// - deviceType: 设备类型（iPad、Android等）
// - deviceName: 设备名称
// - uin: 用户唯一标识
//
// @Summary 获取已登录账号列表
// @Description 获取当前所有已登录并保持连接的微信账号信息，包括账号状态、登录时间、设备类型等
// @Tags 登录管理
// @Accept json
// @Produce json
// @Success 200 {object} models.ResponseResult{data=[]map[string]interface{}} "成功返回账号列表"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @Router /api/Login/GetLoggedAccounts [get]
func (c *LoginController) GetLoggedAccounts() {
	// 获取微信连接管理器
	wxConnectMgr := wxcore.GetWXConnectMgr()

	// 获取所有已连接的账号信息
	accounts := wxConnectMgr.GetAllConnectedAccounts()

	// 构造响应结果
	result := models.ResponseResult{
		Code:    200,
		Success: true,
		Message: fmt.Sprintf("成功获取 %d 个已登录账号", len(accounts)),
		Data:    accounts,
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// DeleteAccount 删除账号
//
// 功能说明：
// - 完全删除指定的微信账号及其所有相关数据
// - 停止心跳连接和自动登录
// - 清理Redis中的登录数据、心跳日志等
// - 可选择是否先退出登录（发送退出登录请求到微信服务器）
//
// 删除内容包括：
// - Redis中的登录数据（PERM1:wxid 和 wxid_* 键）
// - 心跳连接和定时器
// - 自动心跳日志
// - 设备ID映射关系
// - 其他相关缓存数据
//
// @Summary 删除账号
// @Description 完全删除指定的微信账号及其所有相关数据，包括登录信息、心跳连接、日志等
// @Tags 登录管理
// @Accept json
// @Produce json
// @Param wxid query string true "要删除的微信账号ID"
// @Param logout query bool false "是否先退出登录（默认false）"
// @Success 200 {object} models.ResponseResult "删除成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @Failure 404 {object} models.ResponseResult "账号不存在"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @Router /api/Login/DeleteAccount [post]
func (c *LoginController) DeleteAccount() {
	wxid := strings.TrimSpace(c.GetString("wxid"))
	logoutFirst := c.GetString("logout") == "true"

	// 参数验证
	if wxid == "" || wxid == "string" {
		result := models.ResponseResult{
			Code:    400,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 检查账号是否存在
	loginData, err := comm.GetLoginata(wxid, nil)
	if err != nil || loginData == nil || loginData.Wxid == "" {
		result := models.ResponseResult{
			Code:    404,
			Success: false,
			Message: fmt.Sprintf("账号 %s 不存在或已被删除", wxid),
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 记录要删除的账号信息
	accountInfo := map[string]interface{}{
		"wxid":     loginData.Wxid,
		"nickname": loginData.NickName,
		"mobile":   loginData.Mobile,
		"deviceId": loginData.Deviceid_str,
	}

	// 1. 停止心跳连接
	wxConnectMgr := wxcore.GetWXConnectMgr()
	wXConnect := wxConnectMgr.GetWXConnectByWXID(wxid)
	if wXConnect != nil {
		wXConnect.Stop()
		// 从连接管理器中移除
		wxConnectMgr.Remove(wXConnect)
	}

	// 2. 可选：先退出登录
	var logoutResult string
	if logoutFirst {
		logoutResponse := Login.LogOut(wxid)
		if logoutResponse.Success {
			logoutResult = "已发送退出登录请求"
		} else {
			logoutResult = fmt.Sprintf("退出登录失败: %s", logoutResponse.Message)
		}
	} else {
		logoutResult = "跳过退出登录"
	}

	// 3. 删除Redis中的相关数据
	deletedKeys := c.deleteAccountRedisData(wxid, loginData.Deviceid_str)

	// 4. 清理心跳日志
	comm.AutoHeartBeatListClear(wxid)

	// 构造响应结果
	result := models.ResponseResult{
		Code:    200,
		Success: true,
		Message: fmt.Sprintf("账号 %s (%s) 删除成功", wxid, loginData.NickName),
		Data: map[string]interface{}{
			"deletedAccount": accountInfo,
			"logoutResult":   logoutResult,
			"deletedKeys":    deletedKeys,
			"deletedCount":   len(deletedKeys),
		},
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// deleteAccountRedisData 删除账号在Redis中的所有相关数据
func (c *LoginController) deleteAccountRedisData(wxid, deviceId string) []string {
	var deletedKeys []string

	if comm.RedisClient == nil {
		return deletedKeys
	}

	// 1. 删除主要的登录数据键
	mainKeys := []string{
		wxid,            // 原始键
		"PERM1:" + wxid, // 持久化键
		"TEMP1:" + wxid, // 临时键
		"wxid_" + wxid,  // 兼容旧版本的键
	}

	for _, key := range mainKeys {
		exists, err := comm.RedisClient.Exists(key).Result()
		if err == nil && exists > 0 {
			err = comm.RedisClient.Del(key).Err()
			if err == nil {
				deletedKeys = append(deletedKeys, key)
			}
		}
	}

	// 2. 删除设备ID映射关系
	if deviceId != "" {
		deviceKeys := []string{
			"devId:" + deviceId,
			"wechat:deviceId:" + deviceId,
		}

		for _, key := range deviceKeys {
			exists, err := comm.RedisClient.Exists(key).Result()
			if err == nil && exists > 0 {
				err = comm.RedisClient.Del(key).Err()
				if err == nil {
					deletedKeys = append(deletedKeys, key)
				}
			}
		}
	}

	// 3. 删除心跳相关数据
	heartbeatKeys := []string{
		"AutoHeartBeatList:" + wxid,
	}

	for _, key := range heartbeatKeys {
		exists, err := comm.RedisClient.Exists(key).Result()
		if err == nil && exists > 0 {
			err = comm.RedisClient.Del(key).Err()
			if err == nil {
				deletedKeys = append(deletedKeys, key)
			}
		}
	}

	// 4. 查找并删除其他可能的相关键（使用模式匹配）
	patterns := []string{
		"*" + wxid + "*",
		"*:" + wxid,
		wxid + ":*",
	}

	for _, pattern := range patterns {
		keys, err := comm.RedisClient.Keys(pattern).Result()
		if err == nil {
			for _, key := range keys {
				// 避免重复删除已经处理过的键
				alreadyDeleted := false
				for _, deletedKey := range deletedKeys {
					if key == deletedKey {
						alreadyDeleted = true
						break
					}
				}

				if !alreadyDeleted {
					err = comm.RedisClient.Del(key).Err()
					if err == nil {
						deletedKeys = append(deletedKeys, key)
					}
				}
			}
		}
	}

	return deletedKeys
}
