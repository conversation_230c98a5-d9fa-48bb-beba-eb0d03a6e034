package comm

import (
	"context"
	"fmt"
	"runtime"
	"runtime/debug"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// MemoryLeakDetector 内存泄漏检测器
type MemoryLeakDetector struct {
	mu                 sync.RWMutex
	enabled            bool
	checkInterval      time.Duration
	memoryThreshold    uint64 // 内存阈值（字节）
	goroutineThreshold int    // Goroutine阈值

	// 历史数据
	memoryHistory    []MemorySnapshot
	goroutineHistory []GoroutineSnapshot
	maxHistorySize   int

	// 检测状态
	lastCheck     time.Time
	leakDetected  bool
	alertCallback func(LeakAlert)

	// 上下文控制
	ctx    context.Context
	cancel context.CancelFunc
}

// MemorySnapshot 内存快照
type MemorySnapshot struct {
	Timestamp  time.Time `json:"timestamp"`
	Alloc      uint64    `json:"alloc"`       // 当前分配的内存
	TotalAlloc uint64    `json:"total_alloc"` // 总分配的内存
	Sys        uint64    `json:"sys"`         // 系统内存
	NumGC      uint32    `json:"num_gc"`      // GC次数
	HeapInuse  uint64    `json:"heap_inuse"`  // 堆使用量
	HeapIdle   uint64    `json:"heap_idle"`   // 堆空闲量
	StackInuse uint64    `json:"stack_inuse"` // 栈使用量
}

// GoroutineSnapshot Goroutine快照
type GoroutineSnapshot struct {
	Timestamp time.Time `json:"timestamp"`
	Count     int       `json:"count"`
	Stack     string    `json:"stack,omitempty"`
}

// LeakAlert 泄漏告警
type LeakAlert struct {
	Type         string      `json:"type"`     // memory 或 goroutine
	Severity     string      `json:"severity"` // low, medium, high, critical
	Message      string      `json:"message"`
	Timestamp    time.Time   `json:"timestamp"`
	CurrentValue interface{} `json:"current_value"`
	Threshold    interface{} `json:"threshold"`
	Trend        string      `json:"trend"` // increasing, stable, decreasing
	Suggestions  []string    `json:"suggestions"`
}

var (
	memoryLeakDetector *MemoryLeakDetector
	detectorOnce       sync.Once
)

// GetMemoryLeakDetector 获取内存泄漏检测器实例
func GetMemoryLeakDetector() *MemoryLeakDetector {
	detectorOnce.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())
		memoryLeakDetector = &MemoryLeakDetector{
			enabled:            true,
			checkInterval:      time.Minute * 2,   // 每2分钟检查一次
			memoryThreshold:    500 * 1024 * 1024, // 500MB
			goroutineThreshold: 1000,
			maxHistorySize:     50, // 保留50个历史记录
			ctx:                ctx,
			cancel:             cancel,
		}

		// 启动检测
		go memoryLeakDetector.start()
	})
	return memoryLeakDetector
}

// start 启动检测
func (mld *MemoryLeakDetector) start() {
	ticker := time.NewTicker(mld.checkInterval)
	defer ticker.Stop()

	log.Info("内存泄漏检测器已启动")

	for {
		select {
		case <-mld.ctx.Done():
			log.Info("内存泄漏检测器已停止")
			return
		case <-ticker.C:
			if mld.enabled {
				mld.performCheck()
			}
		}
	}
}

// performCheck 执行检测
func (mld *MemoryLeakDetector) performCheck() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("内存泄漏检测发生panic: %v", r)
		}
	}()

	// 收集内存快照
	memSnapshot := mld.collectMemorySnapshot()
	goroutineSnapshot := mld.collectGoroutineSnapshot()

	// 添加到历史记录
	mld.addMemorySnapshot(memSnapshot)
	mld.addGoroutineSnapshot(goroutineSnapshot)

	// 分析内存泄漏
	mld.analyzeMemoryLeak(memSnapshot)
	mld.analyzeGoroutineLeak(goroutineSnapshot)

	mld.mu.Lock()
	mld.lastCheck = time.Now()
	mld.mu.Unlock()
}

// collectMemorySnapshot 收集内存快照
func (mld *MemoryLeakDetector) collectMemorySnapshot() MemorySnapshot {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return MemorySnapshot{
		Timestamp:  time.Now(),
		Alloc:      m.Alloc,
		TotalAlloc: m.TotalAlloc,
		Sys:        m.Sys,
		NumGC:      m.NumGC,
		HeapInuse:  m.HeapInuse,
		HeapIdle:   m.HeapIdle,
		StackInuse: m.StackInuse,
	}
}

// collectGoroutineSnapshot 收集Goroutine快照
func (mld *MemoryLeakDetector) collectGoroutineSnapshot() GoroutineSnapshot {
	count := runtime.NumGoroutine()

	snapshot := GoroutineSnapshot{
		Timestamp: time.Now(),
		Count:     count,
	}

	// 如果Goroutine数量异常，收集堆栈信息
	if count > mld.goroutineThreshold {
		snapshot.Stack = string(debug.Stack())
	}

	return snapshot
}

// addMemorySnapshot 添加内存快照到历史记录
func (mld *MemoryLeakDetector) addMemorySnapshot(snapshot MemorySnapshot) {
	mld.mu.Lock()
	defer mld.mu.Unlock()

	mld.memoryHistory = append(mld.memoryHistory, snapshot)

	// 保持历史记录大小
	if len(mld.memoryHistory) > mld.maxHistorySize {
		mld.memoryHistory = mld.memoryHistory[1:]
	}
}

// addGoroutineSnapshot 添加Goroutine快照到历史记录
func (mld *MemoryLeakDetector) addGoroutineSnapshot(snapshot GoroutineSnapshot) {
	mld.mu.Lock()
	defer mld.mu.Unlock()

	mld.goroutineHistory = append(mld.goroutineHistory, snapshot)

	// 保持历史记录大小
	if len(mld.goroutineHistory) > mld.maxHistorySize {
		mld.goroutineHistory = mld.goroutineHistory[1:]
	}
}

// analyzeMemoryLeak 分析内存泄漏
func (mld *MemoryLeakDetector) analyzeMemoryLeak(current MemorySnapshot) {
	mld.mu.RLock()
	historyLen := len(mld.memoryHistory)
	mld.mu.RUnlock()

	if historyLen < 5 {
		return // 需要足够的历史数据
	}

	// 检查内存是否超过阈值
	if current.Alloc > mld.memoryThreshold {
		severity := mld.calculateMemorySeverity(current.Alloc)
		trend := mld.calculateMemoryTrend()

		alert := LeakAlert{
			Type:         "memory",
			Severity:     severity,
			Message:      fmt.Sprintf("内存使用量超过阈值: %s", formatBytes(current.Alloc)),
			Timestamp:    time.Now(),
			CurrentValue: current.Alloc,
			Threshold:    mld.memoryThreshold,
			Trend:        trend,
			Suggestions:  mld.getMemoryLeakSuggestions(current),
		}

		mld.triggerAlert(alert)
	}

	// 检查内存增长趋势
	if mld.isMemoryGrowthAbnormal() {
		alert := LeakAlert{
			Type:      "memory",
			Severity:  "medium",
			Message:   "检测到内存持续增长，可能存在内存泄漏",
			Timestamp: time.Now(),
			Trend:     "increasing",
			Suggestions: []string{
				"检查是否有未释放的资源",
				"分析内存分配模式",
				"考虑强制执行GC",
			},
		}

		mld.triggerAlert(alert)
	}
}

// analyzeGoroutineLeak 分析Goroutine泄漏
func (mld *MemoryLeakDetector) analyzeGoroutineLeak(current GoroutineSnapshot) {
	if current.Count > mld.goroutineThreshold {
		severity := mld.calculateGoroutineSeverity(current.Count)
		trend := mld.calculateGoroutineTrend()

		alert := LeakAlert{
			Type:         "goroutine",
			Severity:     severity,
			Message:      fmt.Sprintf("Goroutine数量超过阈值: %d", current.Count),
			Timestamp:    time.Now(),
			CurrentValue: current.Count,
			Threshold:    mld.goroutineThreshold,
			Trend:        trend,
			Suggestions:  mld.getGoroutineLeakSuggestions(current),
		}

		mld.triggerAlert(alert)
	}
}

// calculateMemorySeverity 计算内存严重程度
func (mld *MemoryLeakDetector) calculateMemorySeverity(current uint64) string {
	ratio := float64(current) / float64(mld.memoryThreshold)

	switch {
	case ratio >= 3.0:
		return "critical"
	case ratio >= 2.0:
		return "high"
	case ratio >= 1.5:
		return "medium"
	default:
		return "low"
	}
}

// calculateGoroutineSeverity 计算Goroutine严重程度
func (mld *MemoryLeakDetector) calculateGoroutineSeverity(current int) string {
	ratio := float64(current) / float64(mld.goroutineThreshold)

	switch {
	case ratio >= 3.0:
		return "critical"
	case ratio >= 2.0:
		return "high"
	case ratio >= 1.5:
		return "medium"
	default:
		return "low"
	}
}

// calculateMemoryTrend 计算内存趋势
func (mld *MemoryLeakDetector) calculateMemoryTrend() string {
	mld.mu.RLock()
	defer mld.mu.RUnlock()

	if len(mld.memoryHistory) < 3 {
		return "stable"
	}

	recent := mld.memoryHistory[len(mld.memoryHistory)-3:]

	if recent[2].Alloc > recent[1].Alloc && recent[1].Alloc > recent[0].Alloc {
		return "increasing"
	} else if recent[2].Alloc < recent[1].Alloc && recent[1].Alloc < recent[0].Alloc {
		return "decreasing"
	}

	return "stable"
}

// calculateGoroutineTrend 计算Goroutine趋势
func (mld *MemoryLeakDetector) calculateGoroutineTrend() string {
	mld.mu.RLock()
	defer mld.mu.RUnlock()

	if len(mld.goroutineHistory) < 3 {
		return "stable"
	}

	recent := mld.goroutineHistory[len(mld.goroutineHistory)-3:]

	if recent[2].Count > recent[1].Count && recent[1].Count > recent[0].Count {
		return "increasing"
	} else if recent[2].Count < recent[1].Count && recent[1].Count < recent[0].Count {
		return "decreasing"
	}

	return "stable"
}

// isMemoryGrowthAbnormal 检查内存增长是否异常
func (mld *MemoryLeakDetector) isMemoryGrowthAbnormal() bool {
	mld.mu.RLock()
	defer mld.mu.RUnlock()

	if len(mld.memoryHistory) < 10 {
		return false
	}

	// 检查最近10次的内存使用情况
	recent := mld.memoryHistory[len(mld.memoryHistory)-10:]

	// 计算增长率
	growthCount := 0
	for i := 1; i < len(recent); i++ {
		if recent[i].Alloc > recent[i-1].Alloc {
			growthCount++
		}
	}

	// 如果80%以上的时间都在增长，认为异常
	return float64(growthCount)/float64(len(recent)-1) > 0.8
}

// getMemoryLeakSuggestions 获取内存泄漏建议
func (mld *MemoryLeakDetector) getMemoryLeakSuggestions(snapshot MemorySnapshot) []string {
	suggestions := []string{
		"检查是否有未关闭的文件句柄或网络连接",
		"分析内存分配热点",
		"检查缓存是否有过期清理机制",
	}

	// 根据具体情况添加建议
	if snapshot.HeapInuse > snapshot.HeapIdle*2 {
		suggestions = append(suggestions, "堆内存使用率过高，考虑优化数据结构")
	}

	if snapshot.StackInuse > 50*1024*1024 { // 50MB
		suggestions = append(suggestions, "栈内存使用过多，检查是否有深度递归")
	}

	return suggestions
}

// getGoroutineLeakSuggestions 获取Goroutine泄漏建议
func (mld *MemoryLeakDetector) getGoroutineLeakSuggestions(snapshot GoroutineSnapshot) []string {
	suggestions := []string{
		"检查是否有未正确关闭的channel",
		"分析Goroutine堆栈，查找阻塞的代码",
		"检查是否有死锁情况",
		"确保所有启动的Goroutine都有退出机制",
		"考虑使用context来控制Goroutine生命周期",
	}

	// 根据Goroutine数量添加特定建议
	if snapshot.Count > 500 {
		suggestions = append(suggestions, "Goroutine数量过多，检查是否有泄漏")
	}

	return suggestions
}

// triggerAlert 触发告警
func (mld *MemoryLeakDetector) triggerAlert(alert LeakAlert) {
	log.WithFields(log.Fields{
		"type":      alert.Type,
		"severity":  alert.Severity,
		"trend":     alert.Trend,
		"threshold": alert.Threshold,
		"current":   alert.CurrentValue,
	}).Warn(alert.Message)

	// 调用回调函数
	if mld.alertCallback != nil {
		go mld.alertCallback(alert)
	}

	// 记录到性能监控
	perfMonitor := GetPerformanceMonitor()
	if perfMonitor != nil {
		perfMonitor.RecordMetric("memory_leak_alerts", 1, "count", map[string]string{
			"type":     alert.Type,
			"severity": alert.Severity,
		})
	}
}

// formatBytes 格式化字节数
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// SetAlertCallback 设置告警回调
func (mld *MemoryLeakDetector) SetAlertCallback(callback func(LeakAlert)) {
	mld.mu.Lock()
	defer mld.mu.Unlock()
	mld.alertCallback = callback
}

// GetMemoryHistory 获取内存历史记录
func (mld *MemoryLeakDetector) GetMemoryHistory() []MemorySnapshot {
	mld.mu.RLock()
	defer mld.mu.RUnlock()

	result := make([]MemorySnapshot, len(mld.memoryHistory))
	copy(result, mld.memoryHistory)
	return result
}

// GetGoroutineHistory 获取Goroutine历史记录
func (mld *MemoryLeakDetector) GetGoroutineHistory() []GoroutineSnapshot {
	mld.mu.RLock()
	defer mld.mu.RUnlock()

	result := make([]GoroutineSnapshot, len(mld.goroutineHistory))
	copy(result, mld.goroutineHistory)
	return result
}

// ForceGC 强制执行垃圾回收
func (mld *MemoryLeakDetector) ForceGC() {
	log.Info("强制执行垃圾回收")
	runtime.GC()
	debug.FreeOSMemory()
}

// Stop 停止检测器
func (mld *MemoryLeakDetector) Stop() {
	if mld.cancel != nil {
		mld.cancel()
	}
}
