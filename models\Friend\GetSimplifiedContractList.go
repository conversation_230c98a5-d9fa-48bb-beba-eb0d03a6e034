package Friend

import (
	"fmt"
	"time"
	"wechatdll/Cilent/mm"
	"wechatdll/models"
	log "github.com/sirupsen/logrus"
)

// 简化联系人数据结构
type SimplifiedContactData struct {
	Wxid  string     `json:"wxid"`
	Total int        `json:"total"`
	List  [][]string `json:"list"` // [wxid, nickname]
}

// 简化联系人缓存数据结构
type SimplifiedContactCacheData struct {
	Wxid      string                `json:"wxid"`
	Data      SimplifiedContactData `json:"data"`
	CacheTime time.Time             `json:"cache_time"`
}



// 从详细联系人数据转换为简化格式
func convertToSimplifiedFormat(wxid string, detailData []interface{}) SimplifiedContactData {
	var contactList [][]string

	for _, item := range detailData {
		if contactMap, ok := item.(map[string]interface{}); ok {
			// 提取wxid和nickname
			contactWxid := ""
			nickname := ""

			// 尝试获取Wxid字段
			if wxidVal, exists := contactMap["Wxid"]; exists {
				if wxidStr, ok := wxidVal.(string); ok {
					contactWxid = wxidStr
				}
			}

			// 尝试获取昵称，优先使用RemarkName，然后是Nickname
			if remarkName, exists := contactMap["RemarkName"]; exists {
				if remarkStr, ok := remarkName.(string); ok && remarkStr != "" {
					nickname = remarkStr
				}
			}
			
			if nickname == "" {
				if nicknameVal, exists := contactMap["Nickname"]; exists {
					if nicknameStr, ok := nicknameVal.(string); ok {
						nickname = nicknameStr
					}
				}
			}

			// 如果没有昵称，使用wxid作为昵称
			if nickname == "" {
				nickname = contactWxid
			}

			// 如果有有效的wxid，添加到列表
			if contactWxid != "" {
				contactList = append(contactList, []string{contactWxid, nickname})
			}
		}
	}

	return SimplifiedContactData{
		Wxid:  wxid,
		Total: len(contactList),
		List:  contactList,
	}
}

// convertDetailToSimplifiedFormat 从详细联系人数据转换为简化格式
func convertDetailToSimplifiedFormat(wxid string, details []interface{}) SimplifiedContactData {
	var contactList [][]string

	for _, detail := range details {
		// 处理从JSON反序列化的数据（map[string]interface{}格式）
		if detailMap, ok := detail.(map[string]interface{}); ok {
			if contactListData, exists := detailMap["ContactList"]; exists {
				if contacts, ok := contactListData.([]interface{}); ok {
					for _, contactData := range contacts {
						if contactMap, ok := contactData.(map[string]interface{}); ok {
							var contactWxid, nickname string

							// 提取wxid
							if userNameData, exists := contactMap["UserName"]; exists {
								if userNameMap, ok := userNameData.(map[string]interface{}); ok {
									if wxidVal, exists := userNameMap["string"]; exists {
										if wxidStr, ok := wxidVal.(string); ok {
											contactWxid = wxidStr
										}
									}
								}
							}

							// 提取nickname
							if nickNameData, exists := contactMap["NickName"]; exists {
								if nickNameMap, ok := nickNameData.(map[string]interface{}); ok {
									if nicknameVal, exists := nickNameMap["string"]; exists {
										if nicknameStr, ok := nicknameVal.(string); ok {
											nickname = nicknameStr
										}
									}
								}
							}

							// 如果有有效的wxid，添加到列表
							if contactWxid != "" {
								contactList = append(contactList, []string{contactWxid, nickname})
							}
						}
					}
				}
			}
		} else if response, ok := detail.(mm.GetContactResponse); ok {
			// 处理原始的 mm.GetContactResponse 类型数据（向后兼容）
			for _, contact := range response.ContactList {
				var contactWxid, nickname string

				// 提取wxid
				if contact.UserName != nil && contact.UserName.String_ != nil {
					contactWxid = *contact.UserName.String_
				}

				// 提取nickname
				if contact.NickName != nil && contact.NickName.String_ != nil {
					nickname = *contact.NickName.String_
				}

				// 如果有有效的wxid，添加到列表
				if contactWxid != "" {
					contactList = append(contactList, []string{contactWxid, nickname})
				}
			}
		}
	}

	return SimplifiedContactData{
		Wxid:  wxid,
		Total: len(contactList),
		List:  contactList,
	}
}

// convertMapSliceToSimplifiedFormat 从map切片转换为简化格式
func convertMapSliceToSimplifiedFormat(wxid string, mapSlice []map[string]interface{}) SimplifiedContactData {
	var contactList [][]string

	for _, contact := range mapSlice {
		var contactWxid, nickname string

		// 提取wxid
		if wxidVal, exists := contact["wxid"]; exists {
			if wxidStr, ok := wxidVal.(string); ok {
				contactWxid = wxidStr
			}
		}

		// 提取nickname
		if nicknameVal, exists := contact["nickname"]; exists {
			if nicknameStr, ok := nicknameVal.(string); ok {
				nickname = nicknameStr
			}
		}

		// 如果有有效的wxid，添加到列表
		if contactWxid != "" {
			contactList = append(contactList, []string{contactWxid, nickname})
		}
	}

	return SimplifiedContactData{
		Wxid:  wxid,
		Total: len(contactList),
		List:  contactList,
	}
}

// GetSimplifiedContractList 获取简化格式的联系人列表
func GetSimplifiedContractList(Data GetContactListParams) models.ResponseResult {
	// 如果不是强制刷新，先检查共享的缓存文件
	if !Data.ForceRefresh {
		if cacheData, err := loadCacheFromFile(Data.Wxid); err == nil {
			// 直接从共享缓存转换为简化格式
			simplifiedData := convertDetailToSimplifiedFormat(Data.Wxid, cacheData.Details)

			log.WithFields(log.Fields{
				"wxid":         Data.Wxid,
				"cached_count": cacheData.TotalCount,
				"simplified_count": simplifiedData.Total,
				"cache_time":   cacheData.CacheTime.Format("2006-01-02 15:04:05"),
				"file_path":    getCacheFilePath(Data.Wxid),
			}).Info("📁 使用共享缓存文件的联系人信息，已转换为简化格式")

			return models.ResponseResult{
				Code:    0,
				Success: true,
				Message: fmt.Sprintf("成功（来自共享缓存，缓存时间：%s）", cacheData.CacheTime.Format("2006-01-02 15:04:05")),
				Data:    simplifiedData,
			}
		} else {
			log.WithFields(log.Fields{
				"wxid":  Data.Wxid,
				"error": err.Error(),
			}).Info("📁 共享缓存文件不存在或无效，将获取详细数据并转换")
		}
	} else {
		log.WithFields(log.Fields{
			"wxid": Data.Wxid,
		}).Info("🔄 强制刷新缓存，跳过缓存检查")
	}

	// 获取详细联系人数据（这会更新共享缓存）
	log.WithFields(log.Fields{
		"wxid": Data.Wxid,
	}).Info("📞 调用GetTotalContractList获取详细联系人数据")

	detailResult := GetTotalContractList(Data)
	if !detailResult.Success {
		return detailResult
	}

	// 从返回的数据转换为简化格式
	var simplifiedData SimplifiedContactData
	if cacheData, ok := detailResult.Data.(ContactCacheData); ok {
		// 处理 GetTotalContractList 返回的 ContactCacheData 格式
		simplifiedData = convertDetailToSimplifiedFormat(Data.Wxid, cacheData.Details)
	} else if mapSlice, ok := detailResult.Data.([]map[string]interface{}); ok {
		// 处理旧格式（向后兼容）
		simplifiedData = convertMapSliceToSimplifiedFormat(Data.Wxid, mapSlice)
	} else {
		log.WithFields(log.Fields{
			"wxid": Data.Wxid,
			"data_type": fmt.Sprintf("%T", detailResult.Data),
		}).Error("详细联系人数据格式不匹配")
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "详细联系人数据格式错误",
			Data:    nil,
		}
	}

	log.WithFields(log.Fields{
		"wxid":            Data.Wxid,
		"total_contacts":  simplifiedData.Total,
	}).Info("✅ 获取简化格式联系人列表完成")

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    simplifiedData,
	}
}
