package comm

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// NetworkOptimizer 网络优化器
type NetworkOptimizer struct {
	httpClient     *http.Client
	transport      *http.Transport
	connPool       *ConnectionPool
	circuitBreaker *CircuitBreaker
	retryPolicy    *RetryPolicy
}

// ConnectionPool 连接池
type ConnectionPool struct {
	mu          sync.RWMutex
	connections map[string]*pooledConnection
	maxIdle     int
	maxActive   int
	idleTimeout time.Duration
}

type pooledConnection struct {
	conn     net.Conn
	lastUsed time.Time
	inUse    bool
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	mu           sync.RWMutex
	state        CircuitState
	failures     int
	lastFailTime time.Time
	timeout      time.Duration
	maxFailures  int
}

type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// RetryPolicy 重试策略
type RetryPolicy struct {
	maxRetries    int
	baseDelay     time.Duration
	maxDelay      time.Duration
	backoffFactor float64
}

// NewNetworkOptimizer 创建网络优化器
func NewNetworkOptimizer() *NetworkOptimizer {
	// 创建优化的传输层
	transport := &http.Transport{
		// 连接池配置
		MaxIdleConns:        100,              // 最大空闲连接数
		MaxIdleConnsPerHost: 20,               // 每个主机最大空闲连接数
		MaxConnsPerHost:     50,               // 每个主机最大连接数
		IdleConnTimeout:     90 * time.Second, // 空闲连接超时

		// TCP配置
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second, // 连接超时
			KeepAlive: 30 * time.Second, // TCP Keep-Alive
		}).DialContext,

		// TLS配置
		TLSHandshakeTimeout: 10 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
			MinVersion:         tls.VersionTLS12,
		},

		// HTTP/2支持
		ForceAttemptHTTP2:      true,
		MaxResponseHeaderBytes: 4096,

		// 响应头超时
		ResponseHeaderTimeout: 30 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	// 创建HTTP客户端
	client := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second, // 总超时时间
	}

	// 创建连接池
	connPool := &ConnectionPool{
		connections: make(map[string]*pooledConnection),
		maxIdle:     50,
		maxActive:   200,
		idleTimeout: 5 * time.Minute,
	}

	// 创建熔断器
	circuitBreaker := &CircuitBreaker{
		state:       CircuitClosed,
		timeout:     30 * time.Second,
		maxFailures: 5,
	}

	// 创建重试策略
	retryPolicy := &RetryPolicy{
		maxRetries:    3,
		baseDelay:     100 * time.Millisecond,
		maxDelay:      5 * time.Second,
		backoffFactor: 2.0,
	}

	optimizer := &NetworkOptimizer{
		httpClient:     client,
		transport:      transport,
		connPool:       connPool,
		circuitBreaker: circuitBreaker,
		retryPolicy:    retryPolicy,
	}

	// 启动连接池清理
	go optimizer.startConnectionPoolCleanup()

	return optimizer
}

// DoRequest 执行HTTP请求（带重试和熔断）
func (no *NetworkOptimizer) DoRequest(req *http.Request) (*http.Response, error) {
	// 检查熔断器状态
	if !no.circuitBreaker.Allow() {
		return nil, fmt.Errorf("circuit breaker is open")
	}

	var lastErr error

	// 重试逻辑
	for attempt := 0; attempt <= no.retryPolicy.maxRetries; attempt++ {
		if attempt > 0 {
			// 计算退避延迟
			delay := no.calculateBackoffDelay(attempt)
			time.Sleep(delay)
			log.Debugf("重试请求，第%d次尝试", attempt)
		}

		// 执行请求
		resp, err := no.httpClient.Do(req)
		if err == nil {
			// 请求成功，重置熔断器
			no.circuitBreaker.OnSuccess()
			return resp, nil
		}

		lastErr = err

		// 检查是否应该重试
		if !no.shouldRetry(err, attempt) {
			break
		}
	}

	// 所有重试都失败，记录失败
	no.circuitBreaker.OnFailure()
	return nil, fmt.Errorf("request failed after %d attempts: %w", no.retryPolicy.maxRetries+1, lastErr)
}

// calculateBackoffDelay 计算退避延迟
func (no *NetworkOptimizer) calculateBackoffDelay(attempt int) time.Duration {
	delay := time.Duration(float64(no.retryPolicy.baseDelay) *
		pow(no.retryPolicy.backoffFactor, float64(attempt-1)))

	if delay > no.retryPolicy.maxDelay {
		delay = no.retryPolicy.maxDelay
	}

	return delay
}

// pow 简单的幂运算
func pow(base, exp float64) float64 {
	result := 1.0
	for i := 0; i < int(exp); i++ {
		result *= base
	}
	return result
}

// shouldRetry 判断是否应该重试
func (no *NetworkOptimizer) shouldRetry(err error, attempt int) bool {
	if attempt >= no.retryPolicy.maxRetries {
		return false
	}

	// 检查错误类型，某些错误不应该重试
	if netErr, ok := err.(net.Error); ok {
		return netErr.Timeout() || netErr.Temporary()
	}

	return true
}

// Allow 检查熔断器是否允许请求
func (cb *CircuitBreaker) Allow() bool {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	switch cb.state {
	case CircuitClosed:
		return true
	case CircuitOpen:
		// 检查是否可以进入半开状态
		if time.Since(cb.lastFailTime) > cb.timeout {
			cb.mu.RUnlock()
			cb.mu.Lock()
			cb.state = CircuitHalfOpen
			cb.mu.Unlock()
			cb.mu.RLock()
			return true
		}
		return false
	case CircuitHalfOpen:
		return true
	default:
		return false
	}
}

// OnSuccess 记录成功
func (cb *CircuitBreaker) OnSuccess() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.failures = 0
	cb.state = CircuitClosed
}

// OnFailure 记录失败
func (cb *CircuitBreaker) OnFailure() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.failures++
	cb.lastFailTime = time.Now()

	if cb.failures >= cb.maxFailures {
		cb.state = CircuitOpen
		log.Warnf("熔断器开启，失败次数: %d", cb.failures)
	}
}

// GetConnection 从连接池获取连接
func (cp *ConnectionPool) GetConnection(address string) (net.Conn, error) {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	// 查找可用连接
	if pooledConn, exists := cp.connections[address]; exists {
		if !pooledConn.inUse && time.Since(pooledConn.lastUsed) < cp.idleTimeout {
			pooledConn.inUse = true
			pooledConn.lastUsed = time.Now()
			return pooledConn.conn, nil
		}
		// 连接已过期，删除
		delete(cp.connections, address)
		if pooledConn.conn != nil {
			pooledConn.conn.Close()
		}
	}

	// 创建新连接
	conn, err := net.DialTimeout("tcp", address, 10*time.Second)
	if err != nil {
		return nil, err
	}

	// 添加到连接池
	cp.connections[address] = &pooledConnection{
		conn:     conn,
		lastUsed: time.Now(),
		inUse:    true,
	}

	return conn, nil
}

// ReturnConnection 归还连接到连接池
func (cp *ConnectionPool) ReturnConnection(address string, conn net.Conn) {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	if pooledConn, exists := cp.connections[address]; exists && pooledConn.conn == conn {
		pooledConn.inUse = false
		pooledConn.lastUsed = time.Now()
	}
}

// startConnectionPoolCleanup 启动连接池清理
func (no *NetworkOptimizer) startConnectionPoolCleanup() {
	ticker := time.NewTicker(time.Minute * 5) // 每5分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		no.cleanupConnectionPool()
	}
}

// cleanupConnectionPool 清理连接池
func (no *NetworkOptimizer) cleanupConnectionPool() {
	no.connPool.mu.Lock()
	defer no.connPool.mu.Unlock()

	now := time.Now()
	for address, pooledConn := range no.connPool.connections {
		if !pooledConn.inUse && now.Sub(pooledConn.lastUsed) > no.connPool.idleTimeout {
			delete(no.connPool.connections, address)
			if pooledConn.conn != nil {
				pooledConn.conn.Close()
			}
		}
	}
}

// GetStats 获取网络优化器统计信息
func (no *NetworkOptimizer) GetStats() map[string]interface{} {
	no.connPool.mu.RLock()
	defer no.connPool.mu.RUnlock()

	no.circuitBreaker.mu.RLock()
	defer no.circuitBreaker.mu.RUnlock()

	activeConns := 0
	idleConns := 0

	for _, conn := range no.connPool.connections {
		if conn.inUse {
			activeConns++
		} else {
			idleConns++
		}
	}

	return map[string]interface{}{
		"connection_pool": map[string]interface{}{
			"active_connections": activeConns,
			"idle_connections":   idleConns,
			"total_connections":  len(no.connPool.connections),
		},
		"circuit_breaker": map[string]interface{}{
			"state":    no.circuitBreaker.state,
			"failures": no.circuitBreaker.failures,
		},
		"retry_policy": map[string]interface{}{
			"max_retries":    no.retryPolicy.maxRetries,
			"base_delay":     no.retryPolicy.baseDelay,
			"max_delay":      no.retryPolicy.maxDelay,
			"backoff_factor": no.retryPolicy.backoffFactor,
		},
	}
}

// RequestWithContext 带上下文的请求
func (no *NetworkOptimizer) RequestWithContext(ctx context.Context, method, url string, body interface{}) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return nil, err
	}

	// 设置通用头部
	req.Header.Set("User-Agent", "WeChatAPI/1.0")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// 如果有body，序列化
	if body != nil {
		jsonData, err := GetJSONOptimizer().Marshal(body)
		if err != nil {
			return nil, err
		}
		req.Body = &readCloser{data: jsonData}
		req.ContentLength = int64(len(jsonData))
	}

	return no.DoRequest(req)
}

// readCloser 实现io.ReadCloser接口
type readCloser struct {
	data []byte
	pos  int
}

func (rc *readCloser) Read(p []byte) (n int, err error) {
	if rc.pos >= len(rc.data) {
		return 0, fmt.Errorf("EOF")
	}

	n = copy(p, rc.data[rc.pos:])
	rc.pos += n
	return n, nil
}

func (rc *readCloser) Close() error {
	return nil
}

// 全局网络优化器实例
var (
	globalNetworkOptimizer *NetworkOptimizer
	networkOptimizerOnce   sync.Once
)

// GetNetworkOptimizer 获取全局网络优化器
func GetNetworkOptimizer() *NetworkOptimizer {
	networkOptimizerOnce.Do(func() {
		globalNetworkOptimizer = NewNetworkOptimizer()
	})
	return globalNetworkOptimizer
}

// OptimizedHTTPClient 获取优化的HTTP客户端
func OptimizedHTTPClient() *http.Client {
	return GetNetworkOptimizer().httpClient
}
