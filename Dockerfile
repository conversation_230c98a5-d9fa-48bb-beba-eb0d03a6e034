# 多阶段构建 - 构建阶段
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的构建工具（包含CGO支持）
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    gcc \
    musl-dev \
    libc-dev

# 设置 Go 环境（启用CGO支持）
ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=1
ENV GOOS=linux
ENV GOARCH=amd64

# 复制 go mod 文件并下载依赖
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码和依赖库
COPY . .

# 构建应用（保留CGO支持）
RUN go build -ldflags="-w -s" -o wxapi .

# 运行阶段
FROM alpine:latest

# 安装必要的运行时依赖
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    libc6-compat \
    wget

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件和必要文件
COPY --from=builder /app/wxapi .
COPY --from=builder /app/conf ./conf
COPY --from=builder /app/lib ./lib

# 创建日志目录
RUN mkdir -p logs

# 设置文件权限
RUN chown -R appuser:appgroup /app && \
    chmod +x wxapi && \
    chmod -R 755 lib

# 切换到非root用户
USER appuser

# 暴露端口（根据app.conf配置）
EXPOSE 8059 8088

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8059/ || exit 1

# 设置环境变量
ENV DEPLOY_MODE=docker

# 启动应用
ENTRYPOINT ["./wxapi"]