package Login

import (
	"crypto/md5"
	"fmt"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/Mmtls"
	"wechatdll/baseinfo"
	"wechatdll/clientsdk/baseutils"
	"wechatdll/clientsdk/extinfo"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
)

func Data62(Data Data62LoginReq, domain string) models.ResponseResult {
	// 获取username为key的缓存
	D, _ := comm.GetLoginata(Data.UserName, nil)
	reqDataLogin := DataLogin{
		UserName:   Data.UserName,
		Password:   Data.Password,
		Data62:     Data.Data62,
		DeviceName: Data.DeviceName,
		DeviceId:   "",
		Proxy:      Data.Proxy,
	}
	// D, _ := comm.GetLoginataByDevId()
	if D == nil || D.Wxid == "" {
		// 没有缓存, 初始化新的账号环境
		D = GeniPhoneLoginData(reqDataLogin)
	} else {
		D = UpdateiPhoneLoginData(D, reqDataLogin)
	}
	if domain == "" {
		domain = D.ShortHost
	}
	// 更新ShortHost
	if D.ShortHost == "" {
		D.ShortHost = domain
	} else {
		domain = D.ShortHost
	}

	//初始化Mmtls
	var httpclient *Mmtls.HttpClientModel
	var err error
	if D.MmtlsKey == nil {
		httpclient, D.MmtlsKey, err = comm.MmtlsInitialize(Data.Proxy, domain)
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
				Data:    nil,
			}
		}
	} else {
		httpclient = Mmtls.GenNewHttpClient(D.MmtlsKey, domain)
	}
	// 请求设备device_token
	if D.DeviceToken == nil || D.DeviceToken.TrustResponseData == nil || D.DeviceToken.TrustResponseData.DeviceToken == nil || *D.DeviceToken.TrustResponseData.DeviceToken == "" {
		FpInitAndRrefresh(D, httpclient)
		if err != nil {
			// 请求失败则放空结构
			D.DeviceToken = &mm.TrustResponse{}
		}
	}
	prikey, pubkey := Algorithm.GetEcdh713Key()

	Wcstf, _ := Algorithm.GetWcstf08(D.Wxid)
	Wcste, _ := Algorithm.GetWcste08()
	fmt.Println("62登录(账号或密码)10")
	accountRequest := &mm.ManualAuthRsaReqData{
		RandomEncryKey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(D.Aeskey))),
			Buffer: D.Aeskey,
		},
		CliPubEcdhkey: &mm.ECDHKey{
			Nid: proto.Int32(713),
			Key: &mm.SKBuiltinBufferT{
				ILen:   proto.Uint32(uint32(len(pubkey))),
				Buffer: pubkey,
			},
		},
		UserName: proto.String(Data.UserName),
		Pwd:      proto.String(baseutils.MD5ToLower(Data.Password)),
	}
	fmt.Println("62登录(账号或密码)11")
	ccData := &mm.CryptoData{
		Version:     []byte("********"),
		Type:        proto.Uint32(1),
		EncryptData: extinfo.GetNewSpamDataV8(D),
		Timestamp:   proto.Uint32(uint32(time.Now().Unix())),
		Unknown5:    proto.Uint32(5),
		Unknown6:    proto.Uint32(0),
	}
	ccDataseq, _ := proto.Marshal(ccData)

	DeviceTokenCCD := &mm.DeviceToken{
		Version:   proto.String(""),
		Encrypted: proto.Uint32(1),
		Data: &mm.SKBuiltinStringT{
			String_: proto.String(D.DeviceToken.GetTrustResponseData().GetDeviceToken()),
		},
		TimeStamp: proto.Uint32(uint32(time.Now().Unix())),
		Optype:    proto.Uint32(2),
		Uin:       proto.Uint32(0),
	}
	DeviceTokenCCDPB, _ := proto.Marshal(DeviceTokenCCD)
	WCExtInfo := &mm.WCExtInfo{
		Wcstf: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcstf))),
			Buffer: Wcstf,
		},
		Wcste: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcste))),
			Buffer: Wcste,
		},
		CcData: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(ccDataseq))),
			Buffer: ccDataseq,
		},
		DeviceToken: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(DeviceTokenCCDPB))),
			Buffer: DeviceTokenCCDPB,
		},
	}
	WCExtInfoseq, _ := proto.Marshal(WCExtInfo)

	ClientSeqId := fmt.Sprintf("%v_%v", D.Deviceid_str, time.Now().Unix())
	uuid1, _ := baseinfo.IOSUuid(D.Deviceid_str)

	deviceRequest := &mm.ManualAuthAesReqData{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(0),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(1),
		},
		BaseReqInfo:  &mm.BaseAuthReqInfo{},
		Imei:         &D.Imei,
		SoftType:     &D.SoftType,
		BuiltinIpseq: proto.Uint32(0),
		ClientSeqId:  &ClientSeqId,
		DeviceName:   proto.String(Data.DeviceName),
		DeviceType:   proto.String(D.DeviceType),
		Language:     proto.String("zh_CN"),
		TimeZone:     proto.String("8.0"),
		Channel:      proto.Int(0),
		TimeStamp:    proto.Uint32(uint32(time.Now().Unix())),
		DeviceBrand:  proto.String("Apple"),
		RealCountry:  proto.String("CN"),
		BundleId:     proto.String("com.tencent.xin"),
		AdSource:     &uuid1,
		IphoneVer:    &D.RomModel,
		InputType:    proto.Uint32(2),
		ExtSpamInfo: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(WCExtInfoseq))),
			Buffer: WCExtInfoseq,
		},
	}
	requset := &mm.SecManualLoginRequest{
		RsaReqData: accountRequest,
		AesReqData: deviceRequest,
	}

	reqdata, _ := proto.Marshal(requset)

	// 更新代理IP
	if Data.Proxy.ProxyIp != "" && Data.Proxy.ProxyIp != "String" {
		D.Proxy = Data.Proxy
	}
	// 更新代理IP
	if Data.Proxy.ProxyIp != "" && Data.Proxy.ProxyIp != "String" {
		D.Proxy = Data.Proxy
	}
	// 更新代理IP
	if Data.Proxy.ProxyIp != "" && Data.Proxy.ProxyIp != "String" {
		D.Proxy = Data.Proxy
	}
	// 临时缓存上下文, 以便被异常中断后再次保持上下文重试
	err = comm.CreateLoginData(D, D.Wxid, 7200, nil)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}
	hec := InitHec(D)
	hecData := hec.HybridEcdhPackIosEn(252, 0, nil, reqdata)
	// 遇到mmtls失败, 则重新握手
	retrys := 2
	doRetry := true
	var recvData []byte
	for retrys > 0 && doRetry == true {
		doRetry = false
		retrys--
		recvData, err = httpclient.MMtlsPost(domain, "/cgi-bin/micromsg-bin/secmanualauth", hecData, Data.Proxy)
		if err != nil && strings.Contains(err.Error(), "MMTLS") {
			// mmtls异常, 重新握手
			httpclient, D.MmtlsKey, err = comm.MmtlsInitialize(Data.Proxy, domain)
			if err != nil {
				return models.ResponseResult{
					Code:    -8,
					Success: false,
					Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
					Data:    nil,
				}
			}
			// 重新提交
			doRetry = true
		}
	}
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}
	if len(recvData) <= 31 {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("组包异常, 返回31字节"),
			Data:    nil,
		}
	}

	ph1 := hec.HybridEcdhPackIosUn(recvData)
	loginRes := mm.UnifyAuthResponse{}
	err = proto.Unmarshal(ph1.Data, &loginRes)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	if loginRes.GetBaseResponse().GetRet() == 0 && loginRes.GetUnifyAuthSectFlag() > 0 {
		D.Cooike = ph1.Cookies
		D.ShortHost = domain

		Wx_loginecdhkey := Algorithm.DoECDH713Key(prikey, loginRes.GetAuthSectResp().GetSvrPubEcdhkey().GetKey().GetBuffer())
		Wx_loginecdhkeylen := int32(len(Wx_loginecdhkey))
		m := md5.New()
		m.Write(Wx_loginecdhkey[:Wx_loginecdhkeylen])
		ecdhdecrptkey := m.Sum(nil)
		// TODO(kedaya): 用于计算组包的checkSum, 使用什么做盐? - 已确定
		// 根据代码分析，checkSum计算使用ECDH密钥作为盐值：
		// 1. CalcHeadCheckSum函数使用checkSumKey（即Loginecdhkey）作为盐值
		// 2. GenSignature函数同样使用salt参数（即Loginecdhkey）进行MD5计算
		// 3. 这确保了每个会话的checkSum都是唯一的，提高了安全性
		D.Loginecdhkey = Wx_loginecdhkey
		D.Uin = loginRes.GetAuthSectResp().GetUin()
		D.Wxid = loginRes.GetAcctSectResp().GetUserName()
		D.Alais = loginRes.GetAcctSectResp().GetAlias()
		D.Mobile = loginRes.GetAcctSectResp().GetBindMobile()
		D.NickName = loginRes.GetAcctSectResp().GetNickName()
		D.Email = loginRes.GetAcctSectResp().GetBindEmail()
		D.Sessionkey = Algorithm.AesDecrypt(loginRes.GetAuthSectResp().GetSessionKey().GetBuffer(), ecdhdecrptkey)
		D.Sessionkey_2 = loginRes.GetAuthSectResp().GetSessionKey().GetBuffer()
		D.Autoauthkey = loginRes.GetAuthSectResp().GetAutoAuthKey().GetBuffer()
		D.Autoauthkeylen = int32(loginRes.GetAuthSectResp().GetAutoAuthKey().GetILen())
		D.Serversessionkey = loginRes.GetAuthSectResp().GetServerSessionKey().GetBuffer()
		D.Clientsessionkey = loginRes.GetAuthSectResp().GetClientSessionKey().GetBuffer()
		D.ShortHost = comm.Rmu0000(*loginRes.NetworkSectResp.BuiltinIplist.ShortConnectIplist[0].Host)
		D.LongHost = comm.Rmu0000(*loginRes.NetworkSectResp.BuiltinIplist.LongConnectIplist[0].Host)
		D.RsaPublicKey = pubkey
		D.RsaPrivateKey = prikey
		// 更新代理IP
		if Data.Proxy.ProxyIp != "" && Data.Proxy.ProxyIp != "String" {
			D.Proxy = Data.Proxy
		}
		err := comm.CreateLoginData(D, D.Wxid, 0, nil)

		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		// 登录成功后自动启动心跳
		go comm.AutoStartHeartBeat(D.Wxid)

		return models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "成功",
			Data:    loginRes,
			Data62:  baseutils.Get62Data(D.Deviceid_str),
		}
	}

	//30系列转向
	if loginRes.GetBaseResponse().GetRet() == -301 {
		D.ShortHost = comm.Rmu0000(*loginRes.NetworkSectResp.BuiltinIplist.ShortConnectIplist[0].Host)
		D.LongHost = comm.Rmu0000(*loginRes.NetworkSectResp.BuiltinIplist.LongConnectIplist[0].Host)
		D.MmtlsKey = nil
		// 更新代理IP
		if Data.Proxy.ProxyIp != "" && Data.Proxy.ProxyIp != "String" {
			D.Proxy = Data.Proxy
		}
		err := comm.CreateLoginData(D, D.Wxid, 7200, nil)
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		return Data62(Data, D.ShortHost)
	}
	/*

		// 自动过滑块
		if strings.Index(loginRes.GetBaseResponse().GetErrMsg().GetString_(), "环境存在异常") >= 0 || strings.Contains(loginRes.GetBaseResponse().GetErrMsg().GetString_(), "The system has detected an abnormal login environment.") {
			// 过滑块并根据结果判断
			if err := LoginOCR(loginRes.GetBaseResponse().GetErrMsg().GetString_()); err == nil {
				// 滑块成功, 再次登录
				return Data62(Data, domain)
			} else {
				// 返回异常
				return models.ResponseResult{
					Code:    -8,
					Success: false,
					Message: fmt.Sprintf("系统异常：%v", err.Error()),
					Data:    nil,
				}
			}
		}
	*/

	return models.ResponseResult{
		Code:    -8,
		Success: false,
		Message: "失败",
		Data:    loginRes,
	}
}
