//go:build linux
// +build linux

package TcpPoll

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"net"
	"net/url"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/clientsdk/baseutils"

	"wechatdll/Mmtls"
	"wechatdll/comm"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
	"golang.org/x/net/proxy"
)

type BusinessFunc = func(cmdId int) error

type TcpClient struct {
	establishCallback *func()              // 收到数据的回调事件
	model             *comm.LoginData      // wx缓存
	callbackMsg       BusinessFunc         // 收到消息的回调事件
	receivedBytes     []byte               // 接收数据缓存区
	pskData           [][]byte             // 握手队列
	handshaking       int32                // 是否握手中 (使用原子操作: 0=完成, 1=握手中)
	mmtlsPrivateKey1  []byte               // 1次握手密钥
	mmtlsPublicKey1   []byte               // 1次握手公钥
	mmtlsPrivateKey2  []byte               // 1次握手密钥
	mmtlsPublicKey2   []byte               // 1次握手公钥
	handshakeHash     []byte               // 握手hash值
	longLinkEncodeIv  []byte               // 握手加密iv
	longLinkEncodeKey []byte               // 握手加密key
	longLinkDecodeIv  []byte               // 握手解密iv
	longLinkDecodeKey []byte               // 握手解密key
	lastHeartbeatTime int64                // 最后一次心跳时间 (使用原子操作)
	shareKey          []byte               // 协商密钥
	serverSequence    int32                // 服务端发包序列 (使用原子操作)
	clientSequence    int32                // 客户端发包序列 (使用原子操作)
	mmtlsSequence     int32                // mmtls组包序列 (使用原子操作)
	pskKey            []byte               // psk密钥
	queue             map[int]func([]byte) // 回调序列
	queueMutex        sync.RWMutex         // 保护回调队列的锁
	messageCache      []byte               // 消息缓存
	cacheMutex        sync.Mutex           // 保护消息缓存的锁
	conn              net.Conn             // 长连接
	connMutex         sync.RWMutex         // 保护连接的锁
	startTime         int64                // tcp 开启时间，如果时间对不上则停止 tcp 心跳
	ctx               context.Context      // 用于优雅关闭
	cancel            context.CancelFunc   // 取消函数
	closed            int32                // 连接是否已关闭 (使用原子操作: 0=开启, 1=关闭)
}

var CORRECT_HEADER = []byte{0x00, 0xf1, 0x03}

func NewTcpClient(model *comm.LoginData) *TcpClient {
	if model == nil {
		log.Error("创建TcpClient时model不能为空")
		return nil
	}

	ctx, cancel := context.WithCancel(context.Background())

	instance := TcpClient{
		model:     model,
		startTime: time.Now().UnixNano(),
		ctx:       ctx,
		cancel:    cancel,
		queue:     make(map[int]func([]byte)),
	}

	// 使用原子操作设置初始状态
	atomic.StoreInt32(&instance.handshaking, 1) // 新建的连接先需要握手
	atomic.StoreInt32(&instance.serverSequence, 1)
	atomic.StoreInt32(&instance.clientSequence, 1)
	atomic.StoreInt32(&instance.mmtlsSequence, 1)
	atomic.StoreInt64(&instance.lastHeartbeatTime, time.Now().Unix())

	// 生成密钥对
	instance.mmtlsPrivateKey1, instance.mmtlsPublicKey1 = Algorithm.GetECDH415Key()
	instance.mmtlsPrivateKey2, instance.mmtlsPublicKey2 = Algorithm.GetECDH415Key()

	log.Debugf("创建新的TcpClient: wxid=%s", model.Wxid)
	return &instance
}

// 创建长连接. remoteAddr string: 目标地址, proxyAddr string: 代理地址, proxyUser string: 代理用户, proxyPassword string: 代理密码
func CreateConnection(remoteAddr string, proxyAddr string, proxyUser string, proxyPassword string) (net.Conn, error) {
	if remoteAddr == "" {
		return nil, errors.New("远程地址不能为空")
	}

	// 使用配置管理器获取连接超时
	timeout := GetConnectionTimeout()

	// 判断proxy是否需要验证密码
	var proxyAuth *proxy.Auth = nil
	if proxyUser != "" {
		proxyAuth = &proxy.Auth{
			User:     proxyUser,
			Password: proxyPassword,
		}
	}

	var conn net.Conn
	var connErr error

	if proxyAddr != "" {
		// 创建proxy连接到远程服务器
		log.Debugf("通过代理连接: proxy=%s, target=%s", proxyAddr, remoteAddr)
		dialer, dialErr := proxy.SOCKS5("tcp", proxyAddr, proxyAuth, proxy.Direct)
		if dialErr != nil {
			return nil, fmt.Errorf("创建代理连接失败: %w", dialErr)
		}

		// 添加端口号（如果没有的话）
		targetAddr := remoteAddr
		if !strings.Contains(remoteAddr, ":") {
			targetAddr = remoteAddr + ":80"
		}

		conn, connErr = dialer.Dial("tcp", targetAddr)
		if connErr != nil {
			return nil, fmt.Errorf("通过代理连接失败: %w", connErr)
		}
	} else {
		// 直接连接远程服务器
		log.Debugf("直接连接: target=%s", remoteAddr)

		// 添加端口号（如果没有的话）
		targetAddr := remoteAddr
		if !strings.Contains(remoteAddr, ":") {
			targetAddr = remoteAddr + ":80"
		}

		conn, connErr = net.DialTimeout("tcp", targetAddr, timeout)
		if connErr != nil {
			return nil, fmt.Errorf("直接连接失败: %w", connErr)
		}
	}

	// 设置连接选项
	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetKeepAlive(true)
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetNoDelay(true)
	}

	log.Debugf("连接建立成功: %s", remoteAddr)
	return conn, nil
}

// 根据wx缓存创建并连接到长连接
func (client *TcpClient) Connect() error {
	if client.model == nil {
		return errors.New("model 不能为空")
	}

	// 检查是否已经关闭
	if atomic.LoadInt32(&client.closed) == 1 {
		return errors.New("客户端已关闭")
	}

	var connErr error

	// 使用写锁保护连接操作
	client.connMutex.Lock()
	defer client.connMutex.Unlock()

	client.conn, connErr = CreateConnection(
		client.model.LongHost,
		client.model.Proxy.ProxyIp,
		client.model.Proxy.ProxyUser,
		client.model.Proxy.ProxyPassword)

	if connErr != nil {
		log.Errorf("连接失败: %v", connErr)
		return fmt.Errorf("连接失败: %w", connErr)
	}

	// 构建并发送握手消息
	helloWrapper, helloBuff := client.BuildClientHello()
	if helloWrapper == nil || helloBuff == nil {
		client.conn.Close()
		client.conn = nil
		return errors.New("构建握手消息失败")
	}

	client.handshakeHash = append(client.handshakeHash, helloBuff...)
	client.Send(helloWrapper, "客户端握手开始")

	log.Debugf("连接建立成功，开始握手: wxid=%s", client.model.Wxid)
	return nil
}

// 接收数据, 回调至 ReceiveMessage 处理
func (client *TcpClient) Once() {
	// 检查连接是否已关闭
	if atomic.LoadInt32(&client.closed) == 1 {
		return
	}

	// 使用读锁保护连接
	client.connMutex.RLock()
	conn := client.conn
	client.connMutex.RUnlock()

	if conn == nil {
		log.Warn("连接为空，跳过读取")
		return
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	buf := make([]byte, GetMessageBufferSize()) // 使用配置的缓冲区大小
	n, err := conn.Read(buf)
	if err != nil {
		if err != io.EOF {
			log.Errorf("TcpClient读取数据错误: %v", err)
			// 如果是严重错误，标记连接为关闭状态
			if isConnectionError(err) {
				atomic.StoreInt32(&client.closed, 1)
			}
		}
		return
	}

	if n > 0 {
		// log.Debugf("TcpClient收到消息: 长度=%d", n)
		client.ReceiveMessage(buf[:n])
	}
}

// isConnectionError 判断是否为连接错误
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "network is unreachable")
}

// 识别并预处理消息字节
func (client *TcpClient) ReceiveMessage(buf []byte) {
	if len(buf) == 0 {
		return
	}

	// 使用锁保护消息缓存
	client.cacheMutex.Lock()
	defer client.cacheMutex.Unlock()

	client.receivedBytes = append(client.receivedBytes, buf...)

	// 处理缓存中的所有完整消息
	for len(client.receivedBytes) >= 5 {
		// 正常的消息报文头为5字节
		headBytes := client.receivedBytes[:5]

		// 校验报文头的合法性
		if headBytes[1] != CORRECT_HEADER[1] || headBytes[2] != CORRECT_HEADER[2] {
			log.Errorf("数据包头格式错误: [%x], 清空缓冲区", headBytes)
			// 清除缓冲区并退出循环
			client.receivedBytes = []byte{}
			break
		}

		// 消息头3-5为完整消息长度
		messageLen := binary.BigEndian.Uint16(headBytes[3:5])

		// 检查消息长度是否合理（防止异常大的消息）
		// uint16最大值是65535，所以这里检查是否超过合理范围
		if messageLen > 32768 { // 32KB限制，适合uint16范围
			log.Errorf("消息长度异常: %d, 清空缓冲区", messageLen)
			client.receivedBytes = []byte{}
			break
		}

		totalLen := int(messageLen) + 5
		if len(client.receivedBytes) < totalLen {
			// 数据包长度小于报文长度, 说明还没有接收完成, 退出循环继续接收消息
			break
		}

		// 取出该条消息
		messageBytes := make([]byte, totalLen)
		copy(messageBytes, client.receivedBytes[:totalLen])

		// 从缓存中移除该条消息
		client.receivedBytes = client.receivedBytes[totalLen:]

		// 异步处理消息，避免阻塞接收
		go func(msg []byte) {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("处理消息时发生panic: %v", r)
				}
			}()
			client.ProcessMessage(msg)
		}(messageBytes)
	}
}

// 处理接收到的消息, 包括mmtls解包和ecdh密钥交换
func (client *TcpClient) ProcessMessage(messageBytes []byte) {
	if len(messageBytes) == 0 {
		return
	}

	// 检查连接是否已关闭
	if atomic.LoadInt32(&client.closed) == 1 {
		return
	}

	// log.Debugf("TcpClient: ProcessMessage执行, 处理消息长度=%d", len(messageBytes))

	// 检查是否在握手阶段
	if atomic.LoadInt32(&client.handshaking) == 1 {
		client.processHandshakeMessage(messageBytes)
	} else {
		client.processApplicationMessage(messageBytes)
	}
}

// processHandshakeMessage 处理握手阶段的消息
func (client *TcpClient) processHandshakeMessage(messageBytes []byte) {
	// 握手阶段的消息处理
	if len(client.pskData) < 5 {
		// 握手交互小于5次, 将消息放入握手队列
		client.pskData = append(client.pskData, messageBytes)
	}

	if len(client.pskData) == 4 {
		// 前四次握手交互
		if err := client.processHandshakeSteps(); err != nil {
			log.Errorf("握手处理失败: %v", err)
			atomic.StoreInt32(&client.closed, 1)
			return
		}

		clientFinishPackage := client.ClientFinish(client.pskData[3])
		if clientFinishPackage == nil {
			log.Errorf("ClientFinish失败")
			atomic.StoreInt32(&client.closed, 1)
			return
		}

		client.Send(clientFinishPackage, "客户端握手结束")
		atomic.StoreInt32(&client.handshaking, 0) // 握手完成
		client.SetStartTime(time.Now().UnixNano())

		// 启动心跳
		go client.SendTcpHeartBeat()

		log.Debugf("握手完成: wxid=%s", client.model.Wxid)
	}
}

// processHandshakeSteps 处理握手步骤
func (client *TcpClient) processHandshakeSteps() error {
	if err := client.ProcessServerHello(client.pskData[0]); err != nil {
		return fmt.Errorf("第一次握手密钥协商错误: %w", err)
	}
	if err := client.GetCertificateVerify(client.pskData[1]); err != nil {
		return fmt.Errorf("第二次握手密钥协商错误: %w", err)
	}
	if err := client.BuildServerTicket(client.pskData[2]); err != nil {
		return fmt.Errorf("第三次握手密钥协商错误: %w", err)
	}
	if err := client.ServerFinish(client.pskData[3]); err != nil {
		return fmt.Errorf("第四次握手密钥协商错误: %w", err)
	}
	return nil
}

// processApplicationMessage 处理应用层消息
func (client *TcpClient) processApplicationMessage(messageBytes []byte) {
	if len(messageBytes) == 0 {
		return
	}

	switch messageBytes[0] {
	case 0x17:
		// mmtls解包并回调
		message := client.UnpackMmtlsLong(messageBytes)
		if message != nil {
			client.HandleMessage(message)
		} else {
			log.Warn("mmtls解包失败")
		}
	case 0x15:
		log.Infof("收到终止长连接消息: wxid=%s", client.model.Wxid)
		atomic.StoreInt32(&client.closed, 1)
		client.Terminate()
	default:
		log.Warnf("未知消息类型: 0x%02x", messageBytes[0])
	}
}

// ----------------------------- mmtls握手内容 --------------------------------------
// 发起握手组包
func (client *TcpClient) BuildClientHello() ([]byte, []byte) {
	var helloBuff = new(bytes.Buffer)
	// part_1: 固定头, 10 - 0 = 10位
	helloBuff.Write([]byte{0x0, 0x0, 0x0, 0xd0, 0x1, 0x3, 0xf1, 0x1, 0xc0, 0x2b})
	// part_2: 随机32位bytes, 42 - 10 = 32位
	helloBuff.Write([]byte(baseutils.RandSeq(32)))
	// part_3: 时间戳, 46 - 42 = 4位
	time := time.Now().Unix()
	binary.Write(helloBuff, binary.BigEndian, (int32)(time))
	// part_4: MMTLS握手协议字段, 20字节, TODO(kedaya): 搞明白这里怎么模拟 - 已解决
	// 根据InitMmtlsShake.go的实现，这是MMTLS握手协议的固定字段：
	// 前12字节：协议版本和握手参数 {0x00, 0x00, 0x00, 0xa2, 0x01, 0x00, 0x00, 0x00, 0x9d, 0x00, 0x10, 0x02}
	// 后8字节：第一个公钥的长度和序号标识 {0x00, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x01}
	// 其中0x47(71)是公钥长度，0x01是第一个公钥的序号
	helloBuff.Write([]byte{0x00, 0x00, 0x00, 0xA2, 0x01, 0x00, 0x00, 0x00, 0x9D, 0x00, 0x10, 0x02})
	// part_4.5: 第一个公钥长度和序号, 8字节
	binary.Write(helloBuff, binary.BigEndian, (int32)(len(client.mmtlsPublicKey1)+6)) // 公钥长度+6
	binary.Write(helloBuff, binary.BigEndian, (int32)(1))                             // 公钥序号1
	// part_5: 第一个公钥长度, 2字节
	binary.Write(helloBuff, binary.BigEndian, (int16)(len(client.mmtlsPublicKey1)))
	// part_6: 第一个公钥值, 65字节
	helloBuff.Write(client.mmtlsPublicKey1)
	// part_7: 第二个公钥长度和序号, 8字节
	binary.Write(helloBuff, binary.BigEndian, (int32)(len(client.mmtlsPublicKey2)+6))
	binary.Write(helloBuff, binary.BigEndian, (int32)(2)) // 公钥序号2
	// part_8: 第二个公钥长度, 2字节
	binary.Write(helloBuff, binary.BigEndian, (int16)(len(client.mmtlsPublicKey2)))
	// part_9: 第二个公钥值, 65字节
	helloBuff.Write(client.mmtlsPublicKey2)
	// part_10: 尾部标识, 4字节
	binary.Write(helloBuff, binary.BigEndian, (int32)(1))
	// 外层包装, 215 - 212 = 3位
	var helloWrapper = new(bytes.Buffer)
	helloWrapper.Write([]byte{0x16, 0xf1, 0x03})
	binary.Write(helloWrapper, binary.BigEndian, (int16)(len(helloBuff.Bytes())))
	helloWrapper.Write(helloBuff.Bytes())
	return helloWrapper.Bytes(), helloBuff.Bytes()
}

// mmtls握手第一个收到的消息是ServerHello
func (client *TcpClient) ProcessServerHello(message []byte) error {
	keyPairSequence := binary.BigEndian.Uint32(message[57:61]) // 确定是使用密钥对1还是密钥对2
	serverPublicKey := message[63:128]                         // 从63位置取出65位服务端公钥
	privateKey := client.mmtlsPrivateKey1
	if keyPairSequence == 2 {
		privateKey = client.mmtlsPrivateKey2
	}
	shareKey := Algorithm.DoECDH415Key(privateKey, serverPublicKey)
	if shareKey == nil {
		return errors.New("Mmtls: 秘钥交互失败")
	} else if len(shareKey) != 32 {
		return errors.New("Mmtls: 交互秘钥长度存在异常")
	}
	client.shareKey = Mmtls.Getsha256(shareKey) // 协商再hash得到共享密钥
	client.handshakeHash = append(client.handshakeHash, message[5:]...)

	var infoBytes = new(bytes.Buffer)
	infoBytes.Write(Mmtls.Utf8ToBytes("handshake key expansion"))
	dataHash := Mmtls.Getsha256(client.handshakeHash)
	infoBytes.Write(dataHash)
	expandKey := Algorithm.Hkdf_Expand(sha256.New, client.shareKey, infoBytes.Bytes(), 56)
	//fmt.Println(len(hkdfexpandkey))
	client.longLinkEncodeKey = expandKey[:16]
	client.longLinkDecodeKey = expandKey[16:32]
	client.longLinkEncodeIv = expandKey[32:44]
	client.longLinkDecodeIv = expandKey[44:]
	return nil
}

// 使用IV和key解码第二次握手内容, 把结果放入hand_shake_hash
func (client *TcpClient) GetCertificateVerify(message []byte) error {
	pskLen := len(message)
	// 去除开头的5字节,包括后面16字节的tag
	dataSection := message[5:pskLen]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, message[0:5]...)
	var iv []byte
	var newSeq int
	iv, newSeq = Mmtls.GetDecryptIv(client.longLinkDecodeIv, int(client.serverSequence))
	client.serverSequence = int32(newSeq)
	serverSecret := Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, dataSection, iv, aad)
	if serverSecret == nil {
		err := errors.New("第二次握手密钥协商错误")
		return err
	}
	client.handshakeHash = append(client.handshakeHash, serverSecret...)
	return nil
}

// 解密第三次握手内容
func (client *TcpClient) BuildServerTicket(message []byte) error {
	pskLen := len(message)
	// 去除开头的5字节和后面16字节的tag
	dataSection := message[5:pskLen]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, message[0:5]...)
	var iv []byte
	var newSeq int
	iv, newSeq = Mmtls.GetDecryptIv(client.longLinkDecodeIv, int(client.serverSequence))
	client.serverSequence = int32(newSeq)
	client.pskKey = Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, dataSection, iv, aad)
	if client.pskKey == nil {
		err := errors.New("第三次握手密钥协商错误")
		return err
	}
	// client.build_short_link_ticket() 这里不需要短连接ticket了,短连接可自己握手
	client.handshakeHash = append(client.handshakeHash, client.pskKey...)
	return nil
}

// 解密第四次握手内容
func (client *TcpClient) ServerFinish(message []byte) error {
	pskLen := len(message)
	// 去除开头的5字节和后面16字节的tag
	dataSection := message[5:pskLen]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, message[0:5]...)
	var iv []byte
	var newSeq int
	iv, newSeq = Mmtls.GetDecryptIv(client.longLinkDecodeIv, int(client.serverSequence))
	client.serverSequence = int32(newSeq)
	serverFinishData := Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, dataSection, iv, aad)
	if serverFinishData == nil {
		err := errors.New("第四次握手密钥协商错误")
		return err
	}
	return nil
}

// mmtls握手完成组包
func (client *TcpClient) ClientFinish(message []byte) []byte {
	var infoBytes = new(bytes.Buffer)
	infoBytes.Write(Mmtls.Utf8ToBytes("client finished"))
	dataHash := Mmtls.Getsha256(client.handshakeHash)
	// infoBytes.Write(dataHash)
	clientFinishData := Algorithm.Hkdf_Expand(sha256.New, client.shareKey, infoBytes.Bytes(), 32)
	hmacHash := hmac.New(sha256.New, clientFinishData)
	hmacHash.Write(dataHash)
	hmacRet := hmacHash.Sum(nil)
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.clientSequence))
	aad = append(aad, message[0:5]...)
	sendData := []byte{0x00, 0x00, 0x00, 0x23, 0x14, 0x00, 0x20}
	sendData = append(sendData, hmacRet...)
	var iv []byte
	var newSeq int
	iv, newSeq = Mmtls.GetEncryptIv(client.longLinkEncodeIv, int(client.clientSequence))
	client.clientSequence = int32(newSeq)
	clientFinishByte := Algorithm.NewAES_GCMEncrypter(client.longLinkEncodeKey, sendData, iv, aad)
	var clientFinishWrapperBuffer = new(bytes.Buffer)
	clientFinishWrapperBuffer.Write([]byte{0x16, 0xf1, 0x03})
	var finishLengthByte = []byte{0, 0}
	binary.BigEndian.PutUint16(finishLengthByte, uint16(len(clientFinishByte)))
	clientFinishWrapperBuffer.Write(finishLengthByte)
	clientFinishWrapperBuffer.Write(clientFinishByte)
	var secretBuffer = new(bytes.Buffer)
	secretBuffer.Write(Mmtls.Utf8ToBytes("expanded secret"))
	secretBuffer.Write(dataHash)
	secretData := Algorithm.Hkdf_Expand(sha256.New, client.shareKey, secretBuffer.Bytes(), 32)
	var expandBuffer = new(bytes.Buffer)
	expandBuffer.Write(Mmtls.Utf8ToBytes("application data key expansion"))
	expandBuffer.Write(dataHash)
	expandData := Algorithm.Hkdf_Expand(sha256.New, secretData, expandBuffer.Bytes(), 56)
	client.longLinkEncodeKey = expandData[0:16]
	client.longLinkDecodeKey = expandData[16:32]
	client.longLinkEncodeIv = expandData[32:44]
	client.longLinkDecodeIv = expandData[44:]
	return clientFinishWrapperBuffer.Bytes()
}

// ------------------------------ 组包解包处理 -------------------------------------------
// mmtls加密
func (client *TcpClient) PackMmtlsLong(plainMessage []byte) ([]byte, error) {
	//log.Infof("TcpClient: PackMmtlsLong: message(%v)[%x]", len(plainMessage), plainMessage)
	var nonce []byte
	var aadBuffer = new(bytes.Buffer)
	binary.Write(aadBuffer, binary.BigEndian, uint64(client.clientSequence))
	var newSeq int
	nonce, newSeq = Mmtls.GetNonce(client.longLinkEncodeIv, int(client.clientSequence))
	client.clientSequence = int32(newSeq)
	aadBuffer.Write([]byte{0x17, 0xf1, 0x03})
	binary.Write(aadBuffer, binary.BigEndian, uint16(len(plainMessage)+16))
	cipherMessage := Algorithm.NewAES_GCMEncrypter(client.longLinkEncodeKey, plainMessage, nonce, aadBuffer.Bytes())
	if cipherMessage == nil {
		return nil, errors.New("AESGCM加密消息失败")
	}
	var wrapBuffer = new(bytes.Buffer)
	wrapBuffer.Write([]byte{0x17, 0xf1, 0x03})
	binary.Write(wrapBuffer, binary.BigEndian, uint16(len(cipherMessage)))
	wrapBuffer.Write(cipherMessage)
	return wrapBuffer.Bytes(), nil
}

// mmtls解密
func (client *TcpClient) UnpackMmtlsLong(messageBytes []byte) []byte {
	packData := messageBytes[5:len(messageBytes)]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, messageBytes[0:5]...)
	var iv []byte
	var newSeq int
	iv, newSeq = Mmtls.GetDecryptIv(client.longLinkDecodeIv, int(client.serverSequence))
	client.serverSequence = int32(newSeq)
	ret := Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, packData, iv, aad)
	return ret
}

// 组包头
func BuildWrapper(message []byte, cmdId int, mmtlsSeq int) []byte {
	dataWrapper := new(bytes.Buffer)
	binary.Write(dataWrapper, binary.BigEndian, int32(len(message)+16)) // 包头组成1: 总长度
	binary.Write(dataWrapper, binary.BigEndian, int16(16))              // 包头组成2: 头部长度
	binary.Write(dataWrapper, binary.BigEndian, int16(1))               // 包头组成3: 组包版本号
	binary.Write(dataWrapper, binary.BigEndian, int32(cmdId))           // 包头组成4: 操作命令ID
	binary.Write(dataWrapper, binary.BigEndian, int32(mmtlsSeq))        // 包头组成5: 组包序列
	dataWrapper.Write(message)
	return dataWrapper.Bytes()
}

// 解包头
func StripWrapper(message []byte, length int) ([]byte, int, int, int, int, int) {
	totalLength := binary.BigEndian.Uint32(message[:4])
	headLength := binary.BigEndian.Uint16(message[4:6])
	packVersion := binary.BigEndian.Uint16(message[6:8])
	cmdId := binary.BigEndian.Uint32(message[8:12])
	packSequence := binary.BigEndian.Uint32(message[12:16])
	packData := message[16:]
	return packData, int(cmdId), int(packSequence), int(packVersion), int(headLength), int(totalLength)
}

// ------------------------------ 消息执行 ----------------------------------------------
// 处理消息
func (client *TcpClient) HandleMessage(message []byte) {
	if len(message) > 0 {
		message = append(client.messageCache, message...)
	}
	// messageBody, cmdId, packSequence, packVersion, headLength, totalLength := StripWrapper(message, len(message))
	// log.Infof("TcpClient[%d]: handle_message收到消息{cmd_id: %v, pack_seq: %v, pack_version: %v, head_length: %v, total_length: %v}", socketFD(client.conn), cmdId, packSequence, packVersion, headLength, totalLength)

	messageBody, cmdId, packSequence, _, _, totalLength := StripWrapper(message, len(message))
	// 超长的数据包会分包发送, 这里使用缓存将分包归总
	if totalLength > len(message) {
		// log.Infof("长包跟踪: 长度不对缓存不处理: 预计长度[%d], 实际长度[%d]", totalLength, len(message))
		client.messageCache = message
		return
	} else {
		client.messageCache = []byte{}
	}
	if cmdId != 1000000006 {
		// log.Infof("TcpClient[%d]: handle_message收到消息{cmd_id: %v, pack_seq: %v, pack_version: %v, head_length: %v, total_length: %v}", socketFD(client.conn), cmdId, packSequence, packVersion, headLength, totalLength)
		if client.callbackMsg != nil {
			go client.callbackMsg(cmdId)
		}
	}
	if cmdId == 24 {
		// TODO(kedaya): 回调SyncMessage - 已实现完整的消息同步回调机制
		// 解析消息状态，提供更详细的回调信息
		var status uint32 = 0
		if len(messageBody) >= 4 {
			status = binary.BigEndian.Uint32(messageBody)
		}

		log.WithFields(log.Fields{
			"wxid":   client.model.Wxid,
			"status": status,
			"cmdId":  cmdId,
		}).Info("收到24消息提醒，执行SyncMessage回调")

		// 执行配置的业务回调URL
		syncUrl := strings.Replace(beego.AppConfig.String("syncmessagebusinessuri"), "{0}", client.model.Wxid, -1)
		if syncUrl != "" {
			// 构建回调数据
			callbackData := url.Values{}
			callbackData.Set("wxid", client.model.Wxid)
			callbackData.Set("status", fmt.Sprintf("%d", status))
			callbackData.Set("cmdId", fmt.Sprintf("%d", cmdId))
			callbackData.Set("timestamp", fmt.Sprintf("%d", time.Now().Unix()))

			go func() {
				result := comm.HttpPost(syncUrl, callbackData, nil, "", "", "", "")
				if result == "" || result != "" {
					// HttpPost返回空字符串表示失败，非空字符串可能是错误信息或成功响应
					// 这里我们检查是否包含常见的错误关键词来判断是否成功
					if result == "" ||
						strings.Contains(result, "error") ||
						strings.Contains(result, "Error") ||
						strings.Contains(result, "异常") ||
						strings.Contains(result, "失败") ||
						strings.Contains(result, "timeout") ||
						strings.Contains(result, "connection") {
						log.WithFields(log.Fields{
							"wxid":    client.model.Wxid,
							"syncUrl": syncUrl,
							"error":   result,
						}).Error("SyncMessage回调失败")
					} else {
						log.WithFields(log.Fields{
							"wxid":    client.model.Wxid,
							"syncUrl": syncUrl,
							"result":  result,
						}).Debug("SyncMessage回调成功")
					}
				}
			}()
		} else {
			log.WithField("wxid", client.model.Wxid).Debug("未配置SyncMessage回调URL，跳过回调")
		}

		// 执行内部消息回调（如果有注册的话）
		if client.callbackMsg != nil {
			go client.callbackMsg(cmdId)
		}

		return
	}
	// 处理回调队列
	client.queueMutex.Lock()
	cb := client.queue[packSequence]
	if cb != nil {
		delete(client.queue, packSequence)
	}
	client.queueMutex.Unlock()

	// 在锁外执行回调，避免死锁
	if cb != nil {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("回调执行时发生panic: %v", r)
				}
			}()
			cb(messageBody)
		}()
	}
}

// Send 发送数据
func (client *TcpClient) Send(data []byte, tag string) error {
	if len(data) == 0 {
		return errors.New("发送数据不能为空")
	}

	// 检查连接是否已关闭
	if atomic.LoadInt32(&client.closed) == 1 {
		return errors.New("连接已关闭")
	}

	// 使用读锁保护连接
	client.connMutex.RLock()
	conn := client.conn
	client.connMutex.RUnlock()

	if conn == nil {
		return errors.New("连接为空")
	}

	// 设置写入超时
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))

	// 记录日志（心跳消息使用debug级别）
	if tag != "Tcp心跳" {
		log.Infof("TcpClient发送[%s]: 长度=%d", tag, len(data))
	} else {
		log.Debugf("TcpClient发送心跳: 长度=%d", len(data))
	}

	// 发送数据
	n, err := conn.Write(data)
	if err != nil {
		log.Errorf("发送数据失败: %v", err)
		// 如果是连接错误，标记为关闭
		if isConnectionError(err) {
			atomic.StoreInt32(&client.closed, 1)
		}
		return fmt.Errorf("发送数据失败: %w", err)
	}

	if n != len(data) {
		log.Warnf("数据发送不完整: 期望=%d, 实际=%d", len(data), n)
	}

	return nil
}

// MmtlsSend mmtls发包
func (client *TcpClient) MmtlsSend(data []byte, cmdId int, tag string) (*[]byte, error) {
	if client == nil {
		return nil, errors.New("客户端为空")
	}

	// 检查连接是否已关闭
	if atomic.LoadInt32(&client.closed) == 1 {
		return nil, errors.New("连接已关闭")
	}

	// 获取当前序列号并自增
	currentSeq := atomic.AddInt32(&client.mmtlsSequence, 1) - 1

	// mmtls组包头
	dataWrapper := BuildWrapper(data, cmdId, int(currentSeq))
	sendData, err := client.PackMmtlsLong(dataWrapper)
	if err != nil {
		return nil, fmt.Errorf("mmtls加密失败: %w", err)
	}

	// 创建响应channel
	respChan := make(chan []byte, 1)

	// 注册回调处理
	client.queueMutex.Lock()
	client.queue[int(currentSeq)] = func(recv []byte) {
		select {
		case respChan <- recv:
		default:
			log.Warn("响应channel已满，丢弃响应")
		}
	}
	client.queueMutex.Unlock()

	// 确保在函数结束时清理回调
	defer func() {
		client.queueMutex.Lock()
		delete(client.queue, int(currentSeq))
		client.queueMutex.Unlock()
	}()

	// 发包
	if err := client.Send(sendData, tag); err != nil {
		return nil, fmt.Errorf("发送数据失败: %w", err)
	}

	// 等待结果，使用context控制超时
	timeoutSpan, _ := time.ParseDuration(beego.AppConfig.String("longlinkconnecttimeout"))
	if timeoutSpan == 0 {
		timeoutSpan = 30 * time.Second // 默认30秒超时
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeoutSpan)
	defer cancel()

	var resp []byte
	select {
	case resp = <-respChan:
		// 收到响应
	case <-ctx.Done():
		return nil, errors.New("请求超时")
	case <-client.ctx.Done():
		return nil, errors.New("客户端已关闭")
	}

	if len(resp) < 32 {
		// 长度小于32, 用户session已失效
		return nil, errors.New("用户可能退出")
	}

	// 处理业务数据
	loginDataMu := comm.GetLoginLock(client.model.Wxid)
	loginDataMu.Lock()
	defer loginDataMu.Unlock()

	client.model, _ = comm.GetLoginata(client.model.Wxid, loginDataMu)
	unpackData := Algorithm.UnpackBusinessPacket(resp, client.model.Sessionkey, client.model.Uin, &client.model.Cooike)

	// 将cookie更新保存到redis
	err = comm.CreateLoginData(client.model, client.model.Wxid, 0, loginDataMu)
	if err != nil {
		log.Errorf("TcpClient: MmtlsSend回调时更新redis失败: %v", err)
		return nil, fmt.Errorf("更新redis失败: %w", err)
	}

	return &unpackData, nil
}

// Terminate 安全关闭客户端连接
func (client *TcpClient) Terminate() {
	if client == nil {
		return
	}

	// 设置关闭标志
	atomic.StoreInt32(&client.closed, 1)

	// 取消context
	if client.cancel != nil {
		client.cancel()
	}

	// 关闭连接
	client.connMutex.Lock()
	if client.conn != nil {
		client.conn.Close()
		client.conn = nil
	}
	client.connMutex.Unlock()

	// 清理回调队列
	client.queueMutex.Lock()
	client.queue = make(map[int]func([]byte))
	client.queueMutex.Unlock()

	// 清理消息缓存
	client.cacheMutex.Lock()
	client.receivedBytes = nil
	client.messageCache = nil
	client.cacheMutex.Unlock()

	log.Debugf("TcpClient已终止: wxid=%s", func() string {
		if client.model != nil {
			return client.model.Wxid
		}
		return "unknown"
	}())
}

// SendTcpHeartBeat 发送TCP心跳
func (client *TcpClient) SendTcpHeartBeat() {
	if client == nil {
		return
	}

	startTime := atomic.LoadInt64(&client.startTime)
	heartbeatInterval := GetHeartbeatInterval() // 使用配置管理器

	log.Debugf("开始心跳循环: wxid=%s, 间隔=%v", client.model.Wxid, heartbeatInterval)
	defer log.Debugf("心跳循环结束: wxid=%s", client.model.Wxid)

	ticker := time.NewTicker(heartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-client.ctx.Done():
			log.Debug("收到停止信号，退出心跳循环")
			return
		case <-ticker.C:
			// 检查连接是否已关闭
			if atomic.LoadInt32(&client.closed) == 1 {
				log.Debug("连接已关闭，退出心跳循环")
				return
			}

			// 时间不一致说明已经重新连接，退出心跳
			if startTime != atomic.LoadInt64(&client.startTime) {
				log.Debug("startTime已变更，退出心跳循环")
				return
			}

			// 发送心跳包
			if err := client.sendHeartbeat(); err != nil {
				log.Errorf("发送心跳失败: %v", err)
				// 连续失败可能表示连接有问题
				atomic.StoreInt32(&client.closed, 1)
				return
			}

			// 更新最后心跳时间
			atomic.StoreInt64(&client.lastHeartbeatTime, time.Now().Unix())
		}
	}
}

// sendHeartbeat 发送心跳包
func (client *TcpClient) sendHeartbeat() error {
	sendData, err := client.PackMmtlsLong(BuildWrapper([]byte{}, 6, -1))
	if err != nil {
		return fmt.Errorf("构建心跳包失败: %w", err)
	}

	client.Send(sendData, "Tcp心跳")
	return nil
}

// SetStartTime 设置启动时间
func (client *TcpClient) SetStartTime(start int64) {
	atomic.StoreInt64(&client.startTime, start)
}

// GetStartTime 获取启动时间
func (client *TcpClient) GetStartTime() int64 {
	return atomic.LoadInt64(&client.startTime)
}

// IsHandshaking 检查是否在握手中
func (client *TcpClient) IsHandshaking() bool {
	return atomic.LoadInt32(&client.handshaking) == 1
}

// IsClosed 检查连接是否已关闭
func (client *TcpClient) IsClosed() bool {
	return atomic.LoadInt32(&client.closed) == 1
}

// GetLastHeartbeatTime 获取最后心跳时间
func (client *TcpClient) GetLastHeartbeatTime() int64 {
	return atomic.LoadInt64(&client.lastHeartbeatTime)
}
