package controllers

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"wechatdll/TcpPoll"
	"wechatdll/comm"
	"wechatdll/models"
	"wechatdll/models/Msg"
)

// 消息模块
type MsgController struct {
	BaseController
}

// @Summary 同步消息
// @Param	body		body 	Msg.SyncParam   true	"Scene填写0,Synckey留空"
// @Success 200
// @router /Sync [post]
func (c *MsgController) Sync() {
	var ParamData Msg.SyncParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.Sync(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

/*// @Summary 引用文本消息
// @Param	body		body 	Msg.Quote   true	"Fromusr == 被引用人Wxid, Displayname == 被引用人名称, NewMsgId == 引用人消息返回的NewMsgId, QuoteContent == 引用内容, MsgContent == 消息内容"
// @Success 200
// @router /Quote [post]
func (c *MsgController) Quote() {
	var ParamData Msg.Quote
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	AppXml := `<appmsg appid=""  sdkver="0"><title>`+ParamData.MsgContent+`</title><des></des><action></action><type>57</type><showtype>0</showtype><soundtype>0</soundtype><mediatagname></mediatagname><messageext></messageext><messageaction></messageaction><content></content><contentattr>0</contentattr><url></url><lowurl></lowurl><dataurl></dataurl><lowdataurl></lowdataurl><songalbumurl></songalbumurl><songlyric></songlyric><appattach><totallen>0</totallen><attachid></attachid><emoticonmd5></emoticonmd5><fileext></fileext><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><extinfo></extinfo><sourceusername></sourceusername><sourcedisplayname></sourcedisplayname><thumburl></thumburl><md5></md5><statextstr></statextstr><directshare>0</directshare><refermsg><type>1</type><svrid>`+ParamData.NewMsgId+`</svrid><fromusr>`+ParamData.Fromusr+`</fromusr><chatusr>`+ParamData.Wxid+`</chatusr><displayname>`+ParamData.Displayname+`</displayname><content>`+ParamData.QuoteContent+`</content><msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;`+ParamData.MsgSeq+`&lt;/sequence_id&gt;&lt;/msgsource&gt;</msgsource></refermsg></appmsg><frsername></fromusername>`

	fmt.Println(AppXml)

	WXDATA := Msg.SendAppMsg(Msg.SendAppMsgParam{
		Wxid:   ParamData.Wxid,
		ToWxid: ParamData.ToWxid,
		Xml:    AppXml,
		Type:   57,
	})
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}*/

// @Summary 发送App消息
// @Param	body		body 	Msg.SendAppMsgParam   true	"Type请根据场景设置,xml请自行构造"
// @Success 200
// @router /SendApp [post]
func (c *MsgController) SendApp() {
	var ParamData Msg.SendAppMsgParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendAppMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送分享链接消息
// @Param	body		body 	Msg.SendAppMsgParam   true	"Type==类型 Desc==描述 Xml==发送xml内容 ToWxid==接受者"
// @Success 200
// @router /ShareLink [post]
func (c *MsgController) ShareLink() {
	var ParamData Msg.SendAppMsgParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendAppMsg(Msg.SendAppMsgParam{
		Wxid:   ParamData.Wxid,
		ToWxid: ParamData.ToWxid,
		Xml:    ParamData.Xml,
		Type:   ParamData.Type,
	})
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送文本消息
// @Param	body		body 	Msg.SendNewMsgParam   true	"Type请填写1 At == 群@,多个wxid请用,隔开"
// @Success 200
// @router /SendTxt [post]
func (c *MsgController) SendTxt() {
	var ParamData Msg.SendNewMsgParam
	data := c.Ctx.Input.RequestBody

	// 预处理JSON数据，修复包含换行符的问题
	processedData := c.preprocessJSONData(data)

	err := json.Unmarshal(processedData, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendNewMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送图片
// @Param	body		body 	Msg.SendImageMsgParam   true	"请注意base64格式"
// @Success 200
// @router /UploadImg [post]
func (c *MsgController) UploadImg() {
	var ParamData Msg.SendImageMsgParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendImageMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送Emoji
// @Param	body		body 	Msg.SendEmojiParam   true	""
// @Success 200
// @router /SendEmoji [post]
func (c *MsgController) SendEmoji() {
	var ParamData Msg.SendEmojiParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendEmojiMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送Cdn视频(转发视频)
// @Param	body		body 	Msg.DefaultParam   true	"Content==消息xml"
// @Success 200
// @router /SendCDNVideo [post]
func (c *MsgController) SendCDNVideo() {
	var ParamData Msg.DefaultParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendCDNVideoMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送Cdn图片(转发图片)
// @Param	body		body 	Msg.DefaultParam   true	"Content==消息xml"
// @Success 200
// @router /SendCDNImg [post]
func (c *MsgController) SendCDNImg() {
	var ParamData Msg.DefaultParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendCDNImgMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送文件(转发,并非上传)
// @Param	body		body 	Msg.DefaultParam   true	"Content==收到文件消息xml"
// @Success 200
// @router /SendCDNFile [post]
func (c *MsgController) SendCDNFile() {
	var ParamData Msg.DefaultParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	XML := strings.Replace(ParamData.Content, "<msg>", "", -1)
	XML = strings.Replace(XML, "</msg>", "", -1)

	WXDATA := Msg.SendAppMsg(Msg.SendAppMsgParam{
		Wxid:   ParamData.Wxid,
		ToWxid: ParamData.ToWxid,
		Xml:    XML,
		Type:   6,
	})
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送语音
// @Param	body		body 	Msg.SendVoiceMessageParam   true	"Type： AMR = 0, MP3 = 2, SILK = 4, SPEEX = 1, WAVE = 3 VoiceTime ：音频长度 1000为一秒"
// @Success 200
// @router /SendVoice [post]
func (c *MsgController) SendVoice() {
	var ParamData Msg.SendVoiceMessageParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendVoiceMessage(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送视频
// @Param	body		body 	Msg.SendVideoMsgParam   true	""
// @Success 200
// @router /SendVideo [post]
func (c *MsgController) SendVideo() {
	var ParamData Msg.SendVideoMsgParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendVideoMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 分享名片
// @Param	body		body 	Msg.ShareCardParam   true	"ToWxid==接收的微信ID CardWxId==名片wxid CardNickName==名片昵称 CardAlias==名片别名 "
// @Success 200
// @router /ShareCard [post]
func (c *MsgController) ShareCard() {
	var ParamData Msg.ShareCardParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendNewMsg(Msg.SendNewMsgParam{
		Wxid:    ParamData.Wxid,
		ToWxid:  ParamData.ToWxid,
		Content: fmt.Sprintf("<msg username=\"%v\" nickname=\"%v\" fullpy=\"\" shortpy=\"\" alias=\"%v\" imagestatus=\"3\" scene=\"17\" province=\"\" city=\"\" sign=\"\" sex=\"1\" certflag=\"0\" certinfo=\"\" brandIconUrl=\"\" brandHomeUrl=\"\" brandSubscriptConfigUrl=\"\" brandFlags=\"0\" regionCode=\"CN\" ></msg>", ParamData.CardWxId, ParamData.CardNickName, ParamData.CardAlias),
		Type:    42,
	})
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 测试WebSocket消息推送
// @Param	wxid	query	string	true	"微信ID"
// @Success 200
// @router /TestWebSocket [get]
func (c *MsgController) TestWebSocket() {
	wxid := c.GetString("wxid")
	if wxid == "" {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 发送测试消息
	err := TcpPoll.SendTestMessage(wxid)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("发送测试消息失败: %v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "测试消息发送成功",
		Data: map[string]interface{}{
			"wxid": wxid,
			"sent": true,
		},
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 手动同步微信消息并推送到WebSocket
// @Param	wxid	query	string	true	"微信ID"
// @Success 200
// @router /SyncAndPush [get]
func (c *MsgController) SyncAndPush() {
	wxid := c.GetString("wxid")
	if wxid == "" {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 同步消息
	syncResult := Msg.Sync(Msg.SyncParam{
		Wxid:    wxid,
		Scene:   0,
		Synckey: "",
	})

	if !syncResult.Success {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("消息同步失败: %v", syncResult.Message),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 检查是否有新消息
	var messageCount int
	if syncData, ok := syncResult.Data.(Msg.SyncResponse); ok {
		messageCount = len(syncData.AddMsgs)
		if messageCount > 0 {
			// 推送到WebSocket
			err := TcpPoll.SendWeChatMessagesToWebSocket(wxid, syncData.AddMsgs)
			if err != nil {
				Result := models.ResponseResult{
					Code:    -1,
					Success: false,
					Message: fmt.Sprintf("推送消息到WebSocket失败: %v", err.Error()),
					Data: map[string]interface{}{
						"wxid":         wxid,
						"messageCount": messageCount,
						"synced":       true,
						"pushed":       false,
					},
				}
				c.Data["json"] = &Result
				c.ServeJSON()
				return
			}
		}
	}

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: fmt.Sprintf("消息同步完成，共 %d 条新消息", messageCount),
		Data: map[string]interface{}{
			"wxid":         wxid,
			"messageCount": messageCount,
			"synced":       true,
			"pushed":       messageCount > 0,
		},
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 获取WebSocket连接状态
// @Success 200
// @router /WebSocketStatus [get]
func (c *MsgController) WebSocketStatus() {
	connectedClients := TcpPoll.GetConnectedClients()

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: fmt.Sprintf("当前有 %d 个WebSocket连接", len(connectedClients)),
		Data: map[string]interface{}{
			"connectedClients": connectedClients,
			"count":            len(connectedClients),
		},
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 测试消息监听器
// @Param	wxid	query	string	true	"微信ID"
// @Success 200
// @router /TestMsgListener [get]
func (c *MsgController) TestMsgListener() {
	wxid := c.GetString("wxid")
	if wxid == "" {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 获取TCP管理器
	tcpManager, err := TcpPoll.GetTcpManager()
	if err != nil {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("获取TCP管理器失败: %v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 获取登录数据
	userInfo, err := comm.GetLoginata(wxid, nil)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("获取登录数据失败: %v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 测试回调函数
	testCallback := func(cmdId int) error {
		fmt.Printf("[TEST] 测试回调被触发: cmdId=%d, wxid=%s\n", cmdId, wxid)
		return nil
	}

	// 获取或创建TCP客户端
	client, err := tcpManager.GetClient(userInfo, testCallback)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("获取TCP客户端失败: %v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "消息监听器测试设置成功",
		Data: map[string]interface{}{
			"wxid":          wxid,
			"clientCreated": client != nil,
			"callbackSet":   true,
		},
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 开启/关闭自动红包领取
// @Description 设置指定微信账号的自动红包领取功能开关状态，支持Redis持久化存储
// @Tags 支付功能
// @Accept json
// @Produce json
// @Param	wxid	query	string	true	"微信ID"	example(wxid_ywloabezitqc22)
// @Param	enable	query	bool	true	"是否开启自动领取"	example(true)
// @Success 200 {object} models.ResponseResult "设置成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @router /AutoRedPacket [post]
func (c *MsgController) AutoRedPacket() {
	wxid := c.GetString("wxid")
	enable := c.GetString("enable") == "true"

	if wxid == "" {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	// 设置自动红包领取状态
	err := TcpPoll.SetAutoRedPacketStatus(wxid, enable)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("设置自动红包领取失败: %v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	status := "关闭"
	if enable {
		status = "开启"
	}

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: fmt.Sprintf("自动红包领取已%s", status),
		Data: map[string]interface{}{
			"wxid":   wxid,
			"enable": enable,
			"status": status,
		},
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 获取自动红包领取状态
// @Description 查询指定微信账号的自动红包领取功能当前状态
// @Tags 支付功能
// @Accept json
// @Produce json
// @Param	wxid	query	string	true	"微信ID"	example(wxid_ywloabezitqc22)
// @Success 200 {object} models.ResponseResult "查询成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @router /GetAutoRedPacketStatus [get]
func (c *MsgController) GetAutoRedPacketStatus() {
	wxid := c.GetString("wxid")

	if wxid == "" {
		Result := models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "wxid参数不能为空",
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	statusDetails := TcpPoll.GetAutoRedPacketStatusWithDetails(wxid)

	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "获取状态成功",
		Data:    statusDetails,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
}

// @Summary 分享位置
// @Param	body		body 	Msg.ShareLocationParam   true	" "
// @Success 200
// @router /ShareLocation [post]
func (c *MsgController) ShareLocation() {
	var ParamData Msg.ShareLocationParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.SendNewMsg(Msg.SendNewMsgParam{
		Wxid:    ParamData.Wxid,
		ToWxid:  ParamData.ToWxid,
		Content: fmt.Sprintf("<msg><location x=\"%v\" y=\"%v\" scale=\"%v\" label=\"%v\" poiname=\"%v\" maptype=\"roadmap\" infourl=\"\" fromusername=\"\" poiid=\"City\" /></msg>", ParamData.X, ParamData.Y, ParamData.Scale, ParamData.Label, ParamData.Poiname),
		Type:    42,
	})
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 撤回消息
// @Param	body		body 	Msg.RevokeMsgParam   true	"请注意参数"
// @Success 200
// @router /Revoke [post]
func (c *MsgController) Revoke() {
	var ParamData Msg.RevokeMsgParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Msg.RevokeMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送分享视频消息
// @Param	body		body 	Msg.ShareVideoMsgParam   true	"xml：微信返回的视频xml"
// @Success 200
// @router /ShareVideo [post]
func (c *MsgController) ShareVideo() {
	var ParamData Msg.ShareVideoMsgParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Msg.ShareVideoMsg(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// preprocessJSONData 预处理JSON数据，修复包含换行符等特殊字符的问题
func (c *MsgController) preprocessJSONData(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	// 将字节数组转换为字符串进行处理
	dataStr := string(data)

	// 检查是否是有效的JSON格式
	var temp interface{}
	if json.Unmarshal(data, &temp) == nil {
		// 如果已经是有效的JSON，直接返回
		return data
	}

	// 尝试修复常见的JSON问题
	// 1. 处理字符串值中的换行符
	dataStr = c.fixJSONStringValues(dataStr)

	// 2. 验证修复后的JSON是否有效
	if json.Unmarshal([]byte(dataStr), &temp) == nil {
		return []byte(dataStr)
	}

	// 如果修复失败，返回原始数据
	return data
}

// fixJSONStringValues 修复JSON字符串值中的特殊字符
func (c *MsgController) fixJSONStringValues(jsonStr string) string {
	// 使用正则表达式找到所有字符串值并进行转义
	// 匹配 "key": "value" 格式中的value部分
	re := regexp.MustCompile(`"([^"]+)"\s*:\s*"([^"]*)"`)

	return re.ReplaceAllStringFunc(jsonStr, func(match string) string {
		// 解析匹配的键值对
		parts := strings.SplitN(match, ":", 2)
		if len(parts) != 2 {
			return match
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		// 移除value两端的引号
		if len(value) >= 2 && value[0] == '"' && value[len(value)-1] == '"' {
			value = value[1 : len(value)-1]
		}

		// 转义特殊字符
		value = c.escapeJSONString(value)

		// 重新组装
		return key + `: "` + value + `"`
	})
}

// escapeJSONString 转义JSON字符串中的特殊字符
func (c *MsgController) escapeJSONString(s string) string {
	// 替换常见的需要转义的字符
	s = strings.ReplaceAll(s, "\\", "\\\\") // 反斜杠
	s = strings.ReplaceAll(s, "\"", "\\\"") // 双引号
	s = strings.ReplaceAll(s, "\r", "\\r")  // 回车符
	s = strings.ReplaceAll(s, "\n", "\\n")  // 换行符
	s = strings.ReplaceAll(s, "\t", "\\t")  // 制表符
	s = strings.ReplaceAll(s, "\b", "\\b")  // 退格符
	s = strings.ReplaceAll(s, "\f", "\\f")  // 换页符

	return s
}
