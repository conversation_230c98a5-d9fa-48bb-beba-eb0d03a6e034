package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/astaxie/beego/context"
	log "github.com/sirupsen/logrus"
)

// RateLimiter 请求频率限制器
type RateLimiter struct {
	requests map[string]*RequestInfo
	mutex    sync.RWMutex
	maxReqs  int           // 最大请求数
	window   time.Duration // 时间窗口
}

// RequestInfo 请求信息
type RequestInfo struct {
	count     int
	firstTime time.Time
	lastTime  time.Time
}

var (
	globalLimiter *RateLimiter
	once          sync.Once
)

// GetRateLimiter 获取全局限流器实例
func GetRateLimiter() *RateLimiter {
	once.Do(func() {
		// 使用默认限流配置
		globalLimiter = &RateLimiter{
			requests: make(map[string]*RequestInfo),
			maxReqs:  100,                // 默认每分钟100个请求
			window:   60 * time.Second,   // 默认60秒窗口
		}
		// 启动清理协程
		go globalLimiter.cleanup()
	})
	return globalLimiter
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow(key string) bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()

	// 获取或创建请求信息
	reqInfo, exists := rl.requests[key]
	if !exists {
		rl.requests[key] = &RequestInfo{
			count:     1,
			firstTime: now,
			lastTime:  now,
		}
		return true
	}

	// 检查时间窗口
	if now.Sub(reqInfo.firstTime) > rl.window {
		// 重置计数器
		reqInfo.count = 1
		reqInfo.firstTime = now
		reqInfo.lastTime = now
		return true
	}

	// 检查是否超过限制
	if reqInfo.count >= rl.maxReqs {
		log.Warnf("Rate limit exceeded for key: %s", key)
		return false
	}

	reqInfo.count++
	reqInfo.lastTime = now
	return true
}

// cleanup 定期清理过期的请求记录
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		now := time.Now()
		for key, reqInfo := range rl.requests {
			if now.Sub(reqInfo.lastTime) > rl.window*2 {
				delete(rl.requests, key)
			}
		}
		rl.mutex.Unlock()
	}
}

// RateLimitMiddleware Beego中间件
func RateLimitMiddleware(ctx *context.Context) {
	limiter := GetRateLimiter()

	// 使用IP+UserAgent作为限流key
	clientIP := ctx.Input.IP()
	userAgent := ctx.Input.Header("User-Agent")
	key := fmt.Sprintf("%s:%s", clientIP, userAgent)

	if !limiter.Allow(key) {
		ctx.Output.SetStatus(http.StatusTooManyRequests)
		ctx.Output.JSON(map[string]interface{}{
			"code":    -429,
			"success": false,
			"message": "请求过于频繁，请稍后再试",
			"data":    nil,
		}, false, false)
		return
	}
}




