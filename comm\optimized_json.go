package comm

import (
	"encoding/json"
	"io"
	"sync"
	"unsafe"
)

// OptimizedJSONProcessor 优化的JSON处理器
// 使用内存池和零拷贝技术减少内存分配
type OptimizedJSONProcessor struct {
	encoderPool *sync.Pool
	decoderPool *sync.Pool
	bufferPool  *sync.Pool

	// 统计信息
	stats JSONProcessorStats
	mutex sync.RWMutex
}

// JSONProcessorStats JSON处理器统计信息
type JSONProcessorStats struct {
	EncodeCount   int64 `json:"encode_count"`
	DecodeCount   int64 `json:"decode_count"`
	EncodeErrors  int64 `json:"encode_errors"`
	DecodeErrors  int64 `json:"decode_errors"`
	BufferReuses  int64 `json:"buffer_reuses"`
	MemorySavedKB int64 `json:"memory_saved_kb"`
}

// NewOptimizedJSONProcessor 创建优化的JSON处理器
func NewOptimizedJSONProcessor() *OptimizedJSONProcessor {
	processor := &OptimizedJSONProcessor{}

	// 编码器池
	processor.encoderPool = &sync.Pool{
		New: func() interface{} {
			return json.NewEncoder(io.Discard)
		},
	}

	// 解码器池
	processor.decoderPool = &sync.Pool{
		New: func() interface{} {
			return json.NewDecoder(nil)
		},
	}

	// 缓冲区池
	processor.bufferPool = &sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 1024) // 1KB初始容量
		},
	}

	return processor
}

// Marshal 优化的JSON序列化
func (processor *OptimizedJSONProcessor) Marshal(v interface{}) ([]byte, error) {
	processor.mutex.Lock()
	processor.stats.EncodeCount++
	processor.mutex.Unlock()

	// 从池中获取缓冲区
	buffer := processor.bufferPool.Get().([]byte)
	buffer = buffer[:0] // 重置长度但保持容量
	defer func() {
		// 如果缓冲区太大，不要放回池中
		if cap(buffer) <= 64*1024 { // 64KB限制
			processor.bufferPool.Put(buffer)
			processor.mutex.Lock()
			processor.stats.BufferReuses++
			processor.mutex.Unlock()
		}
	}()

	// 使用标准库进行序列化（可以考虑使用更快的JSON库如jsoniter）
	data, err := json.Marshal(v)
	if err != nil {
		processor.mutex.Lock()
		processor.stats.EncodeErrors++
		processor.mutex.Unlock()
		return nil, err
	}

	// 复制到缓冲区（避免返回内部切片）
	buffer = append(buffer, data...)
	result := make([]byte, len(buffer))
	copy(result, buffer)

	// 估算节省的内存
	processor.mutex.Lock()
	processor.stats.MemorySavedKB += int64(cap(buffer)-len(buffer)) / 1024
	processor.mutex.Unlock()

	return result, nil
}

// Unmarshal 优化的JSON反序列化
func (processor *OptimizedJSONProcessor) Unmarshal(data []byte, v interface{}) error {
	processor.mutex.Lock()
	processor.stats.DecodeCount++
	processor.mutex.Unlock()

	err := json.Unmarshal(data, v)
	if err != nil {
		processor.mutex.Lock()
		processor.stats.DecodeErrors++
		processor.mutex.Unlock()
	}

	return err
}

// MarshalToWriter 直接序列化到Writer，避免中间缓冲
func (processor *OptimizedJSONProcessor) MarshalToWriter(w io.Writer, v interface{}) error {
	processor.mutex.Lock()
	processor.stats.EncodeCount++
	processor.mutex.Unlock()

	// 直接创建新的编码器（标准库没有Reset方法）
	encoder := json.NewEncoder(w)

	err := encoder.Encode(v)
	if err != nil {
		processor.mutex.Lock()
		processor.stats.EncodeErrors++
		processor.mutex.Unlock()
	}

	return err
}

// UnmarshalFromReader 直接从Reader反序列化
func (processor *OptimizedJSONProcessor) UnmarshalFromReader(r io.Reader, v interface{}) error {
	processor.mutex.Lock()
	processor.stats.DecodeCount++
	processor.mutex.Unlock()

	// 直接创建新的解码器（标准库没有Reset方法）
	decoder := json.NewDecoder(r)

	err := decoder.Decode(v)
	if err != nil {
		processor.mutex.Lock()
		processor.stats.DecodeErrors++
		processor.mutex.Unlock()
	}

	return err
}

// GetStats 获取统计信息
func (processor *OptimizedJSONProcessor) GetStats() JSONProcessorStats {
	processor.mutex.RLock()
	defer processor.mutex.RUnlock()
	return processor.stats
}

// ResetStats 重置统计信息
func (processor *OptimizedJSONProcessor) ResetStats() {
	processor.mutex.Lock()
	defer processor.mutex.Unlock()
	processor.stats = JSONProcessorStats{}
}

// StringToBytes 零拷贝字符串转字节切片（仅用于只读操作）
func StringToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(&struct {
		string
		Cap int
	}{s, len(s)}))
}

// BytesToString 零拷贝字节切片转字符串（仅用于只读操作）
func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

// 全局优化JSON处理器
var (
	globalJSONProcessor *OptimizedJSONProcessor
	jsonProcessorOnce   sync.Once
)

// GetOptimizedJSONProcessor 获取全局优化JSON处理器
func GetOptimizedJSONProcessor() *OptimizedJSONProcessor {
	jsonProcessorOnce.Do(func() {
		globalJSONProcessor = NewOptimizedJSONProcessor()
	})
	return globalJSONProcessor
}

// FastMarshal 快速JSON序列化（使用全局处理器）
func FastMarshal(v interface{}) ([]byte, error) {
	return GetOptimizedJSONProcessor().Marshal(v)
}

// FastUnmarshal 快速JSON反序列化（使用全局处理器）
func FastUnmarshal(data []byte, v interface{}) error {
	return GetOptimizedJSONProcessor().Unmarshal(data, v)
}

// MemoryOptimizedBuffer 内存优化的缓冲区
type MemoryOptimizedBuffer struct {
	data []byte
	pool *sync.Pool
}

// NewMemoryOptimizedBuffer 创建内存优化缓冲区
func NewMemoryOptimizedBuffer(initialSize int) *MemoryOptimizedBuffer {
	buffer := &MemoryOptimizedBuffer{}

	buffer.pool = &sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, initialSize)
		},
	}

	buffer.data = buffer.pool.Get().([]byte)
	return buffer
}

// Write 写入数据
func (buf *MemoryOptimizedBuffer) Write(p []byte) (n int, err error) {
	buf.data = append(buf.data, p...)
	return len(p), nil
}

// Bytes 获取数据
func (buf *MemoryOptimizedBuffer) Bytes() []byte {
	return buf.data
}

// Reset 重置缓冲区
func (buf *MemoryOptimizedBuffer) Reset() {
	buf.data = buf.data[:0]
}

// Close 关闭缓冲区并归还到池中
func (buf *MemoryOptimizedBuffer) Close() {
	if buf.pool != nil && buf.data != nil {
		// 如果缓冲区太大，不要放回池中
		if cap(buf.data) <= 64*1024 {
			buf.pool.Put(buf.data[:0])
		}
		buf.data = nil
	}
}

// ObjectPool 通用对象池
type ObjectPool struct {
	pool    *sync.Pool
	newFunc func() interface{}

	// 统计信息
	gets  int64
	puts  int64
	mutex sync.RWMutex
}

// NewObjectPool 创建通用对象池
func NewObjectPool(newFunc func() interface{}) *ObjectPool {
	op := &ObjectPool{
		newFunc: newFunc,
	}

	op.pool = &sync.Pool{
		New: newFunc,
	}

	return op
}

// Get 从池中获取对象
func (op *ObjectPool) Get() interface{} {
	op.mutex.Lock()
	op.gets++
	op.mutex.Unlock()

	return op.pool.Get()
}

// Put 将对象放回池中
func (op *ObjectPool) Put(obj interface{}) {
	if obj == nil {
		return
	}

	op.mutex.Lock()
	op.puts++
	op.mutex.Unlock()

	op.pool.Put(obj)
}

// GetStats 获取池统计信息
func (op *ObjectPool) GetStats() map[string]int64 {
	op.mutex.RLock()
	defer op.mutex.RUnlock()

	return map[string]int64{
		"gets": op.gets,
		"puts": op.puts,
	}
}
