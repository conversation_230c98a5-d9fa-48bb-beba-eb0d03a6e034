package wxcore

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"wechatdll/comm"
	"wechatdll/srv"
	"wechatdll/srv/wxface"
)

// WXConnectMgr 微信链接管理器
type WXConnectMgr struct {
	wxConnectMap map[string]wxface.IWXConnect //管理的连接信息
	wxConnLock   sync.RWMutex                 //读写连接的读写锁
}

// Add 添加链接
func (wm *WXConnectMgr) Add(wxConnect wxface.IWXConnect) {
	wm.wxConnLock.Lock()
	defer wm.wxConnLock.Unlock()
	wm.wxConnectMap[wxConnect.GetWXAccount().GetUserInfo().Wxid] = wxConnect
	// 打印链接数量
	go wm.ShowConnectInfo()
}

// GetWXConnectByWXID 根据WXID获取微信链接
func (wm *WXConnectMgr) GetWXConnectByWXID(wxid string) wxface.IWXConnect {
	//保护共享资源Map 加读锁
	wm.wxConnLock.RLock()
	defer wm.wxConnLock.RUnlock()
	//根据WXID获取微信链接
	for _, wxConn := range wm.wxConnectMap {
		tmpUserInfo := wxConn.GetWXAccount().GetUserInfo()
		if tmpUserInfo == nil || strings.Compare(tmpUserInfo.Wxid, wxid) != 0 {
			continue
		}
		return wxConn
	}
	return nil
}

func (wm *WXConnectMgr) ShowConnectInfo() string {
	totalNum := len(wm.wxConnectMap)
	showText := time.Now().Format("2006-01-02 15:04:05")
	showText = showText + " 总链接数量: " + strconv.Itoa(totalNum)
	fmt.Println(showText)
	return showText
}

// Remove 删除连接
func (wm *WXConnectMgr) Remove(wxconn wxface.IWXConnect) {
	wm.wxConnLock.Lock()
	//删除
	currentUserInfo := wxconn.GetWXAccount().GetUserInfo()
	delete(wm.wxConnectMap, currentUserInfo.Wxid)
	currentUserInfo = nil
	// 打印链接数量
	wm.wxConnLock.Unlock()
	wm.ShowConnectInfo()
}

// ClearWXConn 删除并停止所有链接
func (wm *WXConnectMgr) ClearWXConn() {
	//保护共享资源Map 加写锁
	wm.wxConnLock.Lock()

	//停止并删除全部的连接信息
	for uuid, wxConn := range wm.wxConnectMap {
		//停止
		wxConn.Stop()
		//删除
		delete(wm.wxConnectMap, uuid)
	}

	wm.wxConnLock.Unlock()
	// 打印链接数量
	wm.ShowConnectInfo()
}

var wxConnectMgr *WXConnectMgr = &WXConnectMgr{
	wxConnectMap: make(map[string]wxface.IWXConnect),
}

// 获取 WXConnectMgr 对象
func GetWXConnectMgr() *WXConnectMgr {
	if wxConnectMgr == nil {
		wxConnectMgr = &WXConnectMgr{
			wxConnectMap: make(map[string]wxface.IWXConnect),
		}
	}
	return wxConnectMgr
}

// 初始化自动心跳
func (wm *WXConnectMgr) InitAutoHeartBeat() {
	AutoHeartBeatList := comm.GetAutoHeartBeatList()
	// 记录已经判断的wxid
	var wxidList []string = make([]string, 0)
	for _, v := range AutoHeartBeatList {
		item := v[0]
		// ["[xyuh111],[果汁] 发送心跳成功，下次心跳时间：2025-01-11 17:12:34"]
		// 去第一条是否包含“发送心跳成功”
		if !strings.Contains(item, "下次刷新时间") {
			continue
		}
		// 获取 wxid [xyuh111]
		wxid := strings.Split(item, "[")[1]
		wxid = strings.Split(wxid, "]")[0]
		// 判断 wxid 是否存在
		if strings.Contains(strings.Join(wxidList, ","), wxid) {
			continue
		}
		wxidList = append(wxidList, wxid)
		wXConnect := wm.GetWXConnectByWXID(wxid)
		if wXConnect == nil {
			// redis 取
			userInfo, err := comm.GetLoginata(wxid, nil)
			if err != nil || userInfo == nil || userInfo.Wxid == "" {
				fmt.Println("初始化心跳失败 err:", wxid)
				continue
			}
			wxAccount := srv.NewWXAccount(userInfo)
			wXConnect = NewWXConnect(wxConnectMgr, wxAccount)
			// 重要：将连接添加到管理器中
			wm.Add(wXConnect)
		}
		wXConnect.Start()
		go wXConnect.SendHeartBeat()
	}
}

// GetAllConnectedAccounts 获取所有账号信息（包括已连接和已断开的）
func (wm *WXConnectMgr) GetAllConnectedAccounts() []map[string]interface{} {
	wm.wxConnLock.RLock()
	defer wm.wxConnLock.RUnlock()

	var accounts []map[string]interface{}
	processedWxids := make(map[string]bool) // 用于去重

	// 1. 首先处理当前已连接的账号
	for _, wxConn := range wm.wxConnectMap {
		userInfo := wxConn.GetWXAccount().GetUserInfo()
		if userInfo == nil {
			continue
		}

		// 获取设备类型名称
		deviceTypeName := getDeviceTypeName(userInfo.DeviceType)

		// 获取连接状态
		status := "online"
		if wxConnImpl, ok := wxConn.(*WXConnect); ok {
			if !wxConnImpl.isConnected {
				status = "offline"
			}
		}

		account := map[string]interface{}{
			"wxid":       userInfo.Wxid,
			"nickname":   userInfo.NickName,
			"status":     status,
			"loginTime":  time.Unix(userInfo.LoginDate, 0).Format("2006-01-02 15:04:05"),
			"deviceType": deviceTypeName,
			"deviceName": userInfo.DeviceName,
			"uin":        userInfo.Uin,
			// 新增详细信息
			"mobile":           userInfo.Mobile,                                                       // 手机号
			"email":            userInfo.Email,                                                        // 邮箱
			"headUrl":          userInfo.HeadUrl,                                                      // 头像URL
			"alias":            userInfo.Alais,                                                        // 别名
			"imei":             userInfo.Imei,                                                         // 设备IMEI
			"osVersion":        userInfo.OsVersion,                                                    // 操作系统版本
			"romModel":         userInfo.RomModel,                                                     // ROM型号
			"softType":         userInfo.SoftType,                                                     // 软件类型
			"deviceId":         userInfo.Deviceid_str,                                                 // 设备ID
			"loginMode":        userInfo.LoginMode,                                                    // 登录模式
			"refreshTokenDate": time.Unix(userInfo.RefreshTokenDate, 0).Format("2006-01-02 15:04:05"), // 刷新Token时间
		}

		accounts = append(accounts, account)
		processedWxids[userInfo.Wxid] = true
	}

	// 2. 然后处理缓存中的已断开连接的账号
	cachedAccounts := wm.getCachedDisconnectedAccounts(processedWxids)
	accounts = append(accounts, cachedAccounts...)

	return accounts
}

// getCachedDisconnectedAccounts 获取缓存中已断开连接的账号
func (wm *WXConnectMgr) getCachedDisconnectedAccounts(processedWxids map[string]bool) []map[string]interface{} {
	var accounts []map[string]interface{}

	// 从Redis获取所有缓存的登录信息
	if comm.RedisClient == nil {
		return accounts
	}

	// 获取所有持久化的登录数据键（PERM1:前缀）
	keys, err := comm.RedisClient.Keys("PERM1:*").Result()
	if err != nil {
		fmt.Printf("获取Redis键失败: %v\n", err)
		return accounts
	}

	// 也获取无前缀的键（兼容旧版本）
	oldKeys, err := comm.RedisClient.Keys("wxid_*").Result()
	if err == nil {
		keys = append(keys, oldKeys...)
	}

	for _, key := range keys {
		// 移除前缀获取实际的wxid
		wxid := key
		if strings.HasPrefix(key, "PERM1:") {
			wxid = strings.TrimPrefix(key, "PERM1:")
		}

		// 跳过已经在连接管理器中的账号
		if processedWxids[wxid] {
			continue
		}

		// 获取缓存的登录数据
		loginData, err := comm.GetLoginata(wxid, nil)
		if err != nil || loginData == nil || loginData.Wxid == "" {
			continue
		}

		// 获取设备类型名称
		deviceTypeName := getDeviceTypeName(loginData.DeviceType)

		// 构造账号信息（状态为离线）
		account := map[string]interface{}{
			"wxid":       loginData.Wxid,
			"nickname":   loginData.NickName,
			"status":     "offline", // 缓存中的账号都标记为离线
			"loginTime":  time.Unix(loginData.LoginDate, 0).Format("2006-01-02 15:04:05"),
			"deviceType": deviceTypeName,
			"deviceName": loginData.DeviceName,
			"uin":        loginData.Uin,
			// 详细信息
			"mobile":           loginData.Mobile,
			"email":            loginData.Email,
			"headUrl":          loginData.HeadUrl,
			"alias":            loginData.Alais,
			"imei":             loginData.Imei,
			"osVersion":        loginData.OsVersion,
			"romModel":         loginData.RomModel,
			"softType":         loginData.SoftType,
			"deviceId":         loginData.Deviceid_str,
			"loginMode":        loginData.LoginMode,
			"refreshTokenDate": time.Unix(loginData.RefreshTokenDate, 0).Format("2006-01-02 15:04:05"),
		}

		accounts = append(accounts, account)
		processedWxids[wxid] = true // 标记为已处理，避免重复
	}

	return accounts
}

// getDeviceTypeName 根据设备类型获取设备名称
func getDeviceTypeName(deviceType string) string {
	deviceTypeMap := map[string]string{
		"ipad-17.0.0":     "iPad",
		"android-34":      "Android",
		"android-pad-34":  "Android Pad",
		"iphone-17.0.0":   "iPhone",
		"windows-10.0":    "Windows",
		"windows-unified": "Windows统一版",
		"car-1.0":         "Car",
		"mac-14.0":        "Mac",
	}

	if name, exists := deviceTypeMap[deviceType]; exists {
		return name
	}
	return deviceType
}
