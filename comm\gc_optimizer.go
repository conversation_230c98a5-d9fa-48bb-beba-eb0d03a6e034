package comm

import (
	"runtime"
	"runtime/debug"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// GCOptimizer 垃圾回收优化器
type GCOptimizer struct {
	// 配置参数
	targetGCPercent    int           // 目标GC百分比
	maxHeapSize        int64         // 最大堆大小（字节）
	gcInterval         time.Duration // GC检查间隔
	memoryThreshold    float64       // 内存使用阈值
	
	// 运行时状态
	isRunning          bool
	stop<PERSON>han           chan bool
	
	// 统计信息
	stats              GCStats
	mutex              sync.RWMutex
	
	// 回调函数
	onMemoryPressure   func()
	onGCTriggered      func(stats *runtime.MemStats)
}

// GCStats GC统计信息
type GCStats struct {
	TotalGCs           uint32        `json:"total_gcs"`
	LastGCTime         time.Time     `json:"last_gc_time"`
	AverageGCDuration  time.Duration `json:"average_gc_duration"`
	HeapSize           uint64        `json:"heap_size"`
	HeapObjects        uint64        `json:"heap_objects"`
	NextGC             uint64        `json:"next_gc"`
	GCCPUFraction      float64       `json:"gc_cpu_fraction"`
	MemoryPressureCount int64        `json:"memory_pressure_count"`
	ForcedGCCount      int64         `json:"forced_gc_count"`
}

// NewGCOptimizer 创建GC优化器
func NewGCOptimizer() *GCOptimizer {
	return &GCOptimizer{
		targetGCPercent:  100,                // 默认100%
		maxHeapSize:      1024 * 1024 * 1024, // 默认1GB
		gcInterval:       30 * time.Second,   // 默认30秒检查一次
		memoryThreshold:  0.8,                // 默认80%阈值
		stopChan:         make(chan bool),
	}
}

// SetConfig 设置配置
func (gc *GCOptimizer) SetConfig(gcPercent int, maxHeapMB int64, interval time.Duration, threshold float64) {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()
	
	gc.targetGCPercent = gcPercent
	gc.maxHeapSize = maxHeapMB * 1024 * 1024
	gc.gcInterval = interval
	gc.memoryThreshold = threshold
	
	// 应用GC百分比设置
	debug.SetGCPercent(gcPercent)
	
	log.Infof("GC优化器配置更新: GC百分比=%d, 最大堆=%dMB, 检查间隔=%v, 内存阈值=%.1f%%", 
		gcPercent, maxHeapMB, interval, threshold*100)
}

// SetCallbacks 设置回调函数
func (gc *GCOptimizer) SetCallbacks(onMemoryPressure func(), onGCTriggered func(*runtime.MemStats)) {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()
	
	gc.onMemoryPressure = onMemoryPressure
	gc.onGCTriggered = onGCTriggered
}

// Start 启动GC优化器
func (gc *GCOptimizer) Start() {
	gc.mutex.Lock()
	if gc.isRunning {
		gc.mutex.Unlock()
		return
	}
	gc.isRunning = true
	gc.mutex.Unlock()
	
	log.Info("GC优化器已启动")
	
	go gc.optimizationLoop()
}

// Stop 停止GC优化器
func (gc *GCOptimizer) Stop() {
	gc.mutex.Lock()
	if !gc.isRunning {
		gc.mutex.Unlock()
		return
	}
	gc.isRunning = false
	gc.mutex.Unlock()
	
	close(gc.stopChan)
	log.Info("GC优化器已停止")
}

// optimizationLoop 优化循环
func (gc *GCOptimizer) optimizationLoop() {
	ticker := time.NewTicker(gc.gcInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			gc.checkAndOptimize()
		case <-gc.stopChan:
			return
		}
	}
}

// checkAndOptimize 检查并优化
func (gc *GCOptimizer) checkAndOptimize() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	// 更新统计信息
	gc.updateStats(&memStats)
	
	// 检查内存压力
	heapUsage := float64(memStats.HeapInuse) / float64(gc.maxHeapSize)
	if heapUsage > gc.memoryThreshold {
		gc.handleMemoryPressure(&memStats)
	}
	
	// 动态调整GC百分比
	gc.adjustGCPercent(&memStats)
	
	// 记录详细的GC信息（仅在调试模式下）
	if log.GetLevel() == log.DebugLevel {
		gc.logGCDetails(&memStats)
	}
}

// updateStats 更新统计信息
func (gc *GCOptimizer) updateStats(memStats *runtime.MemStats) {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()
	
	gc.stats.TotalGCs = memStats.NumGC
	gc.stats.HeapSize = memStats.HeapInuse
	gc.stats.HeapObjects = memStats.HeapObjects
	gc.stats.NextGC = memStats.NextGC
	gc.stats.GCCPUFraction = memStats.GCCPUFraction
	
	if memStats.NumGC > 0 {
		gc.stats.LastGCTime = time.Unix(0, int64(memStats.LastGC))
		
		// 计算平均GC持续时间
		totalPauseNs := memStats.PauseTotalNs
		if memStats.NumGC > 0 {
			gc.stats.AverageGCDuration = time.Duration(totalPauseNs / uint64(memStats.NumGC))
		}
	}
}

// handleMemoryPressure 处理内存压力
func (gc *GCOptimizer) handleMemoryPressure(memStats *runtime.MemStats) {
	gc.mutex.Lock()
	gc.stats.MemoryPressureCount++
	gc.mutex.Unlock()
	
	log.Warnf("检测到内存压力: 堆使用=%dMB, 堆对象=%d", 
		memStats.HeapInuse/1024/1024, memStats.HeapObjects)
	
	// 触发强制GC
	gc.forceGC()
	
	// 调用内存压力回调
	if gc.onMemoryPressure != nil {
		go gc.onMemoryPressure()
	}
}

// forceGC 强制执行GC
func (gc *GCOptimizer) forceGC() {
	gc.mutex.Lock()
	gc.stats.ForcedGCCount++
	gc.mutex.Unlock()
	
	log.Debug("执行强制GC")
	runtime.GC()
	
	// 触发GC回调
	if gc.onGCTriggered != nil {
		var memStats runtime.MemStats
		runtime.ReadMemStats(&memStats)
		go gc.onGCTriggered(&memStats)
	}
}

// adjustGCPercent 动态调整GC百分比
func (gc *GCOptimizer) adjustGCPercent(memStats *runtime.MemStats) {
	heapUsage := float64(memStats.HeapInuse) / float64(gc.maxHeapSize)
	
	var newGCPercent int
	switch {
	case heapUsage > 0.9: // 90%以上，激进GC
		newGCPercent = 50
	case heapUsage > 0.7: // 70%以上，中等GC
		newGCPercent = 75
	case heapUsage > 0.5: // 50%以上，正常GC
		newGCPercent = 100
	default: // 50%以下，宽松GC
		newGCPercent = 150
	}
	
	if newGCPercent != gc.targetGCPercent {
		gc.targetGCPercent = newGCPercent
		debug.SetGCPercent(newGCPercent)
		log.Debugf("调整GC百分比为: %d%% (堆使用率: %.1f%%)", newGCPercent, heapUsage*100)
	}
}

// logGCDetails 记录GC详细信息
func (gc *GCOptimizer) logGCDetails(memStats *runtime.MemStats) {
	log.WithFields(log.Fields{
		"heap_inuse_mb":    memStats.HeapInuse / 1024 / 1024,
		"heap_objects":     memStats.HeapObjects,
		"gc_count":         memStats.NumGC,
		"gc_cpu_fraction":  memStats.GCCPUFraction,
		"next_gc_mb":       memStats.NextGC / 1024 / 1024,
		"pause_total_ms":   memStats.PauseTotalNs / 1000000,
	}).Debug("GC详细信息")
}

// GetStats 获取GC统计信息
func (gc *GCOptimizer) GetStats() GCStats {
	gc.mutex.RLock()
	defer gc.mutex.RUnlock()
	return gc.stats
}

// GetMemoryStats 获取当前内存统计
func (gc *GCOptimizer) GetMemoryStats() map[string]interface{} {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	return map[string]interface{}{
		"heap_alloc_mb":     memStats.HeapAlloc / 1024 / 1024,
		"heap_sys_mb":       memStats.HeapSys / 1024 / 1024,
		"heap_inuse_mb":     memStats.HeapInuse / 1024 / 1024,
		"heap_released_mb":  memStats.HeapReleased / 1024 / 1024,
		"heap_objects":      memStats.HeapObjects,
		"stack_inuse_mb":    memStats.StackInuse / 1024 / 1024,
		"stack_sys_mb":      memStats.StackSys / 1024 / 1024,
		"gc_count":          memStats.NumGC,
		"gc_cpu_fraction":   memStats.GCCPUFraction,
		"next_gc_mb":        memStats.NextGC / 1024 / 1024,
		"last_gc":           time.Unix(0, int64(memStats.LastGC)).Format(time.RFC3339),
		"pause_total_ms":    memStats.PauseTotalNs / 1000000,
		"num_goroutine":     runtime.NumGoroutine(),
	}
}

// TriggerGC 手动触发GC
func (gc *GCOptimizer) TriggerGC() {
	gc.forceGC()
}

// 全局GC优化器实例
var (
	globalGCOptimizer *GCOptimizer
	gcOptimizerOnce   sync.Once
)

// GetGCOptimizer 获取全局GC优化器
func GetGCOptimizer() *GCOptimizer {
	gcOptimizerOnce.Do(func() {
		globalGCOptimizer = NewGCOptimizer()
		
		// 设置默认回调
		globalGCOptimizer.SetCallbacks(
			func() {
				// 内存压力时的处理
				log.Warn("内存压力检测，正在清理缓存...")
				
				// 清理响应缓存
				if cache := GetResponseCache(); cache != nil {
					// 可以实现部分清理逻辑
				}
				
				// 清理HTTP连接池的空闲连接
				if httpPool := GetHTTPPool(); httpPool != nil {
					httpPool.CloseIdleConnections()
				}
			},
			func(memStats *runtime.MemStats) {
				// GC触发后的处理
				log.Debugf("GC完成: 堆大小=%dMB, 对象数=%d", 
					memStats.HeapInuse/1024/1024, memStats.HeapObjects)
			},
		)
		
		// 启动优化器
		globalGCOptimizer.Start()
	})
	return globalGCOptimizer
}
