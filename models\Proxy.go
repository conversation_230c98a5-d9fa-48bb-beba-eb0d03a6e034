package models

import (
	"encoding/json"
	"fmt"
	"strconv"
)

type ProxyInfo struct {
	ProxyIp       string `json:"ProxyIp,omitempty"`       // 原有字段，保持兼容性
	ProxyUser     string `json:"ProxyUser,omitempty"`     // 原有字段，保持兼容性
	ProxyPassword string `json:"ProxyPassword,omitempty"` // 原有字段，保持兼容性

	// 新增字段，兼容前端发送的格式
	Host string `json:"Host,omitempty"` // 主机地址
	Port int    `json:"Port,omitempty"` // 端口号
	Type string `json:"Type,omitempty"` // 代理类型
}

// UnmarshalJSON 自定义JSON反序列化，兼容多种格式
func (p *ProxyInfo) UnmarshalJSON(data []byte) error {
	// 定义一个临时结构体，包含所有可能的字段
	type Alias ProxyInfo
	aux := &struct {
		*Alias
		// 兼容旧格式
		ProxyIp       interface{} `json:"ProxyIp"`
		ProxyUser     interface{} `json:"ProxyUser"`
		ProxyPassword interface{} `json:"ProxyPassword"`
		// 兼容新格式
		Host interface{} `json:"Host"`
		Port interface{} `json:"Port"`
		Type interface{} `json:"Type"`
	}{
		Alias: (*Alias)(p),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 处理ProxyIp字段
	if aux.ProxyIp != nil {
		if str, ok := aux.ProxyIp.(string); ok {
			p.ProxyIp = str
		}
	}

	// 处理ProxyUser字段
	if aux.ProxyUser != nil {
		if str, ok := aux.ProxyUser.(string); ok {
			p.ProxyUser = str
		}
	}

	// 处理ProxyPassword字段
	if aux.ProxyPassword != nil {
		if str, ok := aux.ProxyPassword.(string); ok {
			p.ProxyPassword = str
		}
	}

	// 处理Host字段
	if aux.Host != nil {
		if str, ok := aux.Host.(string); ok {
			p.Host = str
			// 如果ProxyIp为空，使用Host作为ProxyIp
			if p.ProxyIp == "" {
				p.ProxyIp = str
			}
		}
	}

	// 处理Port字段
	if aux.Port != nil {
		switch v := aux.Port.(type) {
		case float64:
			p.Port = int(v)
		case int:
			p.Port = v
		case string:
			if port, err := strconv.Atoi(v); err == nil {
				p.Port = port
			}
		}

		// 如果有端口号，更新ProxyIp格式
		if p.Port > 0 && p.Host != "" {
			p.ProxyIp = fmt.Sprintf("%s:%d", p.Host, p.Port)
		}
	}

	// 处理Type字段
	if aux.Type != nil {
		if str, ok := aux.Type.(string); ok {
			p.Type = str
		}
	}

	return nil
}

// IsEmpty 检查代理信息是否为空
func (p *ProxyInfo) IsEmpty() bool {
	return p.ProxyIp == "" && p.Host == "" && p.Port == 0
}
