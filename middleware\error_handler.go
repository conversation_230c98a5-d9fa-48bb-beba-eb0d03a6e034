package middleware

import (
	"fmt"
	"runtime"
	"strings"
	"time"
	"wechatdll/models"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/context"
	log "github.com/sirupsen/logrus"
)

// ErrorHandler 全局错误处理中间件
func ErrorHandler(ctx *context.Context) {
	defer func() {
		if r := recover(); r != nil {
			// 记录panic信息
			stack := make([]byte, 4096)
			length := runtime.Stack(stack, false)

			log.Errorf("Panic recovered: %v\nStack trace:\n%s", r, string(stack[:length]))

			// 创建错误响应
			response := models.NewErrorResponse(models.ErrCodeSystemError, "系统内部错误")
			response.RequestID = generateRequestID(ctx)

			// 开发模式下显示详细错误信息
			if beego.BConfig.RunMode == "dev" {
				response.Debug = fmt.Sprintf("Panic: %v\nStack: %s", r, string(stack[:length]))
			}

			// 设置响应
			ctx.Output.Status = 500
			ctx.Output.Header("Content-Type", "application/json; charset=utf-8")

			// 安全的JSON输出，处理客户端断开连接的情况
			if err := ctx.Output.JSON(response, false, false); err != nil {
				if isClientDisconnectError(err) {
					log.Debugf("客户端连接已断开，无法发送panic错误响应: %v", err)
				} else {
					log.Errorf("发送panic错误响应失败: %v", err)
				}
			}
		}
	}()
}

// ValidationErrorHandler 参数验证错误处理
func ValidationErrorHandler(ctx *context.Context, err error) {
	response := models.NewErrorResponse(models.ErrCodeInvalidParam, "参数验证失败: "+err.Error())
	response.RequestID = generateRequestID(ctx)

	ctx.Output.Status = 400
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	ctx.Output.JSON(response, false, false)
}

// NetworkErrorHandler 网络错误处理
func NetworkErrorHandler(ctx *context.Context, err error) {
	var code int64 = models.ErrCodeNetworkError
	message := "网络连接错误"

	// 根据错误类型细分
	errStr := err.Error()
	if strings.Contains(errStr, "timeout") {
		code = models.ErrCodeTimeout
		message = "请求超时"
	} else if strings.Contains(errStr, "connection refused") {
		code = models.ErrCodeWXConnectFailed
		message = "连接被拒绝"
	}

	response := models.NewErrorResponse(code, message)
	response.RequestID = generateRequestID(ctx)

	// 记录网络错误
	log.Warnf("网络错误: %v, 请求: %s %s", err, ctx.Request.Method, ctx.Request.URL.Path)

	ctx.Output.Status = 503
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")

	// 安全的JSON输出，处理客户端断开连接的情况
	if err := ctx.Output.JSON(response, false, false); err != nil {
		if isClientDisconnectError(err) {
			log.Debugf("客户端连接已断开，无法发送网络错误响应: %v", err)
		} else {
			log.Errorf("发送网络错误响应失败: %v", err)
		}
	}
}

// WXAPIErrorHandler 微信API错误处理
func WXAPIErrorHandler(ctx *context.Context, wxErr error, wxCode int32) {
	var code int64 = models.ErrCodeWXAPIError
	message := "微信API调用失败"

	// 根据微信错误码映射
	switch wxCode {
	case -1:
		code = models.ErrCodeWXProtocolError
		message = "微信协议错误"
	case -2:
		code = models.ErrCodeWXAccountError
		message = "微信账号异常"
	case -100:
		code = models.ErrCodeLoginExpired
		message = "登录已过期"
	default:
		if wxErr != nil {
			message = fmt.Sprintf("微信API错误: %v", wxErr)
		}
	}

	response := models.NewErrorResponse(code, message)
	response.RequestID = generateRequestID(ctx)

	// 记录微信API错误
	log.Warnf("微信API错误: code=%d, err=%v, 请求: %s %s", wxCode, wxErr, ctx.Request.Method, ctx.Request.URL.Path)

	ctx.Output.Status = 400
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")

	// 安全的JSON输出，处理客户端断开连接的情况
	if err := ctx.Output.JSON(response, false, false); err != nil {
		if isClientDisconnectError(err) {
			log.Debugf("客户端连接已断开，无法发送微信API错误响应: %v", err)
		} else {
			log.Errorf("发送微信API错误响应失败: %v", err)
		}
	}
}

// BusinessErrorHandler 业务逻辑错误处理
func BusinessErrorHandler(ctx *context.Context, err error) {
	var code int64 = models.ErrCodeOperationFailed
	message := "操作失败"

	// 如果是自定义的API错误
	if apiErr, ok := err.(*models.APIError); ok {
		code = apiErr.Code
		message = apiErr.Message
	} else {
		message = err.Error()
	}

	response := models.NewErrorResponse(code, message)
	response.RequestID = generateRequestID(ctx)

	// 记录业务错误
	log.Warnf("业务错误: %v, 请求: %s %s", err, ctx.Request.Method, ctx.Request.URL.Path)

	ctx.Output.Status = 400
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	ctx.Output.JSON(response, false, false)
}

// RateLimitErrorHandler 限流错误处理
func RateLimitErrorHandler(ctx *context.Context) {
	response := models.NewErrorResponse(models.ErrCodeRateLimit, "请求过于频繁，请稍后重试")
	response.RequestID = generateRequestID(ctx)

	// 记录限流事件
	log.Warnf("请求限流: IP=%s, 请求: %s %s", ctx.Input.IP(), ctx.Request.Method, ctx.Request.URL.Path)

	ctx.Output.Status = 429
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	ctx.Output.Header("Retry-After", "60") // 建议60秒后重试
	ctx.Output.JSON(response, false, false)
}

// AuthErrorHandler 认证错误处理
func AuthErrorHandler(ctx *context.Context, message string) {
	if message == "" {
		message = "未授权访问"
	}

	response := models.NewErrorResponse(models.ErrCodeUnauthorized, message)
	response.RequestID = generateRequestID(ctx)

	// 记录认证失败
	log.Warnf("认证失败: %s, IP=%s, 请求: %s %s", message, ctx.Input.IP(), ctx.Request.Method, ctx.Request.URL.Path)

	ctx.Output.Status = 401
	ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	ctx.Output.JSON(response, false, false)
}

// generateRequestID 生成请求ID
func generateRequestID(ctx *context.Context) string {
	return fmt.Sprintf("%d-%s", time.Now().UnixNano(), ctx.Input.IP())
}

// isClientDisconnectError 检查是否是客户端断开连接的错误
func isClientDisconnectError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// 检查常见的客户端断开连接错误
	clientDisconnectPatterns := []string{
		"connection was forcibly closed by the remote host",
		"wsasend: An existing connection was forcibly closed",
		"broken pipe",
		"connection reset by peer",
		"client disconnected",
		"write: connection reset by peer",
		"write: broken pipe",
	}

	for _, pattern := range clientDisconnectPatterns {
		if strings.Contains(strings.ToLower(errStr), strings.ToLower(pattern)) {
			return true
		}
	}

	return false
}

// LogError 记录错误日志
func LogError(ctx *context.Context, err error, message string) {
	log.WithFields(log.Fields{
		"error":      err.Error(),
		"method":     ctx.Request.Method,
		"path":       ctx.Request.URL.Path,
		"ip":         ctx.Input.IP(),
		"user_agent": ctx.Request.UserAgent(),
		"request_id": generateRequestID(ctx),
	}).Error(message)
}

// LogWarn 记录警告日志
func LogWarn(ctx *context.Context, message string, fields map[string]any) {
	logFields := log.Fields{
		"method":     ctx.Request.Method,
		"path":       ctx.Request.URL.Path,
		"ip":         ctx.Input.IP(),
		"request_id": generateRequestID(ctx),
	}

	// 合并自定义字段
	for k, v := range fields {
		logFields[k] = v
	}

	log.WithFields(logFields).Warn(message)
}
