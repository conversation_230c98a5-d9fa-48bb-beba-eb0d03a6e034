package comm

import (
	"sync"
	"sync/atomic"
	"time"

	log "github.com/sirupsen/logrus"
)

// Monitor 性能监控器
type Monitor struct {
	// API调用统计
	apiStats map[string]*APIStats
	apiMutex sync.RWMutex

	// 系统统计
	systemStats *SystemStats

	// 用户活动统计
	userStats map[string]*UserStats
	userMutex sync.RWMutex

	// 启动时间
	startTime time.Time
}

// APIStats API统计信息
type APIStats struct {
	TotalCalls    int64         `json:"total_calls"`
	SuccessCalls  int64         `json:"success_calls"`
	ErrorCalls    int64         `json:"error_calls"`
	TotalDuration time.Duration `json:"total_duration"`
	AvgDuration   time.Duration `json:"avg_duration"`
	MaxDuration   time.Duration `json:"max_duration"`
	MinDuration   time.Duration `json:"min_duration"`
	LastCall      time.Time     `json:"last_call"`
}

// SystemStats 系统统计信息
type SystemStats struct {
	// 总请求数
	TotalRequests int64 `json:"total_requests"`

	// 当前活跃连接数
	ActiveConnections int64 `json:"active_connections"`

	// 内存使用情况
	MemoryUsage int64 `json:"memory_usage"`

	// 错误计数
	ErrorCount int64 `json:"error_count"`

	// 最后更新时间
	LastUpdate time.Time `json:"last_update"`
}

// UserStats 用户统计信息
type UserStats struct {
	WXID            string    `json:"wxid"`
	LoginTime       time.Time `json:"login_time"`
	LastActivity    time.Time `json:"last_activity"`
	MessagesSent    int64     `json:"messages_sent"`
	ImagesUploaded  int64     `json:"images_uploaded"`
	APICallsCount   int64     `json:"api_calls_count"`
	ErrorCount      int64     `json:"error_count"`
	TotalOnlineTime int64     `json:"total_online_time"` // 秒
}

var (
	globalMonitor *Monitor
	monitorOnce   sync.Once
)

// GetMonitor 获取全局监控器实例
func GetMonitor() *Monitor {
	monitorOnce.Do(func() {
		globalMonitor = &Monitor{
			apiStats:    make(map[string]*APIStats),
			userStats:   make(map[string]*UserStats),
			systemStats: &SystemStats{},
			startTime:   time.Now(),
		}

		// 启动监控协程
		go globalMonitor.startMonitoring()
	})
	return globalMonitor
}

// RecordAPICall 记录API调用
func (m *Monitor) RecordAPICall(endpoint string, duration time.Duration, success bool) {
	m.apiMutex.Lock()
	defer m.apiMutex.Unlock()

	stats, exists := m.apiStats[endpoint]
	if !exists {
		stats = &APIStats{
			MinDuration: duration,
			MaxDuration: duration,
		}
		m.apiStats[endpoint] = stats
	}

	atomic.AddInt64(&stats.TotalCalls, 1)
	stats.TotalDuration += duration
	stats.AvgDuration = time.Duration(int64(stats.TotalDuration) / stats.TotalCalls)
	stats.LastCall = time.Now()

	if duration > stats.MaxDuration {
		stats.MaxDuration = duration
	}
	if duration < stats.MinDuration {
		stats.MinDuration = duration
	}

	if success {
		atomic.AddInt64(&stats.SuccessCalls, 1)
	} else {
		atomic.AddInt64(&stats.ErrorCalls, 1)
	}

	// 更新系统统计
	atomic.AddInt64(&m.systemStats.TotalRequests, 1)
	if !success {
		atomic.AddInt64(&m.systemStats.ErrorCount, 1)
	}
}

// RecordUserActivity 记录用户活动
func (m *Monitor) RecordUserActivity(wxid, activity string) {
	m.userMutex.Lock()
	defer m.userMutex.Unlock()

	stats, exists := m.userStats[wxid]
	if !exists {
		stats = &UserStats{
			WXID:         wxid,
			LoginTime:    time.Now(),
			LastActivity: time.Now(),
		}
		m.userStats[wxid] = stats
	}

	stats.LastActivity = time.Now()
	atomic.AddInt64(&stats.APICallsCount, 1)

	switch activity {
	case "send_message":
		atomic.AddInt64(&stats.MessagesSent, 1)
	case "upload_image":
		atomic.AddInt64(&stats.ImagesUploaded, 1)
	case "error":
		atomic.AddInt64(&stats.ErrorCount, 1)
	}
}

// GetAPIStats 获取API统计信息
func (m *Monitor) GetAPIStats() map[string]*APIStats {
	m.apiMutex.RLock()
	defer m.apiMutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]*APIStats)
	for k, v := range m.apiStats {
		statsCopy := *v
		result[k] = &statsCopy
	}
	return result
}

// GetUserStats 获取用户统计信息
func (m *Monitor) GetUserStats() map[string]*UserStats {
	m.userMutex.RLock()
	defer m.userMutex.RUnlock()

	result := make(map[string]*UserStats)
	for k, v := range m.userStats {
		statsCopy := *v
		// 计算在线时间
		statsCopy.TotalOnlineTime = int64(time.Since(v.LoginTime).Seconds())
		result[k] = &statsCopy
	}
	return result
}

// GetSystemStats 获取系统统计信息
func (m *Monitor) GetSystemStats() *SystemStats {
	stats := *m.systemStats
	stats.LastUpdate = time.Now()
	return &stats
}

// GetStartTime 获取系统启动时间
func (m *Monitor) GetStartTime() time.Time {
	return m.startTime
}

// GetHealthStatus 获取系统健康状态
func (m *Monitor) GetHealthStatus() map[string]interface{} {
	systemStats := m.GetSystemStats()
	apiStats := m.GetAPIStats()

	// 计算错误率
	errorRate := float64(0)
	if systemStats.TotalRequests > 0 {
		errorRate = float64(systemStats.ErrorCount) / float64(systemStats.TotalRequests) * 100
	}

	// 计算平均响应时间
	var totalDuration time.Duration
	var totalCalls int64
	for _, stats := range apiStats {
		totalDuration += stats.TotalDuration
		totalCalls += stats.TotalCalls
	}

	avgResponseTime := time.Duration(0)
	if totalCalls > 0 {
		avgResponseTime = time.Duration(int64(totalDuration) / totalCalls)
	}

	// 运行时间
	uptime := time.Since(m.startTime)

	return map[string]interface{}{
		"status":             "healthy",
		"uptime":             uptime.String(),
		"total_requests":     systemStats.TotalRequests,
		"error_rate":         errorRate,
		"avg_response_time":  avgResponseTime.String(),
		"active_users":       len(m.userStats),
		"memory_usage":       systemStats.MemoryUsage,
		"active_connections": systemStats.ActiveConnections,
		"last_update":        systemStats.LastUpdate,
	}
}

// startMonitoring 启动监控协程
func (m *Monitor) startMonitoring() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		m.collectSystemMetrics()
		m.logPerformanceMetrics()
		m.cleanupOldStats()
	}
}

// collectSystemMetrics 收集系统指标
func (m *Monitor) collectSystemMetrics() {
	// 这里可以添加系统资源监控
	// 例如：CPU使用率、内存使用率、磁盘使用率等

	// 更新活跃连接数（示例）
	activeUsers := int64(len(m.userStats))
	atomic.StoreInt64(&m.systemStats.ActiveConnections, activeUsers)

	m.systemStats.LastUpdate = time.Now()
}

// logPerformanceMetrics 记录性能指标日志
func (m *Monitor) logPerformanceMetrics() {
	healthStatus := m.GetHealthStatus()

	log.WithFields(log.Fields{
		"total_requests":    healthStatus["total_requests"],
		"error_rate":        healthStatus["error_rate"],
		"avg_response_time": healthStatus["avg_response_time"],
		"active_users":      healthStatus["active_users"],
		"uptime":            healthStatus["uptime"],
	}).Info("Performance metrics")

	// 如果错误率过高，记录警告
	if errorRate, ok := healthStatus["error_rate"].(float64); ok && errorRate > 5.0 {
		log.WithField("error_rate", errorRate).Warn("High error rate detected")
	}
}

// cleanupOldStats 清理旧的统计数据
func (m *Monitor) cleanupOldStats() {
	m.userMutex.Lock()
	defer m.userMutex.Unlock()

	// 清理超过24小时未活动的用户统计
	cutoff := time.Now().Add(-24 * time.Hour)
	for wxid, stats := range m.userStats {
		if stats.LastActivity.Before(cutoff) {
			delete(m.userStats, wxid)
			log.WithField("wxid", wxid).Debug("Cleaned up old user stats")
		}
	}
}

// ResetStats 重置统计数据
func (m *Monitor) ResetStats() {
	m.apiMutex.Lock()
	m.userMutex.Lock()
	defer m.apiMutex.Unlock()
	defer m.userMutex.Unlock()

	m.apiStats = make(map[string]*APIStats)
	m.userStats = make(map[string]*UserStats)
	m.systemStats = &SystemStats{}
	m.startTime = time.Now()

	log.Info("Monitor stats reset")
}

// GetTopAPIs 获取调用最频繁的API
func (m *Monitor) GetTopAPIs(limit int) []map[string]interface{} {
	apiStats := m.GetAPIStats()

	type apiStat struct {
		Endpoint string
		Stats    *APIStats
	}

	var stats []apiStat
	for endpoint, stat := range apiStats {
		stats = append(stats, apiStat{Endpoint: endpoint, Stats: stat})
	}

	// 按调用次数排序
	for i := 0; i < len(stats)-1; i++ {
		for j := i + 1; j < len(stats); j++ {
			if stats[i].Stats.TotalCalls < stats[j].Stats.TotalCalls {
				stats[i], stats[j] = stats[j], stats[i]
			}
		}
	}

	// 限制返回数量
	if limit > 0 && limit < len(stats) {
		stats = stats[:limit]
	}

	var result []map[string]interface{}
	for _, stat := range stats {
		result = append(result, map[string]interface{}{
			"endpoint":      stat.Endpoint,
			"total_calls":   stat.Stats.TotalCalls,
			"success_calls": stat.Stats.SuccessCalls,
			"error_calls":   stat.Stats.ErrorCalls,
			"avg_duration":  stat.Stats.AvgDuration.String(),
			"last_call":     stat.Stats.LastCall,
		})
	}

	return result
}
