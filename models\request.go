package models

import (
	"fmt"
	"strings"
)

// BaseRequest 基础请求结构
//
// 所有微信API请求的基础结构，包含必要的身份标识和可选的代理配置
//
// 字段说明：
// - Wxid: 微信ID，用于标识操作的微信账号，必填
// - Proxy: 代理配置信息，可选，用于网络代理连接
//
// 使用示例：
//
//	req := BaseRequest{
//	    Wxid: "your_wxid_here",
//	    Proxy: ProxyInfo{...}, // 可选
//	}
//	if err := req.Validate(); err != nil {
//	    // 处理验证错误
//	}
type BaseRequest struct {
	Wxid  string    `json:"wxid" validate:"required"` // 微信ID，必填
	Proxy ProxyInfo `json:"proxy,omitempty"`          // 代理配置，可选
}

// Validate 验证基础请求参数
//
// 验证规则：
// - Wxid不能为空或"string"（swagger默认值）
// - 自动去除Wxid前后空格
//
// 返回值：
// - error: 验证失败时返回具体错误信息，成功时返回nil
func (r *BaseRequest) Validate() error {
	r.Wxid = strings.TrimSpace(r.Wxid)
	if r.Wxid == "" || r.Wxid == "string" {
		return fmt.Errorf("wxid不能为空")
	}
	return nil
}

// MessageRequest 消息请求基础结构
type MessageRequest struct {
	BaseRequest
	ToWxid  string `json:"to_wxid" validate:"required"`
	Content string `json:"content" validate:"required"`
}

// Validate 验证消息请求参数
func (r *MessageRequest) Validate() error {
	if err := r.BaseRequest.Validate(); err != nil {
		return err
	}

	r.ToWxid = strings.TrimSpace(r.ToWxid)
	if r.ToWxid == "" || r.ToWxid == "string" {
		return fmt.Errorf("接收者wxid不能为空")
	}

	if strings.TrimSpace(r.Content) == "" {
		return fmt.Errorf("消息内容不能为空")
	}

	return nil
}

// PaginationRequest 分页请求结构
type PaginationRequest struct {
	Page     int `json:"page" validate:"min=1"`
	PageSize int `json:"page_size" validate:"min=1,max=100"`
}

// Validate 验证分页参数
func (r *PaginationRequest) Validate() error {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.PageSize <= 0 {
		r.PageSize = 20
	}
	if r.PageSize > 100 {
		r.PageSize = 100
	}
	return nil
}

// GetOffset 获取偏移量
func (r *PaginationRequest) GetOffset() int {
	return (r.Page - 1) * r.PageSize
}

// RequestValidator 请求验证器接口
type RequestValidator interface {
	Validate() error
}

// ValidateRequest 通用请求验证函数
func ValidateRequest(req RequestValidator) error {
	return req.Validate()
}
