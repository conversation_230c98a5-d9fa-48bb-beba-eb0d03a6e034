root = "."

[build]
  args_bin = []
  bin = "./wechat_dev.exe"
  cmd = "go build -o ./wechat_dev.exe ."
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "docs", "lib", ".git", ".idea", ".vscode", "element-plus-vite-starter","cache","tmp"]
  exclude_file = []
  exclude_regex = ["_test.go", ".*\\.md$", ".*\\.txt$"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html", "conf", "json", "yaml", "yml"]
  include_file = []
  kill_delay = "2s"
  log = "tmp/build-errors.log"
  poll = false
  poll_interval = 0
  rerun = false
  rerun_delay = 500
  send_interrupt = false
  stop_on_root = false

[color]
  app = "blue"
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  time = true

[misc]
  clean_on_exit = true

[screen]
  clear_on_rebuild = true
  keep_scroll = false

# 自定义环境变量（可选）
[env]
  # 开发模式下禁用CGO以加快编译速度
  CGO_ENABLED = "1"
  # 确保为Windows平台编译
  GOOS = "windows"
  GOARCH = "amd64"
