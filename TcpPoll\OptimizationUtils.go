package TcpPoll

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	log "github.com/sirupsen/logrus"
)

// ==================== 接口定义 ====================

// ClientInterface 客户端接口，用于跨平台兼容
type ClientInterface interface {
	IsClosed() bool
	Terminate()
}

// ==================== 性能监控和统计 ====================

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	connectionCount    int64 // 当前连接数
	messageCount       int64 // 处理的消息总数
	errorCount         int64 // 错误总数
	lastResetTime      int64 // 上次重置时间
	mutex              sync.RWMutex
	stats              map[string]int64 // 自定义统计
}

var globalMonitor = &PerformanceMonitor{
	stats:         make(map[string]int64),
	lastResetTime: time.Now().Unix(),
}

// GetGlobalMonitor 获取全局性能监控器
func GetGlobalMonitor() *PerformanceMonitor {
	return globalMonitor
}

// IncrementConnection 增加连接计数
func (pm *PerformanceMonitor) IncrementConnection() {
	atomic.AddInt64(&pm.connectionCount, 1)
}

// DecrementConnection 减少连接计数
func (pm *PerformanceMonitor) DecrementConnection() {
	atomic.AddInt64(&pm.connectionCount, -1)
}

// IncrementMessage 增加消息计数
func (pm *PerformanceMonitor) IncrementMessage() {
	atomic.AddInt64(&pm.messageCount, 1)
}

// IncrementError 增加错误计数
func (pm *PerformanceMonitor) IncrementError() {
	atomic.AddInt64(&pm.errorCount, 1)
}

// GetStats 获取统计信息
func (pm *PerformanceMonitor) GetStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	stats := map[string]interface{}{
		"connections":     atomic.LoadInt64(&pm.connectionCount),
		"messages":        atomic.LoadInt64(&pm.messageCount),
		"errors":          atomic.LoadInt64(&pm.errorCount),
		"uptime_seconds":  time.Now().Unix() - pm.lastResetTime,
		"goroutines":      runtime.NumGoroutine(),
		"memory": map[string]interface{}{
			"alloc_mb":      bToMb(m.Alloc),
			"total_alloc_mb": bToMb(m.TotalAlloc),
			"sys_mb":        bToMb(m.Sys),
			"gc_cycles":     m.NumGC,
		},
		"custom_stats": make(map[string]int64),
	}
	
	// 复制自定义统计
	customStats := stats["custom_stats"].(map[string]int64)
	for k, v := range pm.stats {
		customStats[k] = v
	}
	
	return stats
}

// IncrementCustomStat 增加自定义统计
func (pm *PerformanceMonitor) IncrementCustomStat(key string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.stats[key]++
}

// SetCustomStat 设置自定义统计
func (pm *PerformanceMonitor) SetCustomStat(key string, value int64) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.stats[key] = value
}

// bToMb 字节转MB
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

// ==================== 连接池管理优化 ====================

// ConnectionPool 连接池
type ConnectionPool struct {
	connections map[string]ClientInterface
	mutex       sync.RWMutex
	maxSize     int
	cleanupTick *time.Ticker
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(maxSize int) *ConnectionPool {
	ctx, cancel := context.WithCancel(context.Background())
	pool := &ConnectionPool{
		connections: make(map[string]ClientInterface),
		maxSize:     maxSize,
		ctx:         ctx,
		cancel:      cancel,
	}
	
	// 启动清理goroutine
	go pool.cleanupRoutine()
	
	return pool
}

// Get 获取连接
func (cp *ConnectionPool) Get(key string) (ClientInterface, bool) {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()
	client, exists := cp.connections[key]
	return client, exists
}

// Put 添加连接
func (cp *ConnectionPool) Put(key string, client ClientInterface) error {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()
	
	if len(cp.connections) >= cp.maxSize {
		return fmt.Errorf("连接池已满，最大连接数: %d", cp.maxSize)
	}
	
	cp.connections[key] = client
	return nil
}

// Remove 移除连接
func (cp *ConnectionPool) Remove(key string) {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()
	delete(cp.connections, key)
}

// Size 获取连接池大小
func (cp *ConnectionPool) Size() int {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()
	return len(cp.connections)
}

// cleanupRoutine 清理例程
func (cp *ConnectionPool) cleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-cp.ctx.Done():
			return
		case <-ticker.C:
			cp.cleanup()
		}
	}
}

// cleanup 清理无效连接
func (cp *ConnectionPool) cleanup() {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()
	
	var toRemove []string
	for key, client := range cp.connections {
		if client.IsClosed() {
			toRemove = append(toRemove, key)
		}
	}
	
	for _, key := range toRemove {
		delete(cp.connections, key)
		log.Debugf("清理无效连接: %s", key)
	}
	
	if len(toRemove) > 0 {
		log.Infof("清理了 %d 个无效连接", len(toRemove))
	}
}

// Close 关闭连接池
func (cp *ConnectionPool) Close() {
	cp.cancel()
	
	cp.mutex.Lock()
	defer cp.mutex.Unlock()
	
	for key, client := range cp.connections {
		client.Terminate()
		delete(cp.connections, key)
	}
}

// ==================== 错误处理优化 ====================

// ErrorHandler 错误处理器
type ErrorHandler struct {
	errorCounts map[string]int64
	mutex       sync.RWMutex
	maxRetries  int
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(maxRetries int) *ErrorHandler {
	return &ErrorHandler{
		errorCounts: make(map[string]int64),
		maxRetries:  maxRetries,
	}
}

// ShouldRetry 判断是否应该重试
func (eh *ErrorHandler) ShouldRetry(key string, err error) bool {
	if err == nil {
		return false
	}
	
	eh.mutex.Lock()
	defer eh.mutex.Unlock()
	
	count := eh.errorCounts[key]
	if count >= int64(eh.maxRetries) {
		return false
	}
	
	eh.errorCounts[key] = count + 1
	return true
}

// ResetErrors 重置错误计数
func (eh *ErrorHandler) ResetErrors(key string) {
	eh.mutex.Lock()
	defer eh.mutex.Unlock()
	delete(eh.errorCounts, key)
}

// GetErrorCount 获取错误计数
func (eh *ErrorHandler) GetErrorCount(key string) int64 {
	eh.mutex.RLock()
	defer eh.mutex.RUnlock()
	return eh.errorCounts[key]
}

// ==================== 工具函数 ====================

// SafeGoroutine 安全的goroutine执行
func SafeGoroutine(name string, fn func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("Goroutine [%s] panic: %v", name, r)
				globalMonitor.IncrementError()
			}
		}()
		fn()
	}()
}

// WithTimeout 带超时的函数执行
func WithTimeout(timeout time.Duration, fn func() error) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	done := make(chan error, 1)
	go func() {
		done <- fn()
	}()
	
	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return fmt.Errorf("操作超时: %v", timeout)
	}
}

// LogPerformanceStats 定期记录性能统计
func LogPerformanceStats() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		stats := globalMonitor.GetStats()
		log.WithFields(log.Fields{
			"connections": stats["connections"],
			"messages":    stats["messages"],
			"errors":      stats["errors"],
			"goroutines":  stats["goroutines"],
		}).Info("性能统计")
	}
}
