package Proxy

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/astaxie/beego/logs"
)

// ProxyInfo 代理信息结构
type ProxyInfo struct {
	ID           int       `json:"id"`
	IP           string    `json:"ip"`
	Port         int       `json:"port"`
	Username     string    `json:"username"`
	Password     string    `json:"password"`
	ExpireDate   string    `json:"expire_date"`
	Status       string    `json:"status"` // active, inactive, testing, error
	LastTest     time.Time `json:"last_test"`
	ResponseTime int       `json:"response_time"` // 响应时间(ms)
	Country      string    `json:"country"`
	Region       string    `json:"region"`
	CreateTime   time.Time `json:"create_time"`
	UpdateTime   time.Time `json:"update_time"`
}

// ProxyImportRequest 批量导入代理请求
type ProxyImportRequest struct {
	ProxyList string `json:"proxy_list"` // 代理列表文本
	Format    string `json:"format"`     // 格式：ip|port|username|password|expire
}

// ProxyTestRequest 代理测试请求
type ProxyTestRequest struct {
	ProxyIDs []int `json:"proxy_ids"` // 要测试的代理ID列表
}

// ProxyListRequest 代理列表请求
type ProxyListRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Status   string `json:"status"`  // 状态筛选
	Country  string `json:"country"` // 国家筛选
	Keyword  string `json:"keyword"` // 关键词搜索
}

// ProxyListResponse 代理列表响应
type ProxyListResponse struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    ProxyData `json:"data"`
}

// ProxyData 代理数据
type ProxyData struct {
	List       []ProxyInfo `json:"list"`
	Total      int         `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// ProxyStatsResponse 代理统计响应
type ProxyStatsResponse struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    ProxyStats `json:"data"`
}

// ProxyStats 代理统计
type ProxyStats struct {
	Total    int `json:"total"`
	Active   int `json:"active"`
	Inactive int `json:"inactive"`
	Testing  int `json:"testing"`
	Error    int `json:"error"`
}

// ParseProxyLine 解析单行代理信息
func ParseProxyLine(line string) (*ProxyInfo, error) {
	// 格式：IP|Port|Username|Password|ExpireDate 或 IP|Port|Username|Password|ExpireDate|Country
	parts := strings.Split(strings.TrimSpace(line), "|")
	if len(parts) != 5 && len(parts) != 6 {
		return nil, fmt.Errorf("代理格式错误，应为：IP|Port|Username|Password|ExpireDate 或 IP|Port|Username|Password|ExpireDate|Country")
	}

	// 验证IP格式
	ip := strings.TrimSpace(parts[0])
	if net.ParseIP(ip) == nil {
		return nil, fmt.Errorf("无效的IP地址: %s", ip)
	}

	// 验证端口
	port, err := strconv.Atoi(strings.TrimSpace(parts[1]))
	if err != nil || port < 1 || port > 65535 {
		return nil, fmt.Errorf("无效的端口: %s", parts[1])
	}

	username := strings.TrimSpace(parts[2])
	password := strings.TrimSpace(parts[3])
	expireDate := strings.TrimSpace(parts[4])

	// 验证过期时间格式
	if _, err := time.Parse("2006-01-02", expireDate); err != nil {
		return nil, fmt.Errorf("无效的过期时间格式: %s，应为 YYYY-MM-DD", expireDate)
	}

	// 处理可选的地区信息
	country := ""
	if len(parts) == 6 {
		country = strings.TrimSpace(parts[5])
	} else {
		// 如果没有提供地区信息，尝试根据IP地址自动识别
		country = GetCountryByIP(ip)
	}

	return &ProxyInfo{
		IP:         ip,
		Port:       port,
		Username:   username,
		Password:   password,
		ExpireDate: expireDate,
		Country:    country,
		Status:     "inactive",
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}, nil
}

// ToJSON 转换为JSON字符串
func (p *ProxyInfo) ToJSON() string {
	data, _ := json.Marshal(p)
	return string(data)
}

// GetProxyURL 获取代理URL
func (p *ProxyInfo) GetProxyURL() string {
	return fmt.Sprintf("socks5://%s:%s@%s:%d", p.Username, p.Password, p.IP, p.Port)
}

// IsExpired 检查是否过期
func (p *ProxyInfo) IsExpired() bool {
	expireTime, err := time.Parse("2006-01-02", p.ExpireDate)
	if err != nil {
		return true
	}
	return time.Now().After(expireTime)
}

// GetDisplayName 获取显示名称
func (p *ProxyInfo) GetDisplayName() string {
	status := "🔴"
	switch p.Status {
	case "active":
		status = "🟢"
	case "testing":
		status = "🟡"
	case "inactive":
		status = "⚪"
	}

	responseTime := ""
	if p.ResponseTime > 0 {
		responseTime = fmt.Sprintf(" (%dms)", p.ResponseTime)
	}

	return fmt.Sprintf("%s %s:%d%s", status, p.IP, p.Port, responseTime)
}

// ValidateProxy 验证代理信息
func (p *ProxyInfo) ValidateProxy() error {
	if net.ParseIP(p.IP) == nil {
		return fmt.Errorf("无效的IP地址")
	}

	if p.Port < 1 || p.Port > 65535 {
		return fmt.Errorf("无效的端口")
	}

	if p.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}

	if p.Password == "" {
		return fmt.Errorf("密码不能为空")
	}

	if _, err := time.Parse("2006-01-02", p.ExpireDate); err != nil {
		return fmt.Errorf("无效的过期时间格式")
	}

	return nil
}

// 百度IP地址查询API响应结构
type BaiduIPResponse struct {
	Status string `json:"status"`
	Data   []struct {
		Location string `json:"location"`
		OrigIP   string `json:"origip"`
	} `json:"data"`
}

// GetCountryByIP 根据IP地址获取地区信息（使用百度API）
func GetCountryByIP(ip string) string {
	// 创建HTTP客户端，设置超时时间
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 构建百度IP查询API请求URL
	url := fmt.Sprintf("http://opendata.baidu.com/api.php?query=%s&co=&resource_id=6006&oe=utf8", ip)

	// 发送GET请求
	resp, err := client.Get(url)
	if err != nil {
		logs.Warn("获取IP地区信息失败:", err)
		return ""
	}
	defer resp.Body.Close()

	// 解析响应
	var baiduResp BaiduIPResponse
	if err := json.NewDecoder(resp.Body).Decode(&baiduResp); err != nil {
		logs.Warn("解析IP地区信息失败:", err)
		return ""
	}

	// 检查响应状态
	if baiduResp.Status != "0" {
		logs.Warn("IP地区查询失败:", ip, "status:", baiduResp.Status)
		return ""
	}

	// 检查是否有数据
	if len(baiduResp.Data) == 0 {
		logs.Warn("IP地区查询无数据:", ip)
		return ""
	}

	// 获取地区信息
	location := baiduResp.Data[0].Location

	// 去除ISP信息，只保留地区信息
	// 例如："西藏自治区拉萨市 电信" -> "西藏自治区拉萨市"
	if strings.Contains(location, " ") {
		parts := strings.Split(location, " ")
		location = parts[0]
	}

	logs.Info("IP地区识别成功:", ip, "->", location)
	return location
}
