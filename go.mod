module wechatdll

go 1.23.0

toolchain go1.23.2

require (
	github.com/PuerkitoBio/goquery v1.10.3
	github.com/astaxie/beego v1.12.3
	github.com/bitly/go-simplejson v0.5.1
	github.com/boombuler/barcode v1.0.2
	github.com/chromedp/chromedp v0.13.6
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/gogf/guuid v1.1.0
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.4
	github.com/gorilla/websocket v1.5.3
	github.com/lunny/log v0.0.0-20160921050905-7887c61bf0de
	github.com/sirupsen/logrus v1.9.3
	golang.org/x/crypto v0.40.0
	golang.org/x/net v0.42.0
	golang.org/x/sys v0.34.0
	google.golang.org/protobuf v1.36.6
)

require github.com/go-json-experiment/json v0.0.0-20250211171154-1ae217ad3535 // indirect

require (
	github.com/andybalholm/cascadia v1.3.3 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/chromedp/cdproto v0.0.0-20250403032234-65de8f5d025b // indirect
	github.com/chromedp/sysutil v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.9.0 // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/gobwas/ws v1.4.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/prometheus/client_golang v1.22.0 // indirect
	github.com/prometheus/client_model v0.6.2 // indirect
	github.com/prometheus/common v0.63.0 // indirect
	github.com/prometheus/procfs v0.16.0 // indirect
	github.com/shiena/ansicolor v0.0.0-20230509054315-a9deabde6e02 // indirect
	golang.org/x/text v0.27.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
