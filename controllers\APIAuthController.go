package controllers

import (
	"crypto/subtle"
	"encoding/json"
	"fmt"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// APIAuthController API密钥验证控制器
type APIAuthController struct {
	BaseController
}

// VerifyKeyRequest 验证密钥请求
type VerifyKeyRequest struct {
	APIKey     string `json:"api_key"`     // API密钥
	RememberMe bool   `json:"remember_me"` // 是否记住密钥（可选）
}

// VerifyKeyResponse 验证密钥响应
type VerifyKeyResponse struct {
	Success   bool   `json:"success"`    // 验证是否成功
	Message   string `json:"message"`    // 响应消息
	RedirectURL string `json:"redirect_url,omitempty"` // 重定向URL（成功时）
}

// AuthPage 显示密钥验证页面
//
// @Summary 显示API密钥验证页面
// @Description 当用户访问API文档但未提供有效密钥时，显示此验证页面
// @Tags Auth
// @Param redirect query string false "验证成功后的重定向URL"
// @Success 200 {string} string "HTML验证页面"
// @router /auth [get]
func (c *APIAuthController) AuthPage() {
	// 获取重定向URL参数
	redirectURL := c.GetString("redirect", "/api")
	
	// 检查是否已经有有效的密钥
	config := comm.GetAPIDocsConfig()
	if config.KeyEnabled {
		// 从Cookie中获取密钥
		if cookie, err := c.Ctx.Request.Cookie("api_key"); err == nil {
			if subtle.ConstantTimeCompare([]byte(cookie.Value), []byte(config.Key)) == 1 {
				// 密钥有效，直接重定向
				c.Redirect(redirectURL, 302)
				return
			}
		}
	}

	html := c.generateAuthPageHTML(redirectURL)
	c.Ctx.ResponseWriter.Header().Set("Content-Type", "text/html; charset=utf-8")
	c.Ctx.WriteString(html)
}

// VerifyKey 验证API密钥
//
// @Summary 验证API密钥
// @Description 验证用户提供的API密钥是否正确
// @Tags Auth
// @Param body body VerifyKeyRequest true "验证请求"
// @Success 200 {object} models.StandardResponse{data=VerifyKeyResponse} "验证结果"
// @Failure 400 {object} models.StandardResponse "参数错误"
// @Failure 401 {object} models.StandardResponse "密钥无效"
// @router /verify [post]
func (c *APIAuthController) VerifyKey() {
	var req VerifyKeyRequest

	// 添加调试信息
	log.WithFields(log.Fields{
		"content_type": c.Ctx.Input.Header("Content-Type"),
		"body_length":  len(c.Ctx.Input.RequestBody),
		"body_content": string(c.Ctx.Input.RequestBody),
	}).Debug("收到验证请求")

	// 检查请求体是否为空
	if len(c.Ctx.Input.RequestBody) == 0 {
		result := models.StandardResponse{
			Code:    -1,
			Success: false,
			Message: "请求体为空",
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result := models.StandardResponse{
			Code:    -1,
			Success: false,
			Message: fmt.Sprintf("参数解析失败：%v，请求体内容：%s", err.Error(), string(c.Ctx.Input.RequestBody)),
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 验证密钥是否为空
	if req.APIKey == "" {
		result := models.StandardResponse{
			Code:    -2,
			Success: false,
			Message: "API密钥不能为空",
			Data:    nil,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 获取配置的密钥
	config := comm.GetAPIDocsConfig()
	if !config.KeyEnabled {
		// 如果未启用密钥验证，直接返回成功
		response := VerifyKeyResponse{
			Success: true,
			Message: "密钥验证已禁用",
		}
		result := models.StandardResponse{
			Code:    0,
			Success: true,
			Message: "验证成功",
			Data:    response,
		}
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 使用常量时间比较防止时序攻击
	if subtle.ConstantTimeCompare([]byte(req.APIKey), []byte(config.Key)) != 1 {
		// 记录验证失败日志
		log.WithFields(log.Fields{
			"ip":  c.Ctx.Input.IP(),
			"key": maskKey(req.APIKey),
		}).Warn("API密钥验证失败")

		result := models.StandardResponse{
			Code:    -3,
			Success: false,
			Message: "API密钥无效",
			Data:    nil,
		}
		c.Ctx.ResponseWriter.WriteHeader(401)
		c.Data["json"] = &result
		c.ServeJSON()
		return
	}

	// 验证成功，设置Cookie
	cookieExpiry := 3600 * 24 // 默认24小时
	if req.RememberMe {
		cookieExpiry = 3600 * 24 * 30 // 记住密钥30天
	}

	// 设置Cookie（根据环境决定是否使用Secure标志）
	isProduction := beego.BConfig.RunMode == "prod"
	c.Ctx.SetCookie("api_key", req.APIKey, cookieExpiry, "/", "", isProduction, true)

	// 记录验证成功日志
	log.WithFields(log.Fields{
		"ip":          c.Ctx.Input.IP(),
		"remember_me": req.RememberMe,
		"expiry_days": cookieExpiry / (3600 * 24),
	}).Info("API密钥验证成功")

	// 返回成功响应
	response := VerifyKeyResponse{
		Success: true,
		Message: "密钥验证成功",
	}

	result := models.StandardResponse{
		Code:    0,
		Success: true,
		Message: "验证成功",
		Data:    response,
	}

	c.Data["json"] = &result
	c.ServeJSON()
}

// generateAuthPageHTML 生成验证页面HTML
func (c *APIAuthController) generateAuthPageHTML(redirectURL string) string {
	return fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API密钥验证 - WeChat API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-container {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%%;
            max-width: 450px;
            animation: slideUp 0.5s ease-out;
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .auth-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
        }
        
        .auth-header p {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%%;
            padding: 0.875rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 0.5rem;
        }
        
        .checkbox-group label {
            margin-bottom: 0;
            font-weight: normal;
            color: #666;
            cursor: pointer;
        }
        
        .btn {
            width: 100%%;
            padding: 0.875rem;
            background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn .loading {
            display: none;
        }
        
        .btn.loading .loading {
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .btn.loading .text {
            display: none;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: #fdf2f2;
            border: 1px solid #fecaca;
            border-radius: 5px;
            display: none;
        }
        
        .success-message {
            color: #059669;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 5px;
            display: none;
        }
        
        .footer {
            text-align: center;
            margin-top: 2rem;
            color: #666;
            font-size: 0.85rem;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0%% { transform: rotate(0deg); }
            100%% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .auth-container {
                padding: 2rem 1.5rem;
                margin: 10px;
            }

            .auth-header h1 {
                font-size: 1.5rem;
            }

            .form-group input {
                padding: 0.75rem;
                font-size: 0.95rem;
            }

            .btn {
                padding: 0.75rem;
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>🔐 API密钥验证</h1>
            <p>请输入API密钥以访问文档</p>
        </div>
        
        <form id="auth-form">
            <div class="form-group">
                <label for="api-key">API密钥</label>
                <input type="password" id="api-key" name="api_key" placeholder="请输入API密钥" required>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="remember-me" name="remember_me">
                <label for="remember-me">记住密钥（30天）</label>
            </div>
            
            <button type="submit" class="btn" id="submit-btn">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
                <span class="text">验证并进入</span>
            </button>
            
            <div class="error-message" id="error-message"></div>
            <div class="success-message" id="success-message"></div>
        </form>
        
        <div class="footer">
            <p>WeChat API Documentation</p>
        </div>
    </div>
    
    <script>
        document.getElementById('auth-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('api-key').value;
            const rememberMe = document.getElementById('remember-me').checked;
            const submitBtn = document.getElementById('submit-btn');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');
            
            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            if (!apiKey.trim()) {
                showError('请输入API密钥');
                return;
            }
            
            // 显示加载状态
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            
            // 发送验证请求
            fetch('/api/auth/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey,
                    remember_me: rememberMe
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('验证成功，正在跳转...');
                    setTimeout(() => {
                        window.location.href = '%s';
                    }, 1000);
                } else {
                    showError(data.message || '验证失败');
                }
            })
            .catch(error => {
                console.error('验证请求失败:', error);
                showError('网络错误，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            });
        });
        
        function showError(message) {
            const errorMessage = document.getElementById('error-message');
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }
        
        function showSuccess(message) {
            const successMessage = document.getElementById('success-message');
            successMessage.textContent = message;
            successMessage.style.display = 'block';
        }
        
        // 页面加载时自动聚焦到密钥输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('api-key').focus();

            // 添加键盘事件监听
            document.getElementById('api-key').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('auth-form').dispatchEvent(new Event('submit'));
                }
            });

            // 清除URL中的错误参数
            if (window.location.search.includes('error=')) {
                const url = new URL(window.location);
                url.searchParams.delete('error');
                window.history.replaceState({}, document.title, url.toString());
            }
        });
    </script>
</body>
</html>`, redirectURL)
}

// maskKey 遮蔽密钥用于日志记录
func maskKey(key string) string {
	if len(key) <= 8 {
		return "****"
	}
	return key[:2] + "****" + key[len(key)-2:]
}
