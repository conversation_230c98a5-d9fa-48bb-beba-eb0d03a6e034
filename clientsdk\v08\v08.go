package v08

import (
	"fmt"
	"runtime"
	"sync"
	"wechatdll/clientsdk/dynlib"

	"github.com/lunny/log"
)

var (
	lib         dynlib.DynamicLibrary
	mu          sync.Mutex
	isInit      bool = false
	initOnce    sync.Once
	callMu      sync.Mutex         // 保护对lib.Call的调用
	libLoadFail bool       = false // 标记动态库是否加载失败
)

func init() {
	fmt.Printf("init v08: %v\n", runtime.GOOS)
	var libPath string
	switch runtime.GOOS {
	case "windows":
		libPath = "lib\\v08.dll"
	case "linux":
		libPath = "lib/libv08.so"
	default:
		panic("unsupported platform")
	}
	fmt.Println(libPath)
	//libPath = "D:\\v08.dll" //test
	var err error

	// 使用defer和recover来捕获任何可能的panic
	defer func() {
		if r := recover(); r != nil {
			libLoadFail = true
			lib = nil
			log.Error("动态库初始化时发生panic: ", r)
			log.Warn("动态库加载失败，程序将使用默认值运行，某些功能可能受限")
		}
	}()

	lib, err = dynlib.NewLibrary(libPath)
	if err != nil {
		libLoadFail = true
		log.Error("Failed to load library: ", err)
		log.Warn("动态库加载失败，程序将使用默认值运行，某些功能可能受限")
		lib = nil // 确保lib为nil
	}
}

//
//func Rqtx(md5 string) uint32 {
//	if !isInit {
//		mu.Lock()
//		defer mu.Unlock()
//	}
//	ret, _, _ := lib.Call("rqtx", md5)
//	log.Info("Rqtx returned: ", ret)
//	isInit = true
//	return uint32(ret)
//}

func ensureInit() {
	initOnce.Do(func() {
		mu.Lock()
		defer mu.Unlock()
		// 初始化逻辑
		isInit = true
	})
}

// Rqtx是对动态库函数的封装
func Rqtx(md5 string) uint32 {
	ensureInit()

	// 检查动态库是否成功加载
	if lib == nil {
		log.Debug("动态库未成功加载，返回默认值")
		return 0
	}

	// 确保lib.Call在同一时间只能被一个goroutine调用
	callMu.Lock()
	defer callMu.Unlock()

	ret, _, _ := lib.Call("rqtx", md5)
	//defer res.Release()
	return uint32(ret)
}

// encode string
func EncodeString(input string, constant uint32, timestamp uint32) string {
	// 检查动态库是否成功加载
	if lib == nil {
		log.Debug("动态库未成功加载，返回原始字符串")
		return input
	}

	len := uint32(len(input))
	buff := make([]byte, len)
	copy(buff, []byte(input))
	out := make([]byte, len)
	lib.Call("encode_cstr", buff, len, out, constant, timestamp)
	return string(out)
}

// encode uint64
func EncodeUInt64(input uint64, constant uint32, timestamp uint32) uint64 {
	// 检查动态库是否成功加载
	if lib == nil {
		log.Debug("动态库未成功加载，返回原始值")
		return input
	}

	input_v := input
	out := uint64(0)
	lib.Call("encode_int64", &input_v, timestamp, constant, &out)
	return out
}

// config len(config) = 128
func Si(config string, data string) string {
	return ""
	// si_out := make([]byte, 33)
	// lib.Call("si_cstr", []byte(config), len(config), []byte(data), len(data), si_out)
	// return string(si_out)
}
