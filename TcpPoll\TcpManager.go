//go:build linux
// +build linux

package TcpPoll

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/comm"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

type TcpManager struct {
	running       int32                 // 使用原子操作控制是否消息loop (0=停止, 1=运行)
	connections   map[string]*TcpClient // 以关键字为key的连接池, 用于发送
	fdConnections map[int]*TcpClient    // 以fd为key的连接池, 用于接收
	poll          *epoll
	mutex         sync.RWMutex          // 保护连接池的读写锁
	ctx           context.Context       // 用于优雅关闭
	cancel        context.CancelFunc    // 取消函数
}

// TcpManager单例, 使用sync.Once.Do解决并发时多次创建
var once sync.Once
var instance *TcpManager

// 获取单例Tcp
func GetTcpManager() (*TcpManager, error) {
	var err error
	longLinkEnabled, _ := beego.AppConfig.Bool("longlinkenabled")
	if !longLinkEnabled {
		return nil, errors.New("不支持长连接请求")
	}
	once.Do(func() {
		var epollInstance *epoll
		epollInstance, err = MkEpoll()
		if err != nil {
			log.Errorf("创建epoll实例失败: %v", err)
			return
		}

		ctx, cancel := context.WithCancel(context.Background())
		instance = &TcpManager{
			running:       1, // 使用原子操作，1表示运行中
			connections:   make(map[string]*TcpClient),
			fdConnections: make(map[int]*TcpClient),
			poll:          epollInstance,
			ctx:           ctx,
			cancel:        cancel,
		}

		log.Info("TcpManager 单例创建成功")
	})
	return instance, err
}

// 队列增加长连接. key: 关键字, client: TcpClient
func (manager *TcpManager) Add(key string, client *TcpClient) error {
	if client == nil || client.conn == nil {
		return errors.New("client 或 connection 为空")
	}

	fd, err := manager.poll.Add(client.conn) // 将长连接增加到epoll
	if err != nil {
		log.Errorf("添加连接到epoll失败: %v", err)
		return fmt.Errorf("添加连接到epoll失败: %w", err)
	}

	// 使用锁保护连接池
	manager.mutex.Lock()
	defer manager.mutex.Unlock()

	// 增加对照表
	manager.connections[key] = client
	manager.fdConnections[fd] = client

	log.Debugf("成功添加连接: key=%s, fd=%d", key, fd)
	return nil
}

// 队列移除长连接. client: TcpClient
func (manager *TcpManager) Remove(client *TcpClient) {
	if client == nil {
		log.Warn("尝试移除空的client")
		return
	}

	var fd int
	var wxid string

	// 安全获取连接信息
	if client.conn != nil {
		fd = socketFD(client.conn)
	}
	if client.model != nil {
		wxid = client.model.Wxid
	}

	// 使用锁保护连接池
	manager.mutex.Lock()
	defer manager.mutex.Unlock()

	// 从epoll中移除
	if client.conn != nil {
		if err := manager.poll.Remove(client.conn); err != nil {
			log.Errorf("从epoll移除连接失败: %v", err)
		}
	}

	// 从连接池中移除
	if wxid != "" {
		delete(manager.connections, wxid)
	}
	if fd != 0 {
		delete(manager.fdConnections, fd)
	}

	// 终止客户端连接
	client.Terminate()

	log.Debugf("成功移除连接: wxid=%s, fd=%d", wxid, fd)
}

// 创建长连接并添加到epoll.
func (manager *TcpManager) GetClient(loginData *comm.LoginData, businessFunc BusinessFunc) (*TcpClient, error) {
	if loginData == nil {
		return nil, errors.New("loginData 不能为空")
	}

	// 使用读锁检查是否存在已有连接
	manager.mutex.RLock()
	client, ok := manager.connections[loginData.Wxid]
	manager.mutex.RUnlock()

	if ok {
		// 更新现有连接的配置
		client.model = loginData
		if businessFunc != nil {
			client.callbackMsg = businessFunc
		}
		log.Debugf("复用现有连接: wxid=%s", loginData.Wxid)
		return client, nil
	}

	// 检查LongHost
	if loginData.LongHost == "" {
		loginData.LongHost = Algorithm.MmtlsLongHost
		log.Debugf("使用默认LongHost: %s", loginData.LongHost)
	}

	// 创建新的连接
	client = NewTcpClient(loginData)
	if businessFunc != nil {
		client.callbackMsg = businessFunc
	}

	// 建立连接
	if err := client.Connect(); err != nil {
		log.Errorf("建立连接失败: %v", err)
		return nil, fmt.Errorf("建立连接失败: %w", err)
	}

	// 将完成连接的client添加到epoll
	if err := manager.Add(loginData.Wxid, client); err != nil {
		client.Terminate() // 清理资源
		return nil, fmt.Errorf("添加到epoll失败: %w", err)
	}

	// 等待握手完成，使用context控制超时
	timeoutSpan, _ := time.ParseDuration(beego.AppConfig.String("longlinkconnecttimeout"))
	if timeoutSpan == 0 {
		timeoutSpan = 30 * time.Second // 默认30秒超时
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeoutSpan)
	defer cancel()

	// 使用channel和goroutine进行非阻塞等待
	handshakeDone := make(chan bool, 1)
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				if atomic.LoadInt32(&client.handshaking) == 0 {
					handshakeDone <- true
					return
				}
				time.Sleep(100 * time.Millisecond)
			}
		}
	}()

	// 等待握手完成或超时
	select {
	case <-handshakeDone:
		log.Debugf("握手成功完成: wxid=%s", loginData.Wxid)
		return client, nil
	case <-ctx.Done():
		// 超时没有完成握手, 清理资源并报错
		log.Errorf("握手超时: wxid=%s", loginData.Wxid)
		client.SetStartTime(time.Now().UnixNano()) // 重置时间，关闭旧的 tcp 心跳
		manager.Remove(client)
		return nil, errors.New("mmtls 握手超时")
	}
}

// 循环接收消息
func (manager *TcpManager) RunEventLoop() {
	log.Info("开始运行事件循环")
	defer log.Info("事件循环已停止")

	// 使用原子操作检查运行状态
	for atomic.LoadInt32(&manager.running) == 1 {
		select {
		case <-manager.ctx.Done():
			log.Info("收到停止信号，退出事件循环")
			return
		default:
			// 继续处理事件
		}

		// 等待epoll事件，设置较短的超时以便及时响应停止信号
		fds, waitErr := manager.poll.Wait()
		if waitErr != nil {
			log.Errorf("epoll wait 失败: %v", waitErr)
			time.Sleep(100 * time.Millisecond) // 避免忙等待
			continue
		}

		if len(fds) == 0 {
			time.Sleep(10 * time.Millisecond) // 减少CPU占用
			continue
		}

		// 并发处理多个连接的消息
		manager.processEvents(fds)
	}
}

// processEvents 并发处理事件
func (manager *TcpManager) processEvents(fds []int) {
	// 使用读锁保护fdConnections
	manager.mutex.RLock()
	defer manager.mutex.RUnlock()

	// 为每个有事件的fd启动goroutine处理
	for _, fd := range fds {
		client := manager.fdConnections[fd]
		if client == nil {
			log.Warnf("未找到fd对应的client: %d", fd)
			continue
		}

		// 异步处理消息，避免阻塞事件循环
		go func(c *TcpClient, fdNum int) {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("处理消息时发生panic: fd=%d, error=%v", fdNum, r)
				}
			}()
			c.Once()
		}(client, fd)
	}
}

// Stop 优雅停止TcpManager
func (manager *TcpManager) Stop() {
	log.Info("开始停止TcpManager")

	// 设置停止标志
	atomic.StoreInt32(&manager.running, 0)

	// 取消context
	if manager.cancel != nil {
		manager.cancel()
	}

	// 关闭所有连接
	manager.mutex.Lock()
	defer manager.mutex.Unlock()

	for wxid, client := range manager.connections {
		log.Debugf("关闭连接: %s", wxid)
		client.Terminate()
	}

	// 清空连接池
	manager.connections = make(map[string]*TcpClient)
	manager.fdConnections = make(map[int]*TcpClient)

	log.Info("TcpManager 已停止")
}
