# 微信 API 服务

## 环境要求

### Go 版本

- **Go 1.21** 或更高版本
- 支持 CGO（部分功能需要）

### 动态库文件

项目依赖的动态库文件位于 `lib/` 目录：

- `lib/libv08.so` - Linux 平台动态库
- `lib/v08.dll` - Windows 平台动态库
- `lib/libz.so` - Linux 压缩库
- `lib/zlib.dll` - Windows 压缩库

### Windows 开发环境设置

在 Windows 上进行开发时，建议安装：

- [TDM-GCC](https://jmeubank.github.io/tdm-gcc/) 或 [MinGW-w64](https://www.mingw-w64.org/)（CGO 支持）
- PowerShell 5.0+ 或 PowerShell Core（推荐使用 PowerShell 命令）

## 编译说明

### Windows 平台编译

在 Windows 系统上编译本地可执行文件：

```powershell
$env:CGO_ENABLED=1; $env:GOOS="windows"; $env:GOARCH="amd64"; go build -tags windows -o wechat_win.exe .
```

### Linux 平台编译

```powershell
CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -tags linux -o wechat_linux .
```

## AIR 热重载开发

### 安装 AIR

```bash
# 安装 AIR 工具
go install github.com/cosmtrek/air@latest
```

### 配置 AIR

项目根目录下需要创建 `.air.toml` 配置文件：

```toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
  bin = "./tmp/main.exe"
  cmd = "go build -o ./tmp/main.exe ."
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "docs", "lib"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html", "conf"]
  include_file = []
  kill_delay = "0s"
  log = "build-errors.log"
  poll = false
  poll_interval = 0
  rerun = false
  rerun_delay = 500
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
  keep_scroll = true
```

### 启动 AIR 开发服务

```bash
# 启动 AIR 热重载开发
air
```

**AIR 功能特性：**

- 自动监控 `.go`、`.conf`、`.html` 等文件变化
- 文件变化时自动重新编译并重启程序
- 支持彩色日志输出，便于调试
- 编译错误会保存到 `tmp/build-errors.log`

### AIR 故障排除

**如遇到平台兼容问题：**

1. **清理临时文件：**

   ```powershell
   Remove-Item -Path "tmp\*" -Force -ErrorAction SilentlyContinue
   ```

2. **重新启动 AIR：**

   ```bash
   air
   ```

3. **检查配置文件：**

   - 确认 `.air.toml` 文件存在且格式正确
   - 确认 `bin` 路径使用正确的可执行文件扩展名（Windows: `.exe`）

4. **手动测试编译：**

   ```bash
   go build -o ./tmp/main.exe .
   ```

**常见问题：**

- 如果提示"指定的可执行文件不是此操作系统平台的有效应用程序"，通常是交叉编译导致的平台不匹配
- 确保没有设置错误的 `GOOS` 或 `GOARCH` 环境变量
- Windows 开发时确保使用 `.exe` 扩展名

## 使用说明

### 配置文件

程序使用统一的配置文件，位于 `conf/` 目录下：

- `app.conf` - 统一配置文件，支持多种部署模式
- `.env.example` - 环境变量配置示例文件

#### 部署模式

程序支持以下部署模式，通过 `deploy_mode` 配置项或 `DEPLOY_MODE` 环境变量指定：

- `local` - 本地开发模式（默认）
- `http` - HTTP 服务模式
- `tcp` - TCP 服务模式
- `docker` - Docker 容器部署模式

### 启动服务

#### Windows 平台

```bash
wechat_win.exe
```

#### Linux 平台

```bash
# 给予执行权限
chmod +x wechat_linux

# 使用默认配置启动
./wechat_linux

# 或使用指定配置文件
./wechat_linux -conf=conf/app.conf
```

### 访问服务

服务启动后，可以通过以下方式访问：

- **服务首页**：`http://localhost:8059`
- **API 文档**：`http://localhost:8059/api`

> 注意：端口号根据配置文件中的 `httpport` 设置而定

### 配置说明

#### 环境变量配置

推荐使用环境变量来配置应用，特别是在生产环境中：

```bash
# 复制环境变量示例文件
cp conf/.env.example conf/.env

# 编辑环境变量文件
nano conf/.env
```

#### 主要配置项

```ini
# 部署模式
deploy_mode = "local"    # local/http/tcp/vercel/docker

# 服务配置
httpaddr = "0.0.0.0"     # 监听地址
httpport = 8059          # HTTP端口
wsport = 8088           # WebSocket端口
runmode = dev           # 运行模式：dev/prod

# Redis配置（必需，vercel模式除外）
redislink = "127.0.0.1:6379"
redispass = "your_password"
redisdbnum = 2

# API安全
api_access_key = "your_api_key"
api_key_enabled = true

# 长连接配置
longlinkenabled = true
longlinkconnecttimeout = "10m"
```

#### 环境变量覆盖

所有配置项都可以通过环境变量覆盖：

```bash
# 服务器配置
HTTP_ADDR=0.0.0.0
HTTP_PORT=8080
WS_PORT=8088
RUN_MODE=prod

# Redis配置
REDIS_LINK=redis.example.com:6379
REDIS_PASS=your_redis_password
REDIS_DB=2

# API安全配置
API_ACCESS_KEY=your_secure_api_key
API_KEY_ENABLED=true

# 部署模式
DEPLOY_MODE=docker
```

#### 访问 API 文档

API 文档需要密钥验证，有以下几种方式：

1. **URL 参数**：`http://localhost:8059/api?api_key=your_api_key`
2. **Cookie**：设置名为 `api_key` 的 Cookie
3. **Header**：添加 `api_key` 请求头

### 常见问题

**Q: 服务启动失败？**

- 检查端口是否被占用
- 确认 Redis 服务是否正常运行
- 检查配置文件格式是否正确

**Q: 无法访问 API 文档？**

- 确认 API 密钥是否正确
- 检查防火墙设置
- 确认服务是否正常启动

**Q: 如何修改配置？**

- 可以直接编辑配置文件
- 或通过 API 文档页面的配置管理功能在线修改

## Docker 部署

### 使用 Docker Compose（推荐）

#### 启动默认服务

```bash
# 启动主服务和 Redis
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f wxapi
```

#### 启动特定模式

```bash
# 启动 HTTP 模式
docker-compose --profile http up -d

# 启动 TCP 模式
docker-compose --profile tcp up -d

# 停止所有服务
docker-compose down
```

### 手动 Docker 构建

#### 构建镜像

```bash
# 构建默认镜像
docker build -t wxapi:latest .

# 构建 HTTP 模式镜像
docker build -f Dockerfile.http -t wxapi:http .

# 构建 TCP 模式镜像
docker build -f Dockerfile.tcp -t wxapi:tcp .
```

#### 运行容器

```bash
# 运行默认服务
docker run -d \
  --name wxapi \
  -p 8059:8059 \
  -p 8088:8088 \
  -v $(pwd)/conf:/app/conf:ro \
  -v $(pwd)/logs:/app/logs \
  wxapi:latest

# 运行 HTTP 模式
docker run -d \
  --name wxapi-http \
  -p 8005:8005 \
  -v $(pwd)/conf:/app/conf:ro \
  -v $(pwd)/logs:/app/logs \
  wxapi:http

# 运行 TCP 模式
docker run -d \
  --name wxapi-tcp \
  -p 8057:8057 \
  -v $(pwd)/conf:/app/conf:ro \
  -v $(pwd)/logs:/app/logs \
  wxapi:tcp
```

### Docker 配置说明

- **多阶段构建**：减小最终镜像大小
- **非 root 用户**：提高安全性
- **健康检查**：自动监控服务状态
- **时区设置**：使用上海时区
- **卷挂载**：配置文件只读，日志文件可写

### Docker 常见问题

**Q: 容器启动失败？**

- 检查端口是否被占用：`docker ps` 和 `netstat -tlnp`
- 查看容器日志：`docker logs <container_name>`
- 检查配置文件路径和权限

**Q: 无法访问服务？**

- 确认端口映射正确
- 检查防火墙设置
- 确认容器网络配置

## 自动功能

### 自动通过好友请求

项目新增了自动通过好友请求功能，可以自动检测并通过微信好友请求消息。

#### 功能特性

- ✅ 自动识别好友请求消息（msgType: 10000）
- ✅ 自动解析请求信息（用户名、验证票据、来源场景）
- ✅ 自动调用通过接口
- ✅ 支持多种来源场景（微信号、群聊、手机号、二维码等）
- ✅ 实时 WebSocket 通知
- ✅ Redis 状态持久化
- ✅ 批量操作支持

#### 快速使用

**开启自动通过好友请求：**

```bash
curl -X POST http://localhost:8059/api/AutoFriend/SetStatus \
  -H "Content-Type: application/json" \
  -d '{
    "wxid": "your_wxid",
    "enable": true
  }'
```

**查询状态：**

```bash
curl -X POST http://localhost:8059/api/AutoFriend/GetStatus \
  -H "Content-Type: application/json" \
  -d '{
    "wxid": "your_wxid"
  }'
```

**关闭功能：**

```bash
curl -X POST http://localhost:8059/api/AutoFriend/SetStatus \
  -H "Content-Type: application/json" \
  -d '{
    "wxid": "your_wxid",
    "enable": false
  }'
```

#### API 接口

- `POST /api/AutoFriend/SetStatus` - 设置自动通过好友请求状态
- `POST /api/AutoFriend/GetStatus` - 查询自动通过好友请求状态
- `POST /api/AutoFriend/BatchSetStatus` - 批量设置状态
- `GET /api/AutoFriend/GetInfo` - 获取功能说明

#### 注意事项

⚠️ **安全提醒**：开启后将自动通过所有好友请求，建议谨慎使用，避免添加恶意用户

📝 **详细文档**：查看 `docs/auto_friend_api.md` 获取完整的 API 文档和使用说明

🧪 **功能测试**：运行 `go run scripts/test_auto_friend.go` 进行功能测试
