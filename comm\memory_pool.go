package comm

import (
	"strings"
	"sync"
	"time"
)

// MemoryPool 内存池管理器
type MemoryPool struct {
	// 字节切片池
	byteSlicePools map[int]*sync.Pool

	// 字符串构建器池
	stringBuilderPool *sync.Pool

	// 响应对象池
	responsePool *sync.Pool

	// 请求对象池
	requestPool *sync.Pool

	// 统计信息
	stats *PoolStats
	mu    sync.RWMutex
}

// PoolStats 池统计信息
type PoolStats struct {
	ByteSliceGets     int64 `json:"byte_slice_gets"`
	ByteSlicePuts     int64 `json:"byte_slice_puts"`
	StringBuilderGets int64 `json:"string_builder_gets"`
	StringBuilderPuts int64 `json:"string_builder_puts"`
	ResponseGets      int64 `json:"response_gets"`
	ResponsePuts      int64 `json:"response_puts"`
	RequestGets       int64 `json:"request_gets"`
	RequestPuts       int64 `json:"request_puts"`
}

var (
	globalMemoryPool *MemoryPool
	memoryPoolOnce   sync.Once
)

// GetMemoryPool 获取全局内存池
func GetMemoryPool() *MemoryPool {
	memoryPoolOnce.Do(func() {
		globalMemoryPool = NewMemoryPool()
	})
	return globalMemoryPool
}

// NewMemoryPool 创建新的内存池
func NewMemoryPool() *MemoryPool {
	mp := &MemoryPool{
		byteSlicePools: make(map[int]*sync.Pool),
		stats:          &PoolStats{},
	}

	// 初始化常用大小的字节切片池
	sizes := []int{64, 128, 256, 512, 1024, 2048, 4096, 8192}
	for _, size := range sizes {
		s := size // 捕获循环变量
		mp.byteSlicePools[size] = &sync.Pool{
			New: func() interface{} {
				return make([]byte, 0, s)
			},
		}
	}

	// 字符串构建器池
	mp.stringBuilderPool = &sync.Pool{
		New: func() interface{} {
			return &strings.Builder{}
		},
	}

	// 响应对象池
	mp.responsePool = &sync.Pool{
		New: func() interface{} {
			return &APIResponse{}
		},
	}

	// 请求对象池
	mp.requestPool = &sync.Pool{
		New: func() interface{} {
			return &APIRequest{}
		},
	}

	return mp
}

// GetByteSlice 获取字节切片
func (mp *MemoryPool) GetByteSlice(size int) []byte {
	mp.mu.Lock()
	mp.stats.ByteSliceGets++
	mp.mu.Unlock()

	// 找到最接近的池大小
	poolSize := mp.findNearestPoolSize(size)
	if pool, exists := mp.byteSlicePools[poolSize]; exists {
		slice := pool.Get().([]byte)
		return slice[:0] // 重置长度但保持容量
	}

	// 如果没有合适的池，直接分配
	return make([]byte, 0, size)
}

// PutByteSlice 归还字节切片
func (mp *MemoryPool) PutByteSlice(slice []byte) {
	if slice == nil {
		return
	}

	mp.mu.Lock()
	mp.stats.ByteSlicePuts++
	mp.mu.Unlock()

	capacity := cap(slice)
	poolSize := mp.findNearestPoolSize(capacity)

	if pool, exists := mp.byteSlicePools[poolSize]; exists && capacity <= poolSize*2 {
		// 只有当容量不超过池大小的2倍时才归还，避免内存浪费
		pool.Put(slice[:0])
	}
}

// GetStringBuilder 获取字符串构建器
func (mp *MemoryPool) GetStringBuilder() *strings.Builder {
	mp.mu.Lock()
	mp.stats.StringBuilderGets++
	mp.mu.Unlock()

	sb := mp.stringBuilderPool.Get().(*strings.Builder)
	sb.Reset()
	return sb
}

// PutStringBuilder 归还字符串构建器
func (mp *MemoryPool) PutStringBuilder(sb *strings.Builder) {
	if sb == nil {
		return
	}

	mp.mu.Lock()
	mp.stats.StringBuilderPuts++
	mp.mu.Unlock()

	// 如果构建器太大，不归还到池中
	if sb.Cap() <= 4096 {
		mp.stringBuilderPool.Put(sb)
	}
}

// GetResponse 获取响应对象
func (mp *MemoryPool) GetResponse() *APIResponse {
	mp.mu.Lock()
	mp.stats.ResponseGets++
	mp.mu.Unlock()

	resp := mp.responsePool.Get().(*APIResponse)
	resp.Reset()
	return resp
}

// PutResponse 归还响应对象
func (mp *MemoryPool) PutResponse(resp *APIResponse) {
	if resp == nil {
		return
	}

	mp.mu.Lock()
	mp.stats.ResponsePuts++
	mp.mu.Unlock()

	mp.responsePool.Put(resp)
}

// GetRequest 获取请求对象
func (mp *MemoryPool) GetRequest() *APIRequest {
	mp.mu.Lock()
	mp.stats.RequestGets++
	mp.mu.Unlock()

	req := mp.requestPool.Get().(*APIRequest)
	req.Reset()
	return req
}

// PutRequest 归还请求对象
func (mp *MemoryPool) PutRequest(req *APIRequest) {
	if req == nil {
		return
	}

	mp.mu.Lock()
	mp.stats.RequestPuts++
	mp.mu.Unlock()

	mp.requestPool.Put(req)
}

// findNearestPoolSize 找到最接近的池大小
func (mp *MemoryPool) findNearestPoolSize(size int) int {
	sizes := []int{64, 128, 256, 512, 1024, 2048, 4096, 8192}
	for _, s := range sizes {
		if size <= s {
			return s
		}
	}
	return 8192 // 最大池大小
}

// GetStats 获取统计信息
func (mp *MemoryPool) GetStats() *PoolStats {
	mp.mu.RLock()
	defer mp.mu.RUnlock()

	return &PoolStats{
		ByteSliceGets:     mp.stats.ByteSliceGets,
		ByteSlicePuts:     mp.stats.ByteSlicePuts,
		StringBuilderGets: mp.stats.StringBuilderGets,
		StringBuilderPuts: mp.stats.StringBuilderPuts,
		ResponseGets:      mp.stats.ResponseGets,
		ResponsePuts:      mp.stats.ResponsePuts,
		RequestGets:       mp.stats.RequestGets,
		RequestPuts:       mp.stats.RequestPuts,
	}
}

// APIResponse 可复用的API响应对象
type APIResponse struct {
	Success   bool        `json:"success"`
	Code      int64       `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// Reset 重置响应对象
func (r *APIResponse) Reset() {
	r.Success = false
	r.Code = 0
	r.Message = ""
	r.Data = nil
	r.RequestID = ""
	r.Timestamp = time.Time{}
}

// APIRequest 可复用的API请求对象
type APIRequest struct {
	Method    string            `json:"method"`
	Path      string            `json:"path"`
	Headers   map[string]string `json:"headers"`
	Body      []byte            `json:"body"`
	Timestamp time.Time         `json:"timestamp"`
}

// Reset 重置请求对象
func (r *APIRequest) Reset() {
	r.Method = ""
	r.Path = ""
	if r.Headers == nil {
		r.Headers = make(map[string]string)
	} else {
		for k := range r.Headers {
			delete(r.Headers, k)
		}
	}
	r.Body = r.Body[:0]
	r.Timestamp = time.Time{}
}

// 便捷函数
func GetByteSlice(size int) []byte {
	return GetMemoryPool().GetByteSlice(size)
}

func PutByteSlice(slice []byte) {
	GetMemoryPool().PutByteSlice(slice)
}

func GetStringBuilder() *strings.Builder {
	return GetMemoryPool().GetStringBuilder()
}

func PutStringBuilder(sb *strings.Builder) {
	GetMemoryPool().PutStringBuilder(sb)
}

func GetResponse() *APIResponse {
	return GetMemoryPool().GetResponse()
}

func PutResponse(resp *APIResponse) {
	GetMemoryPool().PutResponse(resp)
}
