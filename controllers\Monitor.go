package controllers

import (
	"fmt"
	"runtime"
	"strings"
	"time"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/astaxie/beego"
)

// MonitorController 监控控制器
type MonitorController struct {
	BaseController
}

// SendSuccessResponse 发送成功响应
func (c *MonitorController) SendSuccessResponse(data interface{}, message string) {
	result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: message,
		Data:    data,
	}
	c.Data["json"] = &result
	c.ServeJSON()
}

// LogOperation 记录操作日志
func (c *MonitorController) LogOperation(wxid, operation, description string) {
	// 这里可以添加操作日志记录逻辑
	// 暂时留空，避免编译错误
}

// HealthCheck 健康检查
// @Summary 系统健康检查
// @Description 获取系统健康状态和性能指标
// @Success 200 {object} models.StandardResponse
// @Router /api/Monitor/Health [get]
func (c *MonitorController) HealthCheck() {
	monitor := comm.GetMonitor()
	healthStatus := monitor.GetHealthStatus()

	c.SendSuccessResponse(healthStatus, "系统运行正常")
}

// GetStats 获取统计信息
// @Summary 获取系统统计信息
// @Description 获取详细的系统统计信息，包括API调用统计和用户活动统计
// @Success 200 {object} models.StandardResponse
// @Router /api/Monitor/Stats [get]
func (c *MonitorController) GetStats() {
	monitor := comm.GetMonitor()

	stats := map[string]interface{}{
		"system":   monitor.GetSystemStats(),
		"api":      monitor.GetAPIStats(),
		"users":    monitor.GetUserStats(),
		"top_apis": monitor.GetTopAPIs(10),
	}

	c.SendSuccessResponse(stats, "统计信息获取成功")
}

// GetAPIStats 获取API统计
// @Summary 获取API调用统计
// @Description 获取各个API接口的调用统计信息
// @Success 200 {object} models.StandardResponse
// @Router /api/Monitor/APIStats [get]
func (c *MonitorController) GetAPIStats() {
	monitor := comm.GetMonitor()
	apiStats := monitor.GetAPIStats()

	c.SendSuccessResponse(apiStats, "API统计获取成功")
}

// GetUserStats 获取用户统计
// @Summary 获取用户活动统计
// @Description 获取用户活动和使用情况统计
// @Success 200 {object} models.StandardResponse
// @Router /api/Monitor/UserStats [get]
func (c *MonitorController) GetUserStats() {
	monitor := comm.GetMonitor()
	userStats := monitor.GetUserStats()

	c.SendSuccessResponse(userStats, "用户统计获取成功")
}

// ResetStats 重置统计数据
// @Summary 重置统计数据
// @Description 清空所有统计数据，重新开始统计
// @Success 200 {object} models.StandardResponse
// @Router /api/Monitor/ResetStats [post]
func (c *MonitorController) ResetStats() {
	monitor := comm.GetMonitor()
	monitor.ResetStats()

	c.LogOperation("", "reset_stats", "管理员重置统计数据")
	c.SendSuccessResponse(nil, "统计数据重置成功")
}

// GetConfig 获取当前配置
// @Summary 获取系统配置
// @Description 获取当前系统配置信息
// @Success 200 {object} models.StandardResponse
// @Router /api/Monitor/Config [get]
func (c *MonitorController) GetConfig() {
	// 返回空配置
	safeConfig := map[string]interface{}{}

	c.SendSuccessResponse(safeConfig, "配置信息获取成功")
}

// GetSystemInfo 获取系统信息
// @Summary 获取系统信息
// @Description 获取系统架构、运行端口、运行模式、内存使用等系统信息
// @Success 200 {object} models.StandardResponse
// @Router /api/system/info [get]
func (c *MonitorController) GetSystemInfo() {
	// 获取系统架构
	architecture := fmt.Sprintf("%s %s", runtime.GOOS, runtime.GOARCH)

	// 获取运行端口
	port := beego.AppConfig.DefaultString("httpport", "8080")

	// 获取运行模式
	runMode := beego.BConfig.RunMode

	// 获取内存使用情况
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	systemMemory := fmt.Sprintf("%.2f MB", float64(memStats.Alloc)/1024/1024)

	// 获取Redis内存使用（模拟）
	redisMemory := c.getRedisMemoryUsage()

	// 获取系统运行时间
	monitor := comm.GetMonitor()
	healthStatus := monitor.GetHealthStatus()
	rawUptime := healthStatus["uptime"].(string)
	uptime := c.formatUptime(rawUptime)

	// 获取启动时间戳（毫秒）
	startTimeMs := monitor.GetStartTime().UnixNano() / int64(time.Millisecond)

	systemInfo := map[string]interface{}{
		"architecture": architecture,
		"port":         port,
		"environment":  runMode,
		"memoryUsage":  systemMemory,
		"redisMemory":  redisMemory,
		"uptime":       uptime,
		"timestamp":    time.Now().Format("2006-01-02 15:04:05"),
		"startTime":    monitor.GetStartTime().Format("2006-01-02T15:04:05Z07:00"), // ISO 8601格式
		"startTimeMs":  startTimeMs, // 毫秒时间戳，用于前端计算
	}

	c.SendSuccessResponse(systemInfo, "系统信息获取成功")
}

// getRedisMemoryUsage 获取Redis内存使用情况
func (c *MonitorController) getRedisMemoryUsage() string {
	// 尝试从Redis获取内存信息
	if comm.RedisClient != nil {
		info, err := comm.RedisClient.Info("memory").Result()
		if err == nil {
			// 解析Redis内存信息
			// 这里简化处理，实际项目中可以解析更详细的信息
			return c.parseRedisMemoryInfo(info)
		}
	}

	// 如果无法获取Redis信息，返回模拟数据
	return "64 MB"
}

// parseRedisMemoryInfo 解析Redis内存信息
func (c *MonitorController) parseRedisMemoryInfo(info string) string {
	// 分割Redis INFO输出为行
	lines := strings.Split(info, "\r\n")

	// 查找 used_memory_human 字段
	for _, line := range lines {
		if strings.HasPrefix(line, "used_memory_human:") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				return strings.TrimSpace(parts[1])
			}
		}
	}

	// 如果解析失败，返回默认值
	return "Unknown"
}

// formatUptime 格式化运行时间显示
func (c *MonitorController) formatUptime(rawUptime string) string {
	// 解析Go的Duration字符串，如 "1m46.8140356s"
	duration, err := time.ParseDuration(rawUptime)
	if err != nil {
		return rawUptime // 如果解析失败，返回原始字符串
	}

	// 转换为总秒数
	totalSeconds := int(duration.Seconds())

	// 计算天、小时、分钟、秒
	days := totalSeconds / (24 * 3600)
	hours := (totalSeconds % (24 * 3600)) / 3600
	minutes := (totalSeconds % 3600) / 60
	seconds := totalSeconds % 60

	// 根据时间长度选择合适的显示格式
	if days > 0 {
		if hours > 0 {
			return fmt.Sprintf("%d天 %d小时 %d分钟", days, hours, minutes)
		} else {
			return fmt.Sprintf("%d天 %d分钟", days, minutes)
		}
	} else if hours > 0 {
		if minutes > 0 {
			return fmt.Sprintf("%d小时 %d分钟", hours, minutes)
		} else {
			return fmt.Sprintf("%d小时", hours)
		}
	} else if minutes > 0 {
		if seconds > 0 {
			return fmt.Sprintf("%d分钟 %d秒", minutes, seconds)
		} else {
			return fmt.Sprintf("%d分钟", minutes)
		}
	} else {
		return fmt.Sprintf("%d秒", seconds)
	}
}
